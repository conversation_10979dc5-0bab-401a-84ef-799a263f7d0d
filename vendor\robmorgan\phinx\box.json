{"alias": "phinx.phar", "chmod": "0755", "compactors": ["Herrera\\Box\\Compactor\\Json", "Herrera\\Box\\Compactor\\Php"], "directories": ["src", "app"], "files": ["LICENSE", "phinx.yml"], "finder": [{"name": "*.php", "exclude": ["File", "mikey179", "Net", "phpunit", "phpunit-test-case", "Tester", "Tests", "tests"], "in": "vendor"}], "git-version": "git_tag", "main": "bin/phinx", "output": "phinx-@git-version@.phar", "stub": true}