<?php
// This file was auto-generated from sdk-root/src/data/wafv2/2019-07-29/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-07-29', 'endpointPrefix' => 'wafv2', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'WAFV2', 'serviceFullName' => 'AWS WAFV2', 'serviceId' => 'WAFV2', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSWAF_20190729', 'uid' => 'wafv2-2019-07-29', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateWebACL' => [ 'name' => 'AssociateWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateWebACLRequest', ], 'output' => [ 'shape' => 'AssociateWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFUnavailableEntityException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'CheckCapacity' => [ 'name' => 'CheckCapacity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CheckCapacityRequest', ], 'output' => [ 'shape' => 'CheckCapacityResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFInvalidResourceException', ], [ 'shape' => 'WAFUnavailableEntityException', ], [ 'shape' => 'WAFSubscriptionNotFoundException', ], [ 'shape' => 'WAFExpiredManagedRuleGroupVersionException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'CreateAPIKey' => [ 'name' => 'CreateAPIKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAPIKeyRequest', ], 'output' => [ 'shape' => 'CreateAPIKeyResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateIPSet' => [ 'name' => 'CreateIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIPSetRequest', ], 'output' => [ 'shape' => 'CreateIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'CreateRegexPatternSet' => [ 'name' => 'CreateRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegexPatternSetRequest', ], 'output' => [ 'shape' => 'CreateRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'CreateRuleGroup' => [ 'name' => 'CreateRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRuleGroupRequest', ], 'output' => [ 'shape' => 'CreateRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFUnavailableEntityException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFSubscriptionNotFoundException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'CreateWebACL' => [ 'name' => 'CreateWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWebACLRequest', ], 'output' => [ 'shape' => 'CreateWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFInvalidResourceException', ], [ 'shape' => 'WAFUnavailableEntityException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFSubscriptionNotFoundException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFConfigurationWarningException', ], [ 'shape' => 'WAFExpiredManagedRuleGroupVersionException', ], ], ], 'DeleteAPIKey' => [ 'name' => 'DeleteAPIKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAPIKeyRequest', ], 'output' => [ 'shape' => 'DeleteAPIKeyResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'DeleteFirewallManagerRuleGroups' => [ 'name' => 'DeleteFirewallManagerRuleGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFirewallManagerRuleGroupsRequest', ], 'output' => [ 'shape' => 'DeleteFirewallManagerRuleGroupsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'DeleteIPSet' => [ 'name' => 'DeleteIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIPSetRequest', ], 'output' => [ 'shape' => 'DeleteIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFAssociatedItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'DeleteLoggingConfiguration' => [ 'name' => 'DeleteLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'DeletePermissionPolicy' => [ 'name' => 'DeletePermissionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePermissionPolicyRequest', ], 'output' => [ 'shape' => 'DeletePermissionPolicyResponse', ], 'errors' => [ [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'DeleteRegexPatternSet' => [ 'name' => 'DeleteRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegexPatternSetRequest', ], 'output' => [ 'shape' => 'DeleteRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFAssociatedItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'DeleteRuleGroup' => [ 'name' => 'DeleteRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleGroupRequest', ], 'output' => [ 'shape' => 'DeleteRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFAssociatedItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'DeleteWebACL' => [ 'name' => 'DeleteWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWebACLRequest', ], 'output' => [ 'shape' => 'DeleteWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFAssociatedItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'DescribeAllManagedProducts' => [ 'name' => 'DescribeAllManagedProducts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAllManagedProductsRequest', ], 'output' => [ 'shape' => 'DescribeAllManagedProductsResponse', ], 'errors' => [ [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'DescribeManagedProductsByVendor' => [ 'name' => 'DescribeManagedProductsByVendor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeManagedProductsByVendorRequest', ], 'output' => [ 'shape' => 'DescribeManagedProductsByVendorResponse', ], 'errors' => [ [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'DescribeManagedRuleGroup' => [ 'name' => 'DescribeManagedRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeManagedRuleGroupRequest', ], 'output' => [ 'shape' => 'DescribeManagedRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidResourceException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFExpiredManagedRuleGroupVersionException', ], ], ], 'DisassociateWebACL' => [ 'name' => 'DisassociateWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateWebACLRequest', ], 'output' => [ 'shape' => 'DisassociateWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GenerateMobileSdkReleaseUrl' => [ 'name' => 'GenerateMobileSdkReleaseUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GenerateMobileSdkReleaseUrlRequest', ], 'output' => [ 'shape' => 'GenerateMobileSdkReleaseUrlResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetDecryptedAPIKey' => [ 'name' => 'GetDecryptedAPIKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDecryptedAPIKeyRequest', ], 'output' => [ 'shape' => 'GetDecryptedAPIKeyResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidResourceException', ], ], ], 'GetIPSet' => [ 'name' => 'GetIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIPSetRequest', ], 'output' => [ 'shape' => 'GetIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetLoggingConfiguration' => [ 'name' => 'GetLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'GetLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetManagedRuleSet' => [ 'name' => 'GetManagedRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetManagedRuleSetRequest', ], 'output' => [ 'shape' => 'GetManagedRuleSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetMobileSdkRelease' => [ 'name' => 'GetMobileSdkRelease', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMobileSdkReleaseRequest', ], 'output' => [ 'shape' => 'GetMobileSdkReleaseResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetPermissionPolicy' => [ 'name' => 'GetPermissionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPermissionPolicyRequest', ], 'output' => [ 'shape' => 'GetPermissionPolicyResponse', ], 'errors' => [ [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'GetRateBasedStatementManagedKeys' => [ 'name' => 'GetRateBasedStatementManagedKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRateBasedStatementManagedKeysRequest', ], 'output' => [ 'shape' => 'GetRateBasedStatementManagedKeysResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFUnsupportedAggregateKeyTypeException', ], ], ], 'GetRegexPatternSet' => [ 'name' => 'GetRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegexPatternSetRequest', ], 'output' => [ 'shape' => 'GetRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetRuleGroup' => [ 'name' => 'GetRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRuleGroupRequest', ], 'output' => [ 'shape' => 'GetRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetSampledRequests' => [ 'name' => 'GetSampledRequests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSampledRequestsRequest', ], 'output' => [ 'shape' => 'GetSampledRequestsResponse', ], 'errors' => [ [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'GetWebACL' => [ 'name' => 'GetWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWebACLRequest', ], 'output' => [ 'shape' => 'GetWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'GetWebACLForResource' => [ 'name' => 'GetWebACLForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWebACLForResourceRequest', ], 'output' => [ 'shape' => 'GetWebACLForResourceResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFUnavailableEntityException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListAPIKeys' => [ 'name' => 'ListAPIKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAPIKeysRequest', ], 'output' => [ 'shape' => 'ListAPIKeysResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidResourceException', ], ], ], 'ListAvailableManagedRuleGroupVersions' => [ 'name' => 'ListAvailableManagedRuleGroupVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAvailableManagedRuleGroupVersionsRequest', ], 'output' => [ 'shape' => 'ListAvailableManagedRuleGroupVersionsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListAvailableManagedRuleGroups' => [ 'name' => 'ListAvailableManagedRuleGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAvailableManagedRuleGroupsRequest', ], 'output' => [ 'shape' => 'ListAvailableManagedRuleGroupsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListIPSets' => [ 'name' => 'ListIPSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIPSetsRequest', ], 'output' => [ 'shape' => 'ListIPSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListLoggingConfigurations' => [ 'name' => 'ListLoggingConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLoggingConfigurationsRequest', ], 'output' => [ 'shape' => 'ListLoggingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListManagedRuleSets' => [ 'name' => 'ListManagedRuleSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListManagedRuleSetsRequest', ], 'output' => [ 'shape' => 'ListManagedRuleSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListMobileSdkReleases' => [ 'name' => 'ListMobileSdkReleases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMobileSdkReleasesRequest', ], 'output' => [ 'shape' => 'ListMobileSdkReleasesResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListRegexPatternSets' => [ 'name' => 'ListRegexPatternSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRegexPatternSetsRequest', ], 'output' => [ 'shape' => 'ListRegexPatternSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListResourcesForWebACL' => [ 'name' => 'ListResourcesForWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourcesForWebACLRequest', ], 'output' => [ 'shape' => 'ListResourcesForWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListRuleGroups' => [ 'name' => 'ListRuleGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRuleGroupsRequest', ], 'output' => [ 'shape' => 'ListRuleGroupsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'ListWebACLs' => [ 'name' => 'ListWebACLs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWebACLsRequest', ], 'output' => [ 'shape' => 'ListWebACLsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'PutLoggingConfiguration' => [ 'name' => 'PutLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'PutLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFServiceLinkedRoleErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFLogDestinationPermissionIssueException', ], ], ], 'PutManagedRuleSetVersions' => [ 'name' => 'PutManagedRuleSetVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutManagedRuleSetVersionsRequest', ], 'output' => [ 'shape' => 'PutManagedRuleSetVersionsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'PutPermissionPolicy' => [ 'name' => 'PutPermissionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutPermissionPolicyRequest', ], 'output' => [ 'shape' => 'PutPermissionPolicyResponse', ], 'errors' => [ [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidPermissionPolicyException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'UpdateIPSet' => [ 'name' => 'UpdateIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIPSetRequest', ], 'output' => [ 'shape' => 'UpdateIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'UpdateManagedRuleSetVersionExpiryDate' => [ 'name' => 'UpdateManagedRuleSetVersionExpiryDate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateManagedRuleSetVersionExpiryDateRequest', ], 'output' => [ 'shape' => 'UpdateManagedRuleSetVersionExpiryDateResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'UpdateRegexPatternSet' => [ 'name' => 'UpdateRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRegexPatternSetRequest', ], 'output' => [ 'shape' => 'UpdateRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFInvalidOperationException', ], ], ], 'UpdateRuleGroup' => [ 'name' => 'UpdateRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuleGroupRequest', ], 'output' => [ 'shape' => 'UpdateRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFUnavailableEntityException', ], [ 'shape' => 'WAFSubscriptionNotFoundException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFConfigurationWarningException', ], ], ], 'UpdateWebACL' => [ 'name' => 'UpdateWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWebACLRequest', ], 'output' => [ 'shape' => 'UpdateWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFDuplicateItemException', ], [ 'shape' => 'WAFOptimisticLockException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFInvalidResourceException', ], [ 'shape' => 'WAFUnavailableEntityException', ], [ 'shape' => 'WAFSubscriptionNotFoundException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFExpiredManagedRuleGroupVersionException', ], [ 'shape' => 'WAFConfigurationWarningException', ], ], ], ], 'shapes' => [ 'APIKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*\\S.*', ], 'APIKeySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'APIKeySummary', ], ], 'APIKeySummary' => [ 'type' => 'structure', 'members' => [ 'TokenDomains' => [ 'shape' => 'TokenDomains', ], 'APIKey' => [ 'shape' => 'APIKey', ], 'CreationTimestamp' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'APIKeyVersion', ], ], ], 'APIKeyTokenDomains' => [ 'type' => 'list', 'member' => [ 'shape' => 'TokenDomain', ], 'min' => 1, ], 'APIKeyVersion' => [ 'type' => 'integer', 'min' => 0, ], 'AWSManagedRulesACFPRuleSet' => [ 'type' => 'structure', 'required' => [ 'CreationPath', 'RegistrationPagePath', 'RequestInspection', ], 'members' => [ 'CreationPath' => [ 'shape' => 'CreationPathString', ], 'RegistrationPagePath' => [ 'shape' => 'RegistrationPagePathString', ], 'RequestInspection' => [ 'shape' => 'RequestInspectionACFP', ], 'ResponseInspection' => [ 'shape' => 'ResponseInspection', ], 'EnableRegexInPath' => [ 'shape' => 'Boolean', ], ], ], 'AWSManagedRulesATPRuleSet' => [ 'type' => 'structure', 'required' => [ 'LoginPath', ], 'members' => [ 'LoginPath' => [ 'shape' => 'String', ], 'RequestInspection' => [ 'shape' => 'RequestInspection', ], 'ResponseInspection' => [ 'shape' => 'ResponseInspection', ], 'EnableRegexInPath' => [ 'shape' => 'Boolean', ], ], ], 'AWSManagedRulesBotControlRuleSet' => [ 'type' => 'structure', 'required' => [ 'InspectionLevel', ], 'members' => [ 'InspectionLevel' => [ 'shape' => 'InspectionLevel', ], 'EnableMachineLearning' => [ 'shape' => 'EnableMachineLearning', ], ], ], 'Action' => [ 'type' => 'string', ], 'ActionCondition' => [ 'type' => 'structure', 'required' => [ 'Action', ], 'members' => [ 'Action' => [ 'shape' => 'ActionValue', ], ], ], 'ActionValue' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'BLOCK', 'COUNT', 'CAPTCHA', 'CHALLENGE', 'EXCLUDED_AS_COUNT', ], ], 'AddressField' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'FieldIdentifier', ], ], ], 'AddressFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddressField', ], ], 'All' => [ 'type' => 'structure', 'members' => [], ], 'AllQueryArguments' => [ 'type' => 'structure', 'members' => [], ], 'AllowAction' => [ 'type' => 'structure', 'members' => [ 'CustomRequestHandling' => [ 'shape' => 'CustomRequestHandling', ], ], ], 'AndStatement' => [ 'type' => 'structure', 'required' => [ 'Statements', ], 'members' => [ 'Statements' => [ 'shape' => 'Statements', ], ], ], 'AssociateWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLArn', 'ResourceArn', ], 'members' => [ 'WebACLArn' => [ 'shape' => 'ResourceArn', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'AssociateWebACLResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociatedResourceType' => [ 'type' => 'string', 'enum' => [ 'CLOUDFRONT', 'API_GATEWAY', 'COGNITO_USER_POOL', 'APP_RUNNER_SERVICE', 'VERIFIED_ACCESS_INSTANCE', ], ], 'AssociationConfig' => [ 'type' => 'structure', 'members' => [ 'RequestBody' => [ 'shape' => 'RequestBody', ], ], ], 'BlockAction' => [ 'type' => 'structure', 'members' => [ 'CustomResponse' => [ 'shape' => 'CustomResponse', ], ], ], 'Body' => [ 'type' => 'structure', 'members' => [ 'OversizeHandling' => [ 'shape' => 'OversizeHandling', ], ], ], 'BodyParsingFallbackBehavior' => [ 'type' => 'string', 'enum' => [ 'MATCH', 'NO_MATCH', 'EVALUATE_AS_STRING', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'ByteMatchStatement' => [ 'type' => 'structure', 'required' => [ 'SearchString', 'FieldToMatch', 'TextTransformations', 'PositionalConstraint', ], 'members' => [ 'SearchString' => [ 'shape' => 'SearchString', ], 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], 'PositionalConstraint' => [ 'shape' => 'PositionalConstraint', ], ], ], 'CapacityUnit' => [ 'type' => 'long', 'min' => 1, ], 'CaptchaAction' => [ 'type' => 'structure', 'members' => [ 'CustomRequestHandling' => [ 'shape' => 'CustomRequestHandling', ], ], ], 'CaptchaConfig' => [ 'type' => 'structure', 'members' => [ 'ImmunityTimeProperty' => [ 'shape' => 'ImmunityTimeProperty', ], ], ], 'CaptchaResponse' => [ 'type' => 'structure', 'members' => [ 'ResponseCode' => [ 'shape' => 'ResponseCode', ], 'SolveTimestamp' => [ 'shape' => 'SolveTimestamp', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'ChallengeAction' => [ 'type' => 'structure', 'members' => [ 'CustomRequestHandling' => [ 'shape' => 'CustomRequestHandling', ], ], ], 'ChallengeConfig' => [ 'type' => 'structure', 'members' => [ 'ImmunityTimeProperty' => [ 'shape' => 'ImmunityTimeProperty', ], ], ], 'ChallengeResponse' => [ 'type' => 'structure', 'members' => [ 'ResponseCode' => [ 'shape' => 'ResponseCode', ], 'SolveTimestamp' => [ 'shape' => 'SolveTimestamp', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'CheckCapacityRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', 'Rules', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'Rules' => [ 'shape' => 'Rules', ], ], ], 'CheckCapacityResponse' => [ 'type' => 'structure', 'members' => [ 'Capacity' => [ 'shape' => 'ConsumedCapacity', ], ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'NE', 'LE', 'LT', 'GE', 'GT', ], ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'ActionCondition' => [ 'shape' => 'ActionCondition', ], 'LabelNameCondition' => [ 'shape' => 'LabelNameCondition', ], ], ], 'Conditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Condition', ], 'min' => 1, ], 'ConsumedCapacity' => [ 'type' => 'long', 'min' => 0, ], 'CookieMatchPattern' => [ 'type' => 'structure', 'members' => [ 'All' => [ 'shape' => 'All', ], 'IncludedCookies' => [ 'shape' => 'CookieNames', ], 'ExcludedCookies' => [ 'shape' => 'CookieNames', ], ], ], 'CookieNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'SingleCookieName', ], 'max' => 199, 'min' => 1, ], 'Cookies' => [ 'type' => 'structure', 'required' => [ 'MatchPattern', 'MatchScope', 'OversizeHandling', ], 'members' => [ 'MatchPattern' => [ 'shape' => 'CookieMatchPattern', ], 'MatchScope' => [ 'shape' => 'MapMatchScope', ], 'OversizeHandling' => [ 'shape' => 'OversizeHandling', ], ], ], 'CountAction' => [ 'type' => 'structure', 'members' => [ 'CustomRequestHandling' => [ 'shape' => 'CustomRequestHandling', ], ], ], 'Country' => [ 'type' => 'string', ], 'CountryCode' => [ 'type' => 'string', 'enum' => [ 'AF', 'AX', 'AL', 'DZ', 'AS', 'AD', 'AO', 'AI', 'AQ', 'AG', 'AR', 'AM', 'AW', 'AU', 'AT', 'AZ', 'BS', 'BH', 'BD', 'BB', 'BY', 'BE', 'BZ', 'BJ', 'BM', 'BT', 'BO', 'BQ', 'BA', 'BW', 'BV', 'BR', 'IO', 'BN', 'BG', 'BF', 'BI', 'KH', 'CM', 'CA', 'CV', 'KY', 'CF', 'TD', 'CL', 'CN', 'CX', 'CC', 'CO', 'KM', 'CG', 'CD', 'CK', 'CR', 'CI', 'HR', 'CU', 'CW', 'CY', 'CZ', 'DK', 'DJ', 'DM', 'DO', 'EC', 'EG', 'SV', 'GQ', 'ER', 'EE', 'ET', 'FK', 'FO', 'FJ', 'FI', 'FR', 'GF', 'PF', 'TF', 'GA', 'GM', 'GE', 'DE', 'GH', 'GI', 'GR', 'GL', 'GD', 'GP', 'GU', 'GT', 'GG', 'GN', 'GW', 'GY', 'HT', 'HM', 'VA', 'HN', 'HK', 'HU', 'IS', 'IN', 'ID', 'IR', 'IQ', 'IE', 'IM', 'IL', 'IT', 'JM', 'JP', 'JE', 'JO', 'KZ', 'KE', 'KI', 'KP', 'KR', 'KW', 'KG', 'LA', 'LV', 'LB', 'LS', 'LR', 'LY', 'LI', 'LT', 'LU', 'MO', 'MK', 'MG', 'MW', 'MY', 'MV', 'ML', 'MT', 'MH', 'MQ', 'MR', 'MU', 'YT', 'MX', 'FM', 'MD', 'MC', 'MN', 'ME', 'MS', 'MA', 'MZ', 'MM', 'NA', 'NR', 'NP', 'NL', 'NC', 'NZ', 'NI', 'NE', 'NG', 'NU', 'NF', 'MP', 'NO', 'OM', 'PK', 'PW', 'PS', 'PA', 'PG', 'PY', 'PE', 'PH', 'PN', 'PL', 'PT', 'PR', 'QA', 'RE', 'RO', 'RU', 'RW', 'BL', 'SH', 'KN', 'LC', 'MF', 'PM', 'VC', 'WS', 'SM', 'ST', 'SA', 'SN', 'RS', 'SC', 'SL', 'SG', 'SX', 'SK', 'SI', 'SB', 'SO', 'ZA', 'GS', 'SS', 'ES', 'LK', 'SD', 'SR', 'SJ', 'SZ', 'SE', 'CH', 'SY', 'TW', 'TJ', 'TZ', 'TH', 'TL', 'TG', 'TK', 'TO', 'TT', 'TN', 'TR', 'TM', 'TC', 'TV', 'UG', 'UA', 'AE', 'GB', 'US', 'UM', 'UY', 'UZ', 'VU', 'VE', 'VN', 'VG', 'VI', 'WF', 'EH', 'YE', 'ZM', 'ZW', 'XK', ], ], 'CountryCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CountryCode', ], 'min' => 1, ], 'CreateAPIKeyRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', 'TokenDomains', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'TokenDomains' => [ 'shape' => 'APIKeyTokenDomains', ], ], ], 'CreateAPIKeyResponse' => [ 'type' => 'structure', 'members' => [ 'APIKey' => [ 'shape' => 'APIKey', ], ], ], 'CreateIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'IPAddressVersion', 'Addresses', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'IPAddressVersion' => [ 'shape' => 'IPAddressVersion', ], 'Addresses' => [ 'shape' => 'IPAddresses', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateIPSetResponse' => [ 'type' => 'structure', 'members' => [ 'Summary' => [ 'shape' => 'IPSetSummary', ], ], ], 'CreateRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'RegularExpressionList', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'RegularExpressionList' => [ 'shape' => 'RegularExpressionList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [ 'Summary' => [ 'shape' => 'RegexPatternSetSummary', ], ], ], 'CreateRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Capacity', 'VisibilityConfig', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Capacity' => [ 'shape' => 'CapacityUnit', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Rules' => [ 'shape' => 'Rules', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], 'Tags' => [ 'shape' => 'TagList', ], 'CustomResponseBodies' => [ 'shape' => 'CustomResponseBodies', ], ], ], 'CreateRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Summary' => [ 'shape' => 'RuleGroupSummary', ], ], ], 'CreateWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'DefaultAction', 'VisibilityConfig', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'DefaultAction' => [ 'shape' => 'DefaultAction', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Rules' => [ 'shape' => 'Rules', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], 'Tags' => [ 'shape' => 'TagList', ], 'CustomResponseBodies' => [ 'shape' => 'CustomResponseBodies', ], 'CaptchaConfig' => [ 'shape' => 'CaptchaConfig', ], 'ChallengeConfig' => [ 'shape' => 'ChallengeConfig', ], 'TokenDomains' => [ 'shape' => 'TokenDomains', ], 'AssociationConfig' => [ 'shape' => 'AssociationConfig', ], ], ], 'CreateWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'Summary' => [ 'shape' => 'WebACLSummary', ], ], ], 'CreationPathString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'CustomHTTPHeader' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'CustomHTTPHeaderName', ], 'Value' => [ 'shape' => 'CustomHTTPHeaderValue', ], ], ], 'CustomHTTPHeaderName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._$-]+$', ], 'CustomHTTPHeaderValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*', ], 'CustomHTTPHeaders' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomHTTPHeader', ], 'min' => 1, ], 'CustomRequestHandling' => [ 'type' => 'structure', 'required' => [ 'InsertHeaders', ], 'members' => [ 'InsertHeaders' => [ 'shape' => 'CustomHTTPHeaders', ], ], ], 'CustomResponse' => [ 'type' => 'structure', 'required' => [ 'ResponseCode', ], 'members' => [ 'ResponseCode' => [ 'shape' => 'ResponseStatusCode', ], 'CustomResponseBodyKey' => [ 'shape' => 'EntityName', ], 'ResponseHeaders' => [ 'shape' => 'CustomHTTPHeaders', ], ], ], 'CustomResponseBodies' => [ 'type' => 'map', 'key' => [ 'shape' => 'EntityName', ], 'value' => [ 'shape' => 'CustomResponseBody', ], 'min' => 1, ], 'CustomResponseBody' => [ 'type' => 'structure', 'required' => [ 'ContentType', 'Content', ], 'members' => [ 'ContentType' => [ 'shape' => 'ResponseContentType', ], 'Content' => [ 'shape' => 'ResponseContent', ], ], ], 'DefaultAction' => [ 'type' => 'structure', 'members' => [ 'Block' => [ 'shape' => 'BlockAction', ], 'Allow' => [ 'shape' => 'AllowAction', ], ], ], 'DeleteAPIKeyRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', 'APIKey', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'APIKey' => [ 'shape' => 'APIKey', ], ], ], 'DeleteAPIKeyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFirewallManagerRuleGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLArn', 'WebACLLockToken', ], 'members' => [ 'WebACLArn' => [ 'shape' => 'ResourceArn', ], 'WebACLLockToken' => [ 'shape' => 'LockToken', ], ], ], 'DeleteFirewallManagerRuleGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextWebACLLockToken' => [ 'shape' => 'LockToken', ], ], ], 'DeleteIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'DeleteIPSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'LogType' => [ 'shape' => 'LogType', ], 'LogScope' => [ 'shape' => 'LogScope', ], ], ], 'DeleteLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePermissionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeletePermissionPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'DeleteRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'DeleteRuleGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'DeleteWebACLResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAllManagedProductsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], ], ], 'DescribeAllManagedProductsResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedProducts' => [ 'shape' => 'ManagedProductDescriptors', ], ], ], 'DescribeManagedProductsByVendorRequest' => [ 'type' => 'structure', 'required' => [ 'VendorName', 'Scope', ], 'members' => [ 'VendorName' => [ 'shape' => 'VendorName', ], 'Scope' => [ 'shape' => 'Scope', ], ], ], 'DescribeManagedProductsByVendorResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedProducts' => [ 'shape' => 'ManagedProductDescriptors', ], ], ], 'DescribeManagedRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'VendorName', 'Name', 'Scope', ], 'members' => [ 'VendorName' => [ 'shape' => 'VendorName', ], 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'VersionName' => [ 'shape' => 'VersionKeyString', ], ], ], 'DescribeManagedRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'VersionName' => [ 'shape' => 'VersionKeyString', ], 'SnsTopicArn' => [ 'shape' => 'ResourceArn', ], 'Capacity' => [ 'shape' => 'CapacityUnit', ], 'Rules' => [ 'shape' => 'RuleSummaries', ], 'LabelNamespace' => [ 'shape' => 'LabelName', ], 'AvailableLabels' => [ 'shape' => 'LabelSummaries', ], 'ConsumedLabels' => [ 'shape' => 'LabelSummaries', ], ], ], 'DisassociateWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DisassociateWebACLResponse' => [ 'type' => 'structure', 'members' => [], ], 'DownloadUrl' => [ 'type' => 'string', ], 'EmailField' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'FieldIdentifier', ], ], ], 'EnableMachineLearning' => [ 'type' => 'boolean', ], 'EntityDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[\\w+=:#@/\\-,\\.][\\w+=:#@/\\-,\\.\\s]+[\\w+=:#@/\\-,\\.]$', ], 'EntityId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$', ], 'EntityName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\w\\-]+$', ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorReason' => [ 'type' => 'string', ], 'EvaluationWindowSec' => [ 'type' => 'long', ], 'ExcludedRule' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], ], ], 'ExcludedRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExcludedRule', ], 'max' => 100, ], 'FailureCode' => [ 'type' => 'integer', 'max' => 999, 'min' => 0, ], 'FailureReason' => [ 'type' => 'string', 'enum' => [ 'TOKEN_MISSING', 'TOKEN_EXPIRED', 'TOKEN_INVALID', 'TOKEN_DOMAIN_MISMATCH', ], ], 'FailureValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '.*\\S.*', ], 'FallbackBehavior' => [ 'type' => 'string', 'enum' => [ 'MATCH', 'NO_MATCH', ], ], 'FieldIdentifier' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*\\S.*', ], 'FieldToMatch' => [ 'type' => 'structure', 'members' => [ 'SingleHeader' => [ 'shape' => 'SingleHeader', ], 'SingleQueryArgument' => [ 'shape' => 'SingleQueryArgument', ], 'AllQueryArguments' => [ 'shape' => 'AllQueryArguments', ], 'UriPath' => [ 'shape' => 'UriPath', ], 'QueryString' => [ 'shape' => 'QueryString', ], 'Body' => [ 'shape' => 'Body', ], 'Method' => [ 'shape' => 'Method', ], 'JsonBody' => [ 'shape' => 'JsonBody', ], 'Headers' => [ 'shape' => 'Headers', ], 'Cookies' => [ 'shape' => 'Cookies', ], 'HeaderOrder' => [ 'shape' => 'HeaderOrder', ], 'JA3Fingerprint' => [ 'shape' => 'JA3Fingerprint', ], ], ], 'FieldToMatchData' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Behavior', 'Requirement', 'Conditions', ], 'members' => [ 'Behavior' => [ 'shape' => 'FilterBehavior', ], 'Requirement' => [ 'shape' => 'FilterRequirement', ], 'Conditions' => [ 'shape' => 'Conditions', ], ], ], 'FilterBehavior' => [ 'type' => 'string', 'enum' => [ 'KEEP', 'DROP', ], ], 'FilterRequirement' => [ 'type' => 'string', 'enum' => [ 'MEETS_ALL', 'MEETS_ANY', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'min' => 1, ], 'FirewallManagerRuleGroup' => [ 'type' => 'structure', 'required' => [ 'Name', 'Priority', 'FirewallManagerStatement', 'OverrideAction', 'VisibilityConfig', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Priority' => [ 'shape' => 'RulePriority', ], 'FirewallManagerStatement' => [ 'shape' => 'FirewallManagerStatement', ], 'OverrideAction' => [ 'shape' => 'OverrideAction', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], ], ], 'FirewallManagerRuleGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallManagerRuleGroup', ], ], 'FirewallManagerStatement' => [ 'type' => 'structure', 'members' => [ 'ManagedRuleGroupStatement' => [ 'shape' => 'ManagedRuleGroupStatement', ], 'RuleGroupReferenceStatement' => [ 'shape' => 'RuleGroupReferenceStatement', ], ], ], 'ForwardedIPConfig' => [ 'type' => 'structure', 'required' => [ 'HeaderName', 'FallbackBehavior', ], 'members' => [ 'HeaderName' => [ 'shape' => 'ForwardedIPHeaderName', ], 'FallbackBehavior' => [ 'shape' => 'FallbackBehavior', ], ], ], 'ForwardedIPHeaderName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'ForwardedIPPosition' => [ 'type' => 'string', 'enum' => [ 'FIRST', 'LAST', 'ANY', ], ], 'GenerateMobileSdkReleaseUrlRequest' => [ 'type' => 'structure', 'required' => [ 'Platform', 'ReleaseVersion', ], 'members' => [ 'Platform' => [ 'shape' => 'Platform', ], 'ReleaseVersion' => [ 'shape' => 'VersionKeyString', ], ], ], 'GenerateMobileSdkReleaseUrlResponse' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'DownloadUrl', ], ], ], 'GeoMatchStatement' => [ 'type' => 'structure', 'members' => [ 'CountryCodes' => [ 'shape' => 'CountryCodes', ], 'ForwardedIPConfig' => [ 'shape' => 'ForwardedIPConfig', ], ], ], 'GetDecryptedAPIKeyRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', 'APIKey', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'APIKey' => [ 'shape' => 'APIKey', ], ], ], 'GetDecryptedAPIKeyResponse' => [ 'type' => 'structure', 'members' => [ 'TokenDomains' => [ 'shape' => 'TokenDomains', ], 'CreationTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], ], ], 'GetIPSetResponse' => [ 'type' => 'structure', 'members' => [ 'IPSet' => [ 'shape' => 'IPSet', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'GetLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'LogType' => [ 'shape' => 'LogType', ], 'LogScope' => [ 'shape' => 'LogScope', ], ], ], 'GetLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'GetManagedRuleSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], ], ], 'GetManagedRuleSetResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedRuleSet' => [ 'shape' => 'ManagedRuleSet', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'GetMobileSdkReleaseRequest' => [ 'type' => 'structure', 'required' => [ 'Platform', 'ReleaseVersion', ], 'members' => [ 'Platform' => [ 'shape' => 'Platform', ], 'ReleaseVersion' => [ 'shape' => 'VersionKeyString', ], ], ], 'GetMobileSdkReleaseResponse' => [ 'type' => 'structure', 'members' => [ 'MobileSdkRelease' => [ 'shape' => 'MobileSdkRelease', ], ], ], 'GetPermissionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetPermissionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'PolicyString', ], ], ], 'GetRateBasedStatementManagedKeysRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', 'WebACLName', 'WebACLId', 'RuleName', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'WebACLName' => [ 'shape' => 'EntityName', ], 'WebACLId' => [ 'shape' => 'EntityId', ], 'RuleGroupRuleName' => [ 'shape' => 'EntityName', ], 'RuleName' => [ 'shape' => 'EntityName', ], ], ], 'GetRateBasedStatementManagedKeysResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedKeysIPV4' => [ 'shape' => 'RateBasedStatementManagedKeysIPSet', ], 'ManagedKeysIPV6' => [ 'shape' => 'RateBasedStatementManagedKeysIPSet', ], ], ], 'GetRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], ], ], 'GetRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [ 'RegexPatternSet' => [ 'shape' => 'RegexPatternSet', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'GetRuleGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'ARN' => [ 'shape' => 'ResourceArn', ], ], ], 'GetRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'RuleGroup' => [ 'shape' => 'RuleGroup', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'GetSampledRequestsRequest' => [ 'type' => 'structure', 'required' => [ 'WebAclArn', 'RuleMetricName', 'Scope', 'TimeWindow', 'MaxItems', ], 'members' => [ 'WebAclArn' => [ 'shape' => 'ResourceArn', ], 'RuleMetricName' => [ 'shape' => 'MetricName', ], 'Scope' => [ 'shape' => 'Scope', ], 'TimeWindow' => [ 'shape' => 'TimeWindow', ], 'MaxItems' => [ 'shape' => 'ListMaxItems', ], ], ], 'GetSampledRequestsResponse' => [ 'type' => 'structure', 'members' => [ 'SampledRequests' => [ 'shape' => 'SampledHTTPRequests', ], 'PopulationSize' => [ 'shape' => 'PopulationSize', ], 'TimeWindow' => [ 'shape' => 'TimeWindow', ], ], ], 'GetWebACLForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetWebACLForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'WebACL' => [ 'shape' => 'WebACL', ], ], ], 'GetWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], ], ], 'GetWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'WebACL' => [ 'shape' => 'WebACL', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'ApplicationIntegrationURL' => [ 'shape' => 'OutputUrl', ], ], ], 'HTTPHeader' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'HeaderName', ], 'Value' => [ 'shape' => 'HeaderValue', ], ], ], 'HTTPHeaders' => [ 'type' => 'list', 'member' => [ 'shape' => 'HTTPHeader', ], ], 'HTTPMethod' => [ 'type' => 'string', ], 'HTTPRequest' => [ 'type' => 'structure', 'members' => [ 'ClientIP' => [ 'shape' => 'IPString', ], 'Country' => [ 'shape' => 'Country', ], 'URI' => [ 'shape' => 'URIString', ], 'Method' => [ 'shape' => 'HTTPMethod', ], 'HTTPVersion' => [ 'shape' => 'HTTPVersion', ], 'Headers' => [ 'shape' => 'HTTPHeaders', ], ], ], 'HTTPVersion' => [ 'type' => 'string', ], 'HeaderMatchPattern' => [ 'type' => 'structure', 'members' => [ 'All' => [ 'shape' => 'All', ], 'IncludedHeaders' => [ 'shape' => 'HeaderNames', ], 'ExcludedHeaders' => [ 'shape' => 'HeaderNames', ], ], ], 'HeaderName' => [ 'type' => 'string', ], 'HeaderNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldToMatchData', ], 'max' => 199, 'min' => 1, ], 'HeaderOrder' => [ 'type' => 'structure', 'required' => [ 'OversizeHandling', ], 'members' => [ 'OversizeHandling' => [ 'shape' => 'OversizeHandling', ], ], ], 'HeaderValue' => [ 'type' => 'string', ], 'Headers' => [ 'type' => 'structure', 'required' => [ 'MatchPattern', 'MatchScope', 'OversizeHandling', ], 'members' => [ 'MatchPattern' => [ 'shape' => 'HeaderMatchPattern', ], 'MatchScope' => [ 'shape' => 'MapMatchScope', ], 'OversizeHandling' => [ 'shape' => 'OversizeHandling', ], ], ], 'IPAddress' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '.*\\S.*', ], 'IPAddressVersion' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'IPV6', ], ], 'IPAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPAddress', ], ], 'IPSet' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'ARN', 'IPAddressVersion', 'Addresses', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'ARN' => [ 'shape' => 'ResourceArn', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'IPAddressVersion' => [ 'shape' => 'IPAddressVersion', ], 'Addresses' => [ 'shape' => 'IPAddresses', ], ], ], 'IPSetForwardedIPConfig' => [ 'type' => 'structure', 'required' => [ 'HeaderName', 'FallbackBehavior', 'Position', ], 'members' => [ 'HeaderName' => [ 'shape' => 'ForwardedIPHeaderName', ], 'FallbackBehavior' => [ 'shape' => 'FallbackBehavior', ], 'Position' => [ 'shape' => 'ForwardedIPPosition', ], ], ], 'IPSetReferenceStatement' => [ 'type' => 'structure', 'required' => [ 'ARN', ], 'members' => [ 'ARN' => [ 'shape' => 'ResourceArn', ], 'IPSetForwardedIPConfig' => [ 'shape' => 'IPSetForwardedIPConfig', ], ], ], 'IPSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPSetSummary', ], ], 'IPSetSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'ARN' => [ 'shape' => 'ResourceArn', ], ], ], 'IPString' => [ 'type' => 'string', ], 'ImmunityTimeProperty' => [ 'type' => 'structure', 'required' => [ 'ImmunityTime', ], 'members' => [ 'ImmunityTime' => [ 'shape' => 'TimeWindowSecond', ], ], ], 'InspectionLevel' => [ 'type' => 'string', 'enum' => [ 'COMMON', 'TARGETED', ], ], 'JA3Fingerprint' => [ 'type' => 'structure', 'required' => [ 'FallbackBehavior', ], 'members' => [ 'FallbackBehavior' => [ 'shape' => 'FallbackBehavior', ], ], ], 'JsonBody' => [ 'type' => 'structure', 'required' => [ 'MatchPattern', 'MatchScope', ], 'members' => [ 'MatchPattern' => [ 'shape' => 'JsonMatchPattern', ], 'MatchScope' => [ 'shape' => 'JsonMatchScope', ], 'InvalidFallbackBehavior' => [ 'shape' => 'BodyParsingFallbackBehavior', ], 'OversizeHandling' => [ 'shape' => 'OversizeHandling', ], ], ], 'JsonMatchPattern' => [ 'type' => 'structure', 'members' => [ 'All' => [ 'shape' => 'All', ], 'IncludedPaths' => [ 'shape' => 'JsonPointerPaths', ], ], ], 'JsonMatchScope' => [ 'type' => 'string', 'enum' => [ 'ALL', 'KEY', 'VALUE', ], ], 'JsonPointerPath' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '([/])|([/](([^~])|(~[01]))+)', ], 'JsonPointerPaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'JsonPointerPath', ], 'min' => 1, ], 'Label' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'LabelName', ], ], ], 'LabelMatchKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[0-9A-Za-z_\\-:]+$', ], 'LabelMatchScope' => [ 'type' => 'string', 'enum' => [ 'LABEL', 'NAMESPACE', ], ], 'LabelMatchStatement' => [ 'type' => 'structure', 'required' => [ 'Scope', 'Key', ], 'members' => [ 'Scope' => [ 'shape' => 'LabelMatchScope', ], 'Key' => [ 'shape' => 'LabelMatchKey', ], ], ], 'LabelName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[0-9A-Za-z_\\-:]+$', ], 'LabelNameCondition' => [ 'type' => 'structure', 'required' => [ 'LabelName', ], 'members' => [ 'LabelName' => [ 'shape' => 'LabelName', ], ], ], 'LabelNamespace' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[0-9A-Za-z_\\-:]+:$', ], 'LabelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LabelSummary', ], ], 'LabelSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'LabelName', ], ], ], 'Labels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Label', ], ], 'ListAPIKeysRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListAPIKeysResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'APIKeySummaries' => [ 'shape' => 'APIKeySummaries', ], 'ApplicationIntegrationURL' => [ 'shape' => 'OutputUrl', ], ], ], 'ListAvailableManagedRuleGroupVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'VendorName', 'Name', 'Scope', ], 'members' => [ 'VendorName' => [ 'shape' => 'VendorName', ], 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListAvailableManagedRuleGroupVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Versions' => [ 'shape' => 'ManagedRuleGroupVersions', ], 'CurrentDefaultVersion' => [ 'shape' => 'VersionKeyString', ], ], ], 'ListAvailableManagedRuleGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListAvailableManagedRuleGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'ManagedRuleGroups' => [ 'shape' => 'ManagedRuleGroupSummaries', ], ], ], 'ListIPSetsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListIPSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'IPSets' => [ 'shape' => 'IPSetSummaries', ], ], ], 'ListLoggingConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], 'LogScope' => [ 'shape' => 'LogScope', ], ], ], 'ListLoggingConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfigurations' => [ 'shape' => 'LoggingConfigurations', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], ], ], 'ListManagedRuleSetsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListManagedRuleSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'ManagedRuleSets' => [ 'shape' => 'ManagedRuleSetSummaries', ], ], ], 'ListMaxItems' => [ 'type' => 'long', 'max' => 500, 'min' => 1, ], 'ListMobileSdkReleasesRequest' => [ 'type' => 'structure', 'required' => [ 'Platform', ], 'members' => [ 'Platform' => [ 'shape' => 'Platform', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListMobileSdkReleasesResponse' => [ 'type' => 'structure', 'members' => [ 'ReleaseSummaries' => [ 'shape' => 'ReleaseSummaries', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], ], ], 'ListRegexPatternSetsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListRegexPatternSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'RegexPatternSets' => [ 'shape' => 'RegexPatternSetSummaries', ], ], ], 'ListResourcesForWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLArn', ], 'members' => [ 'WebACLArn' => [ 'shape' => 'ResourceArn', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'ListResourcesForWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArns' => [ 'shape' => 'ResourceArns', ], ], ], 'ListRuleGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListRuleGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'RuleGroups' => [ 'shape' => 'RuleGroupSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], 'ResourceARN' => [ 'shape' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'TagInfoForResource' => [ 'shape' => 'TagInfoForResource', ], ], ], 'ListWebACLsRequest' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'Scope' => [ 'shape' => 'Scope', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListWebACLsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'WebACLs' => [ 'shape' => 'WebACLSummaries', ], ], ], 'LockToken' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$', ], 'LogDestinationConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], 'max' => 100, 'min' => 1, ], 'LogScope' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER', 'SECURITY_LAKE', ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'WAF_LOGS', ], ], 'LoggingConfiguration' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'LogDestinationConfigs', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'LogDestinationConfigs' => [ 'shape' => 'LogDestinationConfigs', ], 'RedactedFields' => [ 'shape' => 'RedactedFields', ], 'ManagedByFirewallManager' => [ 'shape' => 'Boolean', ], 'LoggingFilter' => [ 'shape' => 'LoggingFilter', ], 'LogType' => [ 'shape' => 'LogType', ], 'LogScope' => [ 'shape' => 'LogScope', ], ], ], 'LoggingConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoggingConfiguration', ], ], 'LoggingFilter' => [ 'type' => 'structure', 'required' => [ 'Filters', 'DefaultBehavior', ], 'members' => [ 'Filters' => [ 'shape' => 'Filters', ], 'DefaultBehavior' => [ 'shape' => 'FilterBehavior', ], ], ], 'LoginPathString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ManagedProductDescriptor' => [ 'type' => 'structure', 'members' => [ 'VendorName' => [ 'shape' => 'VendorName', ], 'ManagedRuleSetName' => [ 'shape' => 'EntityName', ], 'ProductId' => [ 'shape' => 'ProductId', ], 'ProductLink' => [ 'shape' => 'ProductLink', ], 'ProductTitle' => [ 'shape' => 'ProductTitle', ], 'ProductDescription' => [ 'shape' => 'ProductDescription', ], 'SnsTopicArn' => [ 'shape' => 'ResourceArn', ], 'IsVersioningSupported' => [ 'shape' => 'Boolean', ], 'IsAdvancedManagedRuleSet' => [ 'shape' => 'Boolean', ], ], ], 'ManagedProductDescriptors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedProductDescriptor', ], ], 'ManagedRuleGroupConfig' => [ 'type' => 'structure', 'members' => [ 'LoginPath' => [ 'shape' => 'LoginPathString', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Use AWSManagedRulesATPRuleSet LoginPath', ], 'PayloadType' => [ 'shape' => 'PayloadType', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Use AWSManagedRulesATPRuleSet RequestInspection PayloadType', ], 'UsernameField' => [ 'shape' => 'UsernameField', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Use AWSManagedRulesATPRuleSet RequestInspection UsernameField', ], 'PasswordField' => [ 'shape' => 'PasswordField', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Use AWSManagedRulesATPRuleSet RequestInspection PasswordField', ], 'AWSManagedRulesBotControlRuleSet' => [ 'shape' => 'AWSManagedRulesBotControlRuleSet', ], 'AWSManagedRulesATPRuleSet' => [ 'shape' => 'AWSManagedRulesATPRuleSet', ], 'AWSManagedRulesACFPRuleSet' => [ 'shape' => 'AWSManagedRulesACFPRuleSet', ], ], ], 'ManagedRuleGroupConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedRuleGroupConfig', ], ], 'ManagedRuleGroupStatement' => [ 'type' => 'structure', 'required' => [ 'VendorName', 'Name', ], 'members' => [ 'VendorName' => [ 'shape' => 'VendorName', ], 'Name' => [ 'shape' => 'EntityName', ], 'Version' => [ 'shape' => 'VersionKeyString', ], 'ExcludedRules' => [ 'shape' => 'ExcludedRules', ], 'ScopeDownStatement' => [ 'shape' => 'Statement', ], 'ManagedRuleGroupConfigs' => [ 'shape' => 'ManagedRuleGroupConfigs', ], 'RuleActionOverrides' => [ 'shape' => 'RuleActionOverrides', ], ], ], 'ManagedRuleGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedRuleGroupSummary', ], ], 'ManagedRuleGroupSummary' => [ 'type' => 'structure', 'members' => [ 'VendorName' => [ 'shape' => 'VendorName', ], 'Name' => [ 'shape' => 'EntityName', ], 'VersioningSupported' => [ 'shape' => 'Boolean', ], 'Description' => [ 'shape' => 'EntityDescription', ], ], ], 'ManagedRuleGroupVersion' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'VersionKeyString', ], 'LastUpdateTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ManagedRuleGroupVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedRuleGroupVersion', ], ], 'ManagedRuleSet' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'ARN', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'ARN' => [ 'shape' => 'ResourceArn', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'PublishedVersions' => [ 'shape' => 'PublishedVersions', ], 'RecommendedVersion' => [ 'shape' => 'VersionKeyString', ], 'LabelNamespace' => [ 'shape' => 'LabelName', ], ], ], 'ManagedRuleSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedRuleSetSummary', ], ], 'ManagedRuleSetSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'ARN' => [ 'shape' => 'ResourceArn', ], 'LabelNamespace' => [ 'shape' => 'LabelName', ], ], ], 'ManagedRuleSetVersion' => [ 'type' => 'structure', 'members' => [ 'AssociatedRuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'Capacity' => [ 'shape' => 'CapacityUnit', ], 'ForecastedLifetime' => [ 'shape' => 'TimeWindowDay', ], 'PublishTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdateTimestamp' => [ 'shape' => 'Timestamp', ], 'ExpiryTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'MapMatchScope' => [ 'type' => 'string', 'enum' => [ 'ALL', 'KEY', 'VALUE', ], ], 'Method' => [ 'type' => 'structure', 'members' => [], ], 'MetricName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[\\w#:\\.\\-/]+$', ], 'MobileSdkRelease' => [ 'type' => 'structure', 'members' => [ 'ReleaseVersion' => [ 'shape' => 'VersionKeyString', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ReleaseNotes' => [ 'shape' => 'ReleaseNotes', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'NextMarker' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'NoneAction' => [ 'type' => 'structure', 'members' => [], ], 'NotStatement' => [ 'type' => 'structure', 'required' => [ 'Statement', ], 'members' => [ 'Statement' => [ 'shape' => 'Statement', ], ], ], 'OrStatement' => [ 'type' => 'structure', 'required' => [ 'Statements', ], 'members' => [ 'Statements' => [ 'shape' => 'Statements', ], ], ], 'OutputUrl' => [ 'type' => 'string', ], 'OverrideAction' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => 'CountAction', ], 'None' => [ 'shape' => 'NoneAction', ], ], ], 'OversizeHandling' => [ 'type' => 'string', 'enum' => [ 'CONTINUE', 'MATCH', 'NO_MATCH', ], ], 'PaginationLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ParameterExceptionField' => [ 'type' => 'string', 'enum' => [ 'WEB_ACL', 'RULE_GROUP', 'REGEX_PATTERN_SET', 'IP_SET', 'MANAGED_RULE_SET', 'RULE', 'EXCLUDED_RULE', 'STATEMENT', 'BYTE_MATCH_STATEMENT', 'SQLI_MATCH_STATEMENT', 'XSS_MATCH_STATEMENT', 'SIZE_CONSTRAINT_STATEMENT', 'GEO_MATCH_STATEMENT', 'RATE_BASED_STATEMENT', 'RULE_GROUP_REFERENCE_STATEMENT', 'REGEX_PATTERN_REFERENCE_STATEMENT', 'IP_SET_REFERENCE_STATEMENT', 'MANAGED_RULE_SET_STATEMENT', 'LABEL_MATCH_STATEMENT', 'AND_STATEMENT', 'OR_STATEMENT', 'NOT_STATEMENT', 'IP_ADDRESS', 'IP_ADDRESS_VERSION', 'FIELD_TO_MATCH', 'TEXT_TRANSFORMATION', 'SINGLE_QUERY_ARGUMENT', 'SINGLE_HEADER', 'DEFAULT_ACTION', 'RULE_ACTION', 'ENTITY_LIMIT', 'OVERRIDE_ACTION', 'SCOPE_VALUE', 'RESOURCE_ARN', 'RESOURCE_TYPE', 'TAGS', 'TAG_KEYS', 'METRIC_NAME', 'FIREWALL_MANAGER_STATEMENT', 'FALLBACK_BEHAVIOR', 'POSITION', 'FORWARDED_IP_CONFIG', 'IP_SET_FORWARDED_IP_CONFIG', 'HEADER_NAME', 'CUSTOM_REQUEST_HANDLING', 'RESPONSE_CONTENT_TYPE', 'CUSTOM_RESPONSE', 'CUSTOM_RESPONSE_BODY', 'JSON_MATCH_PATTERN', 'JSON_MATCH_SCOPE', 'BODY_PARSING_FALLBACK_BEHAVIOR', 'LOGGING_FILTER', 'FILTER_CONDITION', 'EXPIRE_TIMESTAMP', 'CHANGE_PROPAGATION_STATUS', 'ASSOCIABLE_RESOURCE', 'LOG_DESTINATION', 'MANAGED_RULE_GROUP_CONFIG', 'PAYLOAD_TYPE', 'HEADER_MATCH_PATTERN', 'COOKIE_MATCH_PATTERN', 'MAP_MATCH_SCOPE', 'OVERSIZE_HANDLING', 'CHALLENGE_CONFIG', 'TOKEN_DOMAIN', 'ATP_RULE_SET_RESPONSE_INSPECTION', 'ASSOCIATED_RESOURCE_TYPE', 'SCOPE_DOWN', 'CUSTOM_KEYS', 'ACP_RULE_SET_RESPONSE_INSPECTION', ], ], 'ParameterExceptionParameter' => [ 'type' => 'string', 'min' => 1, ], 'PasswordField' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'FieldIdentifier', ], ], ], 'PayloadType' => [ 'type' => 'string', 'enum' => [ 'JSON', 'FORM_ENCODED', ], ], 'PhoneNumberField' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'FieldIdentifier', ], ], ], 'PhoneNumberFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberField', ], ], 'Platform' => [ 'type' => 'string', 'enum' => [ 'IOS', 'ANDROID', ], ], 'PolicyString' => [ 'type' => 'string', 'max' => 395000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PopulationSize' => [ 'type' => 'long', ], 'PositionalConstraint' => [ 'type' => 'string', 'enum' => [ 'EXACTLY', 'STARTS_WITH', 'ENDS_WITH', 'CONTAINS', 'CONTAINS_WORD', ], ], 'ProductDescription' => [ 'type' => 'string', 'min' => 1, 'pattern' => '.*\\S.*', ], 'ProductId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ProductLink' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ProductTitle' => [ 'type' => 'string', 'min' => 1, 'pattern' => '.*\\S.*', ], 'PublishedVersions' => [ 'type' => 'map', 'key' => [ 'shape' => 'VersionKeyString', ], 'value' => [ 'shape' => 'ManagedRuleSetVersion', ], ], 'PutLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'LoggingConfiguration', ], 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'PutLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'PutManagedRuleSetVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'RecommendedVersion' => [ 'shape' => 'VersionKeyString', ], 'VersionsToPublish' => [ 'shape' => 'VersionsToPublish', ], ], ], 'PutManagedRuleSetVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextLockToken' => [ 'shape' => 'LockToken', ], ], ], 'PutPermissionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Policy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Policy' => [ 'shape' => 'PolicyString', ], ], ], 'PutPermissionPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'QueryString' => [ 'type' => 'structure', 'members' => [], ], 'RateBasedStatement' => [ 'type' => 'structure', 'required' => [ 'Limit', 'AggregateKeyType', ], 'members' => [ 'Limit' => [ 'shape' => 'RateLimit', ], 'EvaluationWindowSec' => [ 'shape' => 'EvaluationWindowSec', ], 'AggregateKeyType' => [ 'shape' => 'RateBasedStatementAggregateKeyType', ], 'ScopeDownStatement' => [ 'shape' => 'Statement', ], 'ForwardedIPConfig' => [ 'shape' => 'ForwardedIPConfig', ], 'CustomKeys' => [ 'shape' => 'RateBasedStatementCustomKeys', ], ], ], 'RateBasedStatementAggregateKeyType' => [ 'type' => 'string', 'enum' => [ 'IP', 'FORWARDED_IP', 'CUSTOM_KEYS', 'CONSTANT', ], ], 'RateBasedStatementCustomKey' => [ 'type' => 'structure', 'members' => [ 'Header' => [ 'shape' => 'RateLimitHeader', ], 'Cookie' => [ 'shape' => 'RateLimitCookie', ], 'QueryArgument' => [ 'shape' => 'RateLimitQueryArgument', ], 'QueryString' => [ 'shape' => 'RateLimitQueryString', ], 'HTTPMethod' => [ 'shape' => 'RateLimitHTTPMethod', ], 'ForwardedIP' => [ 'shape' => 'RateLimitForwardedIP', ], 'IP' => [ 'shape' => 'RateLimitIP', ], 'LabelNamespace' => [ 'shape' => 'RateLimitLabelNamespace', ], 'UriPath' => [ 'shape' => 'RateLimitUriPath', ], ], ], 'RateBasedStatementCustomKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'RateBasedStatementCustomKey', ], 'max' => 5, ], 'RateBasedStatementManagedKeysIPSet' => [ 'type' => 'structure', 'members' => [ 'IPAddressVersion' => [ 'shape' => 'IPAddressVersion', ], 'Addresses' => [ 'shape' => 'IPAddresses', ], ], ], 'RateLimit' => [ 'type' => 'long', 'max' => 2000000000, 'min' => 100, ], 'RateLimitCookie' => [ 'type' => 'structure', 'required' => [ 'Name', 'TextTransformations', ], 'members' => [ 'Name' => [ 'shape' => 'FieldToMatchData', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'RateLimitForwardedIP' => [ 'type' => 'structure', 'members' => [], ], 'RateLimitHTTPMethod' => [ 'type' => 'structure', 'members' => [], ], 'RateLimitHeader' => [ 'type' => 'structure', 'required' => [ 'Name', 'TextTransformations', ], 'members' => [ 'Name' => [ 'shape' => 'FieldToMatchData', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'RateLimitIP' => [ 'type' => 'structure', 'members' => [], ], 'RateLimitLabelNamespace' => [ 'type' => 'structure', 'required' => [ 'Namespace', ], 'members' => [ 'Namespace' => [ 'shape' => 'LabelNamespace', ], ], ], 'RateLimitQueryArgument' => [ 'type' => 'structure', 'required' => [ 'Name', 'TextTransformations', ], 'members' => [ 'Name' => [ 'shape' => 'FieldToMatchData', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'RateLimitQueryString' => [ 'type' => 'structure', 'required' => [ 'TextTransformations', ], 'members' => [ 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'RateLimitUriPath' => [ 'type' => 'structure', 'required' => [ 'TextTransformations', ], 'members' => [ 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'RedactedFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldToMatch', ], 'max' => 100, ], 'Regex' => [ 'type' => 'structure', 'members' => [ 'RegexString' => [ 'shape' => 'RegexPatternString', ], ], ], 'RegexMatchStatement' => [ 'type' => 'structure', 'required' => [ 'RegexString', 'FieldToMatch', 'TextTransformations', ], 'members' => [ 'RegexString' => [ 'shape' => 'RegexPatternString', ], 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'RegexPatternSet' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'ARN' => [ 'shape' => 'ResourceArn', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'RegularExpressionList' => [ 'shape' => 'RegularExpressionList', ], ], ], 'RegexPatternSetReferenceStatement' => [ 'type' => 'structure', 'required' => [ 'ARN', 'FieldToMatch', 'TextTransformations', ], 'members' => [ 'ARN' => [ 'shape' => 'ResourceArn', ], 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'RegexPatternSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegexPatternSetSummary', ], ], 'RegexPatternSetSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'ARN' => [ 'shape' => 'ResourceArn', ], ], ], 'RegexPatternString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*', ], 'RegistrationPagePathString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'RegularExpressionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Regex', ], ], 'ReleaseNotes' => [ 'type' => 'string', ], 'ReleaseSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReleaseSummary', ], ], 'ReleaseSummary' => [ 'type' => 'structure', 'members' => [ 'ReleaseVersion' => [ 'shape' => 'VersionKeyString', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'RequestBody' => [ 'type' => 'map', 'key' => [ 'shape' => 'AssociatedResourceType', ], 'value' => [ 'shape' => 'RequestBodyAssociatedResourceTypeConfig', ], ], 'RequestBodyAssociatedResourceTypeConfig' => [ 'type' => 'structure', 'required' => [ 'DefaultSizeInspectionLimit', ], 'members' => [ 'DefaultSizeInspectionLimit' => [ 'shape' => 'SizeInspectionLimit', ], ], ], 'RequestInspection' => [ 'type' => 'structure', 'required' => [ 'PayloadType', 'UsernameField', 'PasswordField', ], 'members' => [ 'PayloadType' => [ 'shape' => 'PayloadType', ], 'UsernameField' => [ 'shape' => 'UsernameField', ], 'PasswordField' => [ 'shape' => 'PasswordField', ], ], ], 'RequestInspectionACFP' => [ 'type' => 'structure', 'required' => [ 'PayloadType', ], 'members' => [ 'PayloadType' => [ 'shape' => 'PayloadType', ], 'UsernameField' => [ 'shape' => 'UsernameField', ], 'PasswordField' => [ 'shape' => 'PasswordField', ], 'EmailField' => [ 'shape' => 'EmailField', ], 'PhoneNumberFields' => [ 'shape' => 'PhoneNumberFields', ], 'AddressFields' => [ 'shape' => 'AddressFields', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '.*\\S.*', ], 'ResourceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'APPLICATION_LOAD_BALANCER', 'API_GATEWAY', 'APPSYNC', 'COGNITO_USER_POOL', 'APP_RUNNER_SERVICE', 'VERIFIED_ACCESS_INSTANCE', ], ], 'ResponseCode' => [ 'type' => 'integer', ], 'ResponseContent' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'ResponseContentType' => [ 'type' => 'string', 'enum' => [ 'TEXT_PLAIN', 'TEXT_HTML', 'APPLICATION_JSON', ], ], 'ResponseInspection' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'ResponseInspectionStatusCode', ], 'Header' => [ 'shape' => 'ResponseInspectionHeader', ], 'BodyContains' => [ 'shape' => 'ResponseInspectionBodyContains', ], 'Json' => [ 'shape' => 'ResponseInspectionJson', ], ], ], 'ResponseInspectionBodyContains' => [ 'type' => 'structure', 'required' => [ 'SuccessStrings', 'FailureStrings', ], 'members' => [ 'SuccessStrings' => [ 'shape' => 'ResponseInspectionBodyContainsSuccessStrings', ], 'FailureStrings' => [ 'shape' => 'ResponseInspectionBodyContainsFailureStrings', ], ], ], 'ResponseInspectionBodyContainsFailureStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureValue', ], 'max' => 5, 'min' => 1, ], 'ResponseInspectionBodyContainsSuccessStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessValue', ], 'max' => 5, 'min' => 1, ], 'ResponseInspectionHeader' => [ 'type' => 'structure', 'required' => [ 'Name', 'SuccessValues', 'FailureValues', ], 'members' => [ 'Name' => [ 'shape' => 'ResponseInspectionHeaderName', ], 'SuccessValues' => [ 'shape' => 'ResponseInspectionHeaderSuccessValues', ], 'FailureValues' => [ 'shape' => 'ResponseInspectionHeaderFailureValues', ], ], ], 'ResponseInspectionHeaderFailureValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureValue', ], 'max' => 3, 'min' => 1, ], 'ResponseInspectionHeaderName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ResponseInspectionHeaderSuccessValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessValue', ], 'max' => 3, 'min' => 1, ], 'ResponseInspectionJson' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'SuccessValues', 'FailureValues', ], 'members' => [ 'Identifier' => [ 'shape' => 'FieldIdentifier', ], 'SuccessValues' => [ 'shape' => 'ResponseInspectionJsonSuccessValues', ], 'FailureValues' => [ 'shape' => 'ResponseInspectionJsonFailureValues', ], ], ], 'ResponseInspectionJsonFailureValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureValue', ], 'max' => 5, 'min' => 1, ], 'ResponseInspectionJsonSuccessValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessValue', ], 'max' => 5, 'min' => 1, ], 'ResponseInspectionStatusCode' => [ 'type' => 'structure', 'required' => [ 'SuccessCodes', 'FailureCodes', ], 'members' => [ 'SuccessCodes' => [ 'shape' => 'ResponseInspectionStatusCodeSuccessCodes', ], 'FailureCodes' => [ 'shape' => 'ResponseInspectionStatusCodeFailureCodes', ], ], ], 'ResponseInspectionStatusCodeFailureCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureCode', ], 'max' => 10, 'min' => 1, ], 'ResponseInspectionStatusCodeSuccessCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessCode', ], 'max' => 10, 'min' => 1, ], 'ResponseStatusCode' => [ 'type' => 'integer', 'max' => 599, 'min' => 200, ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'Name', 'Priority', 'Statement', 'VisibilityConfig', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Priority' => [ 'shape' => 'RulePriority', ], 'Statement' => [ 'shape' => 'Statement', ], 'Action' => [ 'shape' => 'RuleAction', ], 'OverrideAction' => [ 'shape' => 'OverrideAction', ], 'RuleLabels' => [ 'shape' => 'Labels', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], 'CaptchaConfig' => [ 'shape' => 'CaptchaConfig', ], 'ChallengeConfig' => [ 'shape' => 'ChallengeConfig', ], ], ], 'RuleAction' => [ 'type' => 'structure', 'members' => [ 'Block' => [ 'shape' => 'BlockAction', ], 'Allow' => [ 'shape' => 'AllowAction', ], 'Count' => [ 'shape' => 'CountAction', ], 'Captcha' => [ 'shape' => 'CaptchaAction', ], 'Challenge' => [ 'shape' => 'ChallengeAction', ], ], ], 'RuleActionOverride' => [ 'type' => 'structure', 'required' => [ 'Name', 'ActionToUse', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'ActionToUse' => [ 'shape' => 'RuleAction', ], ], ], 'RuleActionOverrides' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleActionOverride', ], 'max' => 100, ], 'RuleGroup' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'Capacity', 'ARN', 'VisibilityConfig', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'Capacity' => [ 'shape' => 'CapacityUnit', ], 'ARN' => [ 'shape' => 'ResourceArn', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Rules' => [ 'shape' => 'Rules', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], 'LabelNamespace' => [ 'shape' => 'LabelName', ], 'CustomResponseBodies' => [ 'shape' => 'CustomResponseBodies', ], 'AvailableLabels' => [ 'shape' => 'LabelSummaries', ], 'ConsumedLabels' => [ 'shape' => 'LabelSummaries', ], ], ], 'RuleGroupReferenceStatement' => [ 'type' => 'structure', 'required' => [ 'ARN', ], 'members' => [ 'ARN' => [ 'shape' => 'ResourceArn', ], 'ExcludedRules' => [ 'shape' => 'ExcludedRules', ], 'RuleActionOverrides' => [ 'shape' => 'RuleActionOverrides', ], ], ], 'RuleGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSummary', ], ], 'RuleGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'ARN' => [ 'shape' => 'ResourceArn', ], ], ], 'RulePriority' => [ 'type' => 'integer', 'min' => 0, ], 'RuleSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleSummary', ], ], 'RuleSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Action' => [ 'shape' => 'RuleAction', ], ], ], 'Rules' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], ], 'SampleWeight' => [ 'type' => 'long', 'min' => 0, ], 'SampledHTTPRequest' => [ 'type' => 'structure', 'required' => [ 'Request', 'Weight', ], 'members' => [ 'Request' => [ 'shape' => 'HTTPRequest', ], 'Weight' => [ 'shape' => 'SampleWeight', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Action' => [ 'shape' => 'Action', ], 'RuleNameWithinRuleGroup' => [ 'shape' => 'EntityName', ], 'RequestHeadersInserted' => [ 'shape' => 'HTTPHeaders', ], 'ResponseCodeSent' => [ 'shape' => 'ResponseStatusCode', ], 'Labels' => [ 'shape' => 'Labels', ], 'CaptchaResponse' => [ 'shape' => 'CaptchaResponse', ], 'ChallengeResponse' => [ 'shape' => 'ChallengeResponse', ], 'OverriddenAction' => [ 'shape' => 'Action', ], ], ], 'SampledHTTPRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampledHTTPRequest', ], ], 'Scope' => [ 'type' => 'string', 'enum' => [ 'CLOUDFRONT', 'REGIONAL', ], ], 'SearchString' => [ 'type' => 'blob', ], 'SensitivityLevel' => [ 'type' => 'string', 'enum' => [ 'LOW', 'HIGH', ], ], 'SingleCookieName' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '.*\\S.*', ], 'SingleHeader' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'FieldToMatchData', ], ], ], 'SingleQueryArgument' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'FieldToMatchData', ], ], ], 'Size' => [ 'type' => 'long', 'max' => 21474836480, 'min' => 0, ], 'SizeConstraintStatement' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'ComparisonOperator', 'Size', 'TextTransformations', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'Size' => [ 'shape' => 'Size', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], 'SizeInspectionLimit' => [ 'type' => 'string', 'enum' => [ 'KB_16', 'KB_32', 'KB_48', 'KB_64', ], ], 'SolveTimestamp' => [ 'type' => 'long', ], 'SourceType' => [ 'type' => 'string', ], 'SqliMatchStatement' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'TextTransformations', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], 'SensitivityLevel' => [ 'shape' => 'SensitivityLevel', ], ], ], 'Statement' => [ 'type' => 'structure', 'members' => [ 'ByteMatchStatement' => [ 'shape' => 'ByteMatchStatement', ], 'SqliMatchStatement' => [ 'shape' => 'SqliMatchStatement', ], 'XssMatchStatement' => [ 'shape' => 'XssMatchStatement', ], 'SizeConstraintStatement' => [ 'shape' => 'SizeConstraintStatement', ], 'GeoMatchStatement' => [ 'shape' => 'GeoMatchStatement', ], 'RuleGroupReferenceStatement' => [ 'shape' => 'RuleGroupReferenceStatement', ], 'IPSetReferenceStatement' => [ 'shape' => 'IPSetReferenceStatement', ], 'RegexPatternSetReferenceStatement' => [ 'shape' => 'RegexPatternSetReferenceStatement', ], 'RateBasedStatement' => [ 'shape' => 'RateBasedStatement', ], 'AndStatement' => [ 'shape' => 'AndStatement', ], 'OrStatement' => [ 'shape' => 'OrStatement', ], 'NotStatement' => [ 'shape' => 'NotStatement', ], 'ManagedRuleGroupStatement' => [ 'shape' => 'ManagedRuleGroupStatement', ], 'LabelMatchStatement' => [ 'shape' => 'LabelMatchStatement', ], 'RegexMatchStatement' => [ 'shape' => 'RegexMatchStatement', ], ], ], 'Statements' => [ 'type' => 'list', 'member' => [ 'shape' => 'Statement', ], ], 'String' => [ 'type' => 'string', ], 'SuccessCode' => [ 'type' => 'integer', 'max' => 999, 'min' => 0, ], 'SuccessValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagInfoForResource' => [ 'type' => 'structure', 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceArn', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TextTransformation' => [ 'type' => 'structure', 'required' => [ 'Priority', 'Type', ], 'members' => [ 'Priority' => [ 'shape' => 'TextTransformationPriority', ], 'Type' => [ 'shape' => 'TextTransformationType', ], ], ], 'TextTransformationPriority' => [ 'type' => 'integer', 'min' => 0, ], 'TextTransformationType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'COMPRESS_WHITE_SPACE', 'HTML_ENTITY_DECODE', 'LOWERCASE', 'CMD_LINE', 'URL_DECODE', 'BASE64_DECODE', 'HEX_DECODE', 'MD5', 'REPLACE_COMMENTS', 'ESCAPE_SEQ_DECODE', 'SQL_HEX_DECODE', 'CSS_DECODE', 'JS_DECODE', 'NORMALIZE_PATH', 'NORMALIZE_PATH_WIN', 'REMOVE_NULLS', 'REPLACE_NULLS', 'BASE64_DECODE_EXT', 'URL_DECODE_UNI', 'UTF8_TO_UNICODE', ], ], 'TextTransformations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextTransformation', ], 'min' => 1, ], 'TimeWindow' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'TimeWindowDay' => [ 'type' => 'integer', 'min' => 1, ], 'TimeWindowSecond' => [ 'type' => 'long', 'max' => 259200, 'min' => 60, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TokenDomain' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '^[\\w\\.\\-/]+$', ], 'TokenDomains' => [ 'type' => 'list', 'member' => [ 'shape' => 'TokenDomain', ], ], 'URIString' => [ 'type' => 'string', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'Addresses', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Addresses' => [ 'shape' => 'IPAddresses', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'UpdateIPSetResponse' => [ 'type' => 'structure', 'members' => [ 'NextLockToken' => [ 'shape' => 'LockToken', ], ], ], 'UpdateManagedRuleSetVersionExpiryDateRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'LockToken', 'VersionToExpire', 'ExpiryTimestamp', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'VersionToExpire' => [ 'shape' => 'VersionKeyString', ], 'ExpiryTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateManagedRuleSetVersionExpiryDateResponse' => [ 'type' => 'structure', 'members' => [ 'ExpiringVersion' => [ 'shape' => 'VersionKeyString', ], 'ExpiryTimestamp' => [ 'shape' => 'Timestamp', ], 'NextLockToken' => [ 'shape' => 'LockToken', ], ], ], 'UpdateRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'RegularExpressionList', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'RegularExpressionList' => [ 'shape' => 'RegularExpressionList', ], 'LockToken' => [ 'shape' => 'LockToken', ], ], ], 'UpdateRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [ 'NextLockToken' => [ 'shape' => 'LockToken', ], ], ], 'UpdateRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'VisibilityConfig', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Rules' => [ 'shape' => 'Rules', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'CustomResponseBodies' => [ 'shape' => 'CustomResponseBodies', ], ], ], 'UpdateRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'NextLockToken' => [ 'shape' => 'LockToken', ], ], ], 'UpdateWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Id', 'DefaultAction', 'VisibilityConfig', 'LockToken', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Scope' => [ 'shape' => 'Scope', ], 'Id' => [ 'shape' => 'EntityId', ], 'DefaultAction' => [ 'shape' => 'DefaultAction', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Rules' => [ 'shape' => 'Rules', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'CustomResponseBodies' => [ 'shape' => 'CustomResponseBodies', ], 'CaptchaConfig' => [ 'shape' => 'CaptchaConfig', ], 'ChallengeConfig' => [ 'shape' => 'ChallengeConfig', ], 'TokenDomains' => [ 'shape' => 'TokenDomains', ], 'AssociationConfig' => [ 'shape' => 'AssociationConfig', ], ], ], 'UpdateWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'NextLockToken' => [ 'shape' => 'LockToken', ], ], ], 'UriPath' => [ 'type' => 'structure', 'members' => [], ], 'UsernameField' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'FieldIdentifier', ], ], ], 'VendorName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'VersionKeyString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\w#:\\.\\-/]+$', ], 'VersionToPublish' => [ 'type' => 'structure', 'members' => [ 'AssociatedRuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'ForecastedLifetime' => [ 'shape' => 'TimeWindowDay', ], ], ], 'VersionsToPublish' => [ 'type' => 'map', 'key' => [ 'shape' => 'VersionKeyString', ], 'value' => [ 'shape' => 'VersionToPublish', ], ], 'VisibilityConfig' => [ 'type' => 'structure', 'required' => [ 'SampledRequestsEnabled', 'CloudWatchMetricsEnabled', 'MetricName', ], 'members' => [ 'SampledRequestsEnabled' => [ 'shape' => 'Boolean', ], 'CloudWatchMetricsEnabled' => [ 'shape' => 'Boolean', ], 'MetricName' => [ 'shape' => 'MetricName', ], ], ], 'WAFAssociatedItemException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFConfigurationWarningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFDuplicateItemException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFExpiredManagedRuleGroupVersionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFInternalErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'WAFInvalidOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFInvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'Field' => [ 'shape' => 'ParameterExceptionField', ], 'Parameter' => [ 'shape' => 'ParameterExceptionParameter', ], 'Reason' => [ 'shape' => 'ErrorReason', ], ], 'exception' => true, ], 'WAFInvalidPermissionPolicyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFInvalidResourceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFLimitsExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'SourceType' => [ 'shape' => 'SourceType', ], ], 'exception' => true, ], 'WAFLogDestinationPermissionIssueException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFNonexistentItemException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFOptimisticLockException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFServiceLinkedRoleErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFSubscriptionNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFTagOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFTagOperationInternalErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'WAFUnavailableEntityException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WAFUnsupportedAggregateKeyTypeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'WebACL' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'ARN', 'DefaultAction', 'VisibilityConfig', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'ARN' => [ 'shape' => 'ResourceArn', ], 'DefaultAction' => [ 'shape' => 'DefaultAction', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Rules' => [ 'shape' => 'Rules', ], 'VisibilityConfig' => [ 'shape' => 'VisibilityConfig', ], 'Capacity' => [ 'shape' => 'ConsumedCapacity', ], 'PreProcessFirewallManagerRuleGroups' => [ 'shape' => 'FirewallManagerRuleGroups', ], 'PostProcessFirewallManagerRuleGroups' => [ 'shape' => 'FirewallManagerRuleGroups', ], 'ManagedByFirewallManager' => [ 'shape' => 'Boolean', ], 'LabelNamespace' => [ 'shape' => 'LabelName', ], 'CustomResponseBodies' => [ 'shape' => 'CustomResponseBodies', ], 'CaptchaConfig' => [ 'shape' => 'CaptchaConfig', ], 'ChallengeConfig' => [ 'shape' => 'ChallengeConfig', ], 'TokenDomains' => [ 'shape' => 'TokenDomains', ], 'AssociationConfig' => [ 'shape' => 'AssociationConfig', ], ], ], 'WebACLSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebACLSummary', ], ], 'WebACLSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Id' => [ 'shape' => 'EntityId', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'LockToken' => [ 'shape' => 'LockToken', ], 'ARN' => [ 'shape' => 'ResourceArn', ], ], ], 'XssMatchStatement' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'TextTransformations', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformations' => [ 'shape' => 'TextTransformations', ], ], ], ],];
