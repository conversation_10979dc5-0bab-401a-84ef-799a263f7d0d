<?php
// This file was auto-generated from sdk-root/src/data/devicefarm/2015-06-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-06-23', 'endpointPrefix' => 'devicefarm', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS Device Farm', 'serviceId' => 'Device Farm', 'signatureVersion' => 'v4', 'targetPrefix' => 'DeviceFarm_20150623', 'uid' => 'devicefarm-2015-06-23', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateDevicePool' => [ 'name' => 'CreateDevicePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDevicePoolRequest', ], 'output' => [ 'shape' => 'CreateDevicePoolResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'CreateInstanceProfile' => [ 'name' => 'CreateInstanceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceProfileRequest', ], 'output' => [ 'shape' => 'CreateInstanceProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'CreateNetworkProfile' => [ 'name' => 'CreateNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateNetworkProfileRequest', ], 'output' => [ 'shape' => 'CreateNetworkProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProjectRequest', ], 'output' => [ 'shape' => 'CreateProjectResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], [ 'shape' => 'TagOperationException', ], ], ], 'CreateRemoteAccessSession' => [ 'name' => 'CreateRemoteAccessSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRemoteAccessSessionRequest', ], 'output' => [ 'shape' => 'CreateRemoteAccessSessionResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'CreateTestGridProject' => [ 'name' => 'CreateTestGridProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTestGridProjectRequest', ], 'output' => [ 'shape' => 'CreateTestGridProjectResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateTestGridUrl' => [ 'name' => 'CreateTestGridUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTestGridUrlRequest', ], 'output' => [ 'shape' => 'CreateTestGridUrlResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateUpload' => [ 'name' => 'CreateUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUploadRequest', ], 'output' => [ 'shape' => 'CreateUploadResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'CreateVPCEConfiguration' => [ 'name' => 'CreateVPCEConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVPCEConfigurationRequest', ], 'output' => [ 'shape' => 'CreateVPCEConfigurationResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteDevicePool' => [ 'name' => 'DeleteDevicePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDevicePoolRequest', ], 'output' => [ 'shape' => 'DeleteDevicePoolResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteInstanceProfile' => [ 'name' => 'DeleteInstanceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceProfileRequest', ], 'output' => [ 'shape' => 'DeleteInstanceProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteNetworkProfile' => [ 'name' => 'DeleteNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteNetworkProfileRequest', ], 'output' => [ 'shape' => 'DeleteNetworkProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProjectRequest', ], 'output' => [ 'shape' => 'DeleteProjectResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteRemoteAccessSession' => [ 'name' => 'DeleteRemoteAccessSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRemoteAccessSessionRequest', ], 'output' => [ 'shape' => 'DeleteRemoteAccessSessionResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteRun' => [ 'name' => 'DeleteRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRunRequest', ], 'output' => [ 'shape' => 'DeleteRunResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteTestGridProject' => [ 'name' => 'DeleteTestGridProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTestGridProjectRequest', ], 'output' => [ 'shape' => 'DeleteTestGridProjectResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'CannotDeleteException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteUpload' => [ 'name' => 'DeleteUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUploadRequest', ], 'output' => [ 'shape' => 'DeleteUploadResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'DeleteVPCEConfiguration' => [ 'name' => 'DeleteVPCEConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVPCEConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteVPCEConfigurationResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceAccountException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'GetAccountSettings' => [ 'name' => 'GetAccountSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAccountSettingsRequest', ], 'output' => [ 'shape' => 'GetAccountSettingsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetDevice' => [ 'name' => 'GetDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeviceRequest', ], 'output' => [ 'shape' => 'GetDeviceResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetDeviceInstance' => [ 'name' => 'GetDeviceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeviceInstanceRequest', ], 'output' => [ 'shape' => 'GetDeviceInstanceResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetDevicePool' => [ 'name' => 'GetDevicePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevicePoolRequest', ], 'output' => [ 'shape' => 'GetDevicePoolResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetDevicePoolCompatibility' => [ 'name' => 'GetDevicePoolCompatibility', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevicePoolCompatibilityRequest', ], 'output' => [ 'shape' => 'GetDevicePoolCompatibilityResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetInstanceProfile' => [ 'name' => 'GetInstanceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceProfileRequest', ], 'output' => [ 'shape' => 'GetInstanceProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetNetworkProfile' => [ 'name' => 'GetNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetNetworkProfileRequest', ], 'output' => [ 'shape' => 'GetNetworkProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetOfferingStatus' => [ 'name' => 'GetOfferingStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOfferingStatusRequest', ], 'output' => [ 'shape' => 'GetOfferingStatusResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotEligibleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetProject' => [ 'name' => 'GetProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProjectRequest', ], 'output' => [ 'shape' => 'GetProjectResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetRemoteAccessSession' => [ 'name' => 'GetRemoteAccessSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRemoteAccessSessionRequest', ], 'output' => [ 'shape' => 'GetRemoteAccessSessionResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetRun' => [ 'name' => 'GetRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRunRequest', ], 'output' => [ 'shape' => 'GetRunResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetSuite' => [ 'name' => 'GetSuite', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSuiteRequest', ], 'output' => [ 'shape' => 'GetSuiteResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetTest' => [ 'name' => 'GetTest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTestRequest', ], 'output' => [ 'shape' => 'GetTestResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetTestGridProject' => [ 'name' => 'GetTestGridProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTestGridProjectRequest', ], 'output' => [ 'shape' => 'GetTestGridProjectResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetTestGridSession' => [ 'name' => 'GetTestGridSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTestGridSessionRequest', ], 'output' => [ 'shape' => 'GetTestGridSessionResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetUpload' => [ 'name' => 'GetUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUploadRequest', ], 'output' => [ 'shape' => 'GetUploadResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'GetVPCEConfiguration' => [ 'name' => 'GetVPCEConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetVPCEConfigurationRequest', ], 'output' => [ 'shape' => 'GetVPCEConfigurationResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'InstallToRemoteAccessSession' => [ 'name' => 'InstallToRemoteAccessSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'InstallToRemoteAccessSessionRequest', ], 'output' => [ 'shape' => 'InstallToRemoteAccessSessionResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListArtifacts' => [ 'name' => 'ListArtifacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListArtifactsRequest', ], 'output' => [ 'shape' => 'ListArtifactsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListDeviceInstances' => [ 'name' => 'ListDeviceInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeviceInstancesRequest', ], 'output' => [ 'shape' => 'ListDeviceInstancesResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListDevicePools' => [ 'name' => 'ListDevicePools', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDevicePoolsRequest', ], 'output' => [ 'shape' => 'ListDevicePoolsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListDevices' => [ 'name' => 'ListDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDevicesRequest', ], 'output' => [ 'shape' => 'ListDevicesResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListInstanceProfiles' => [ 'name' => 'ListInstanceProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInstanceProfilesRequest', ], 'output' => [ 'shape' => 'ListInstanceProfilesResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListNetworkProfiles' => [ 'name' => 'ListNetworkProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListNetworkProfilesRequest', ], 'output' => [ 'shape' => 'ListNetworkProfilesResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListOfferingPromotions' => [ 'name' => 'ListOfferingPromotions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOfferingPromotionsRequest', ], 'output' => [ 'shape' => 'ListOfferingPromotionsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotEligibleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListOfferingTransactions' => [ 'name' => 'ListOfferingTransactions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOfferingTransactionsRequest', ], 'output' => [ 'shape' => 'ListOfferingTransactionsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotEligibleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListOfferings' => [ 'name' => 'ListOfferings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOfferingsRequest', ], 'output' => [ 'shape' => 'ListOfferingsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotEligibleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProjectsRequest', ], 'output' => [ 'shape' => 'ListProjectsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListRemoteAccessSessions' => [ 'name' => 'ListRemoteAccessSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRemoteAccessSessionsRequest', ], 'output' => [ 'shape' => 'ListRemoteAccessSessionsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListRuns' => [ 'name' => 'ListRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRunsRequest', ], 'output' => [ 'shape' => 'ListRunsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListSamples' => [ 'name' => 'ListSamples', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSamplesRequest', ], 'output' => [ 'shape' => 'ListSamplesResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListSuites' => [ 'name' => 'ListSuites', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSuitesRequest', ], 'output' => [ 'shape' => 'ListSuitesResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TagOperationException', ], ], ], 'ListTestGridProjects' => [ 'name' => 'ListTestGridProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTestGridProjectsRequest', ], 'output' => [ 'shape' => 'ListTestGridProjectsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTestGridSessionActions' => [ 'name' => 'ListTestGridSessionActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTestGridSessionActionsRequest', ], 'output' => [ 'shape' => 'ListTestGridSessionActionsResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTestGridSessionArtifacts' => [ 'name' => 'ListTestGridSessionArtifacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTestGridSessionArtifactsRequest', ], 'output' => [ 'shape' => 'ListTestGridSessionArtifactsResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTestGridSessions' => [ 'name' => 'ListTestGridSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTestGridSessionsRequest', ], 'output' => [ 'shape' => 'ListTestGridSessionsResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTests' => [ 'name' => 'ListTests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTestsRequest', ], 'output' => [ 'shape' => 'ListTestsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListUniqueProblems' => [ 'name' => 'ListUniqueProblems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUniqueProblemsRequest', ], 'output' => [ 'shape' => 'ListUniqueProblemsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListUploads' => [ 'name' => 'ListUploads', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUploadsRequest', ], 'output' => [ 'shape' => 'ListUploadsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ListVPCEConfigurations' => [ 'name' => 'ListVPCEConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVPCEConfigurationsRequest', ], 'output' => [ 'shape' => 'ListVPCEConfigurationsResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'PurchaseOffering' => [ 'name' => 'PurchaseOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PurchaseOfferingRequest', ], 'output' => [ 'shape' => 'PurchaseOfferingResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotEligibleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'RenewOffering' => [ 'name' => 'RenewOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RenewOfferingRequest', ], 'output' => [ 'shape' => 'RenewOfferingResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotEligibleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'ScheduleRun' => [ 'name' => 'ScheduleRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ScheduleRunRequest', ], 'output' => [ 'shape' => 'ScheduleRunResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'StopJob' => [ 'name' => 'StopJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopJobRequest', ], 'output' => [ 'shape' => 'StopJobResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'StopRemoteAccessSession' => [ 'name' => 'StopRemoteAccessSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopRemoteAccessSessionRequest', ], 'output' => [ 'shape' => 'StopRemoteAccessSessionResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'StopRun' => [ 'name' => 'StopRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopRunRequest', ], 'output' => [ 'shape' => 'StopRunResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TagOperationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TagPolicyException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TagOperationException', ], ], ], 'UpdateDeviceInstance' => [ 'name' => 'UpdateDeviceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDeviceInstanceRequest', ], 'output' => [ 'shape' => 'UpdateDeviceInstanceResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'UpdateDevicePool' => [ 'name' => 'UpdateDevicePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDevicePoolRequest', ], 'output' => [ 'shape' => 'UpdateDevicePoolResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'UpdateInstanceProfile' => [ 'name' => 'UpdateInstanceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInstanceProfileRequest', ], 'output' => [ 'shape' => 'UpdateInstanceProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'UpdateNetworkProfile' => [ 'name' => 'UpdateNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNetworkProfileRequest', ], 'output' => [ 'shape' => 'UpdateNetworkProfileResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProjectRequest', ], 'output' => [ 'shape' => 'UpdateProjectResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'UpdateTestGridProject' => [ 'name' => 'UpdateTestGridProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTestGridProjectRequest', ], 'output' => [ 'shape' => 'UpdateTestGridProjectResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUpload' => [ 'name' => 'UpdateUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUploadRequest', ], 'output' => [ 'shape' => 'UpdateUploadResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceAccountException', ], ], ], 'UpdateVPCEConfiguration' => [ 'name' => 'UpdateVPCEConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVPCEConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateVPCEConfigurationResult', ], 'errors' => [ [ 'shape' => 'ArgumentException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceAccountException', ], [ 'shape' => 'InvalidOperationException', ], ], ], ], 'shapes' => [ 'AWSAccountNumber' => [ 'type' => 'string', 'max' => 16, 'min' => 2, ], 'AccountSettings' => [ 'type' => 'structure', 'members' => [ 'awsAccountNumber' => [ 'shape' => 'AWSAccountNumber', ], 'unmeteredDevices' => [ 'shape' => 'PurchasedDevicesMap', ], 'unmeteredRemoteAccessDevices' => [ 'shape' => 'PurchasedDevicesMap', ], 'maxJobTimeoutMinutes' => [ 'shape' => 'JobTimeoutMinutes', ], 'trialMinutes' => [ 'shape' => 'TrialMinutes', ], 'maxSlots' => [ 'shape' => 'MaxSlotMap', ], 'defaultJobTimeoutMinutes' => [ 'shape' => 'JobTimeoutMinutes', ], 'skipAppResign' => [ 'shape' => 'SkipAppResign', ], ], ], 'AccountsCleanup' => [ 'type' => 'boolean', ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 32, 'pattern' => '^arn:.+', ], 'AmazonResourceNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AmazonResourceName', ], ], 'AndroidPaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AppPackagesCleanup' => [ 'type' => 'boolean', ], 'ArgumentException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'Artifact' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'ArtifactType', ], 'extension' => [ 'shape' => 'String', ], 'url' => [ 'shape' => 'URL', ], ], ], 'ArtifactCategory' => [ 'type' => 'string', 'enum' => [ 'SCREENSHOT', 'FILE', 'LOG', ], ], 'ArtifactType' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'SCREENSHOT', 'DEVICE_LOG', 'MESSAGE_LOG', 'VIDEO_LOG', 'RESULT_LOG', 'SERVICE_LOG', 'WEBKIT_LOG', 'INSTRUMENTATION_OUTPUT', 'EXERCISER_MONKEY_OUTPUT', 'CALABASH_JSON_OUTPUT', 'CALABASH_PRETTY_OUTPUT', 'CALABASH_STANDARD_OUTPUT', 'CALABASH_JAVA_XML_OUTPUT', 'AUTOMATION_OUTPUT', 'APPIUM_SERVER_OUTPUT', 'APPIUM_JAVA_OUTPUT', 'APPIUM_JAVA_XML_OUTPUT', 'APPIUM_PYTHON_OUTPUT', 'APPIUM_PYTHON_XML_OUTPUT', 'EXPLORER_EVENT_LOG', 'EXPLORER_SUMMARY_LOG', 'APPLICATION_CRASH_REPORT', 'XCTEST_LOG', 'VIDEO', 'CUSTOMER_ARTIFACT', 'CUSTOMER_ARTIFACT_LOG', 'TESTSPEC_OUTPUT', ], ], 'Artifacts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Artifact', ], ], 'BillingMethod' => [ 'type' => 'string', 'enum' => [ 'METERED', 'UNMETERED', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CPU' => [ 'type' => 'structure', 'members' => [ 'frequency' => [ 'shape' => 'String', ], 'architecture' => [ 'shape' => 'String', ], 'clock' => [ 'shape' => 'Double', ], ], ], 'CannotDeleteException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ClientId' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'ContentType' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'Counters' => [ 'type' => 'structure', 'members' => [ 'total' => [ 'shape' => 'Integer', ], 'passed' => [ 'shape' => 'Integer', ], 'failed' => [ 'shape' => 'Integer', ], 'warned' => [ 'shape' => 'Integer', ], 'errored' => [ 'shape' => 'Integer', ], 'stopped' => [ 'shape' => 'Integer', ], 'skipped' => [ 'shape' => 'Integer', ], ], ], 'CreateDevicePoolRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'name', 'rules', ], 'members' => [ 'projectArn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'rules' => [ 'shape' => 'Rules', ], 'maxDevices' => [ 'shape' => 'Integer', ], ], ], 'CreateDevicePoolResult' => [ 'type' => 'structure', 'members' => [ 'devicePool' => [ 'shape' => 'DevicePool', ], ], ], 'CreateInstanceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'packageCleanup' => [ 'shape' => 'Boolean', ], 'excludeAppPackagesFromCleanup' => [ 'shape' => 'PackageIds', ], 'rebootAfterUse' => [ 'shape' => 'Boolean', ], ], ], 'CreateInstanceProfileResult' => [ 'type' => 'structure', 'members' => [ 'instanceProfile' => [ 'shape' => 'InstanceProfile', ], ], ], 'CreateNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'name', ], 'members' => [ 'projectArn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'type' => [ 'shape' => 'NetworkProfileType', ], 'uplinkBandwidthBits' => [ 'shape' => 'Long', ], 'downlinkBandwidthBits' => [ 'shape' => 'Long', ], 'uplinkDelayMs' => [ 'shape' => 'Long', ], 'downlinkDelayMs' => [ 'shape' => 'Long', ], 'uplinkJitterMs' => [ 'shape' => 'Long', ], 'downlinkJitterMs' => [ 'shape' => 'Long', ], 'uplinkLossPercent' => [ 'shape' => 'PercentInteger', ], 'downlinkLossPercent' => [ 'shape' => 'PercentInteger', ], ], ], 'CreateNetworkProfileResult' => [ 'type' => 'structure', 'members' => [ 'networkProfile' => [ 'shape' => 'NetworkProfile', ], ], ], 'CreateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'defaultJobTimeoutMinutes' => [ 'shape' => 'JobTimeoutMinutes', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'CreateProjectResult' => [ 'type' => 'structure', 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'CreateRemoteAccessSessionConfiguration' => [ 'type' => 'structure', 'members' => [ 'billingMethod' => [ 'shape' => 'BillingMethod', ], 'vpceConfigurationArns' => [ 'shape' => 'AmazonResourceNames', ], ], ], 'CreateRemoteAccessSessionRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'deviceArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'AmazonResourceName', ], 'deviceArn' => [ 'shape' => 'AmazonResourceName', ], 'instanceArn' => [ 'shape' => 'AmazonResourceName', ], 'sshPublicKey' => [ 'shape' => 'SshPublicKey', ], 'remoteDebugEnabled' => [ 'shape' => 'Boolean', ], 'remoteRecordEnabled' => [ 'shape' => 'Boolean', ], 'remoteRecordAppArn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'clientId' => [ 'shape' => 'ClientId', ], 'configuration' => [ 'shape' => 'CreateRemoteAccessSessionConfiguration', ], 'interactionMode' => [ 'shape' => 'InteractionMode', ], 'skipAppResign' => [ 'shape' => 'Boolean', ], ], ], 'CreateRemoteAccessSessionResult' => [ 'type' => 'structure', 'members' => [ 'remoteAccessSession' => [ 'shape' => 'RemoteAccessSession', ], ], ], 'CreateTestGridProjectRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'vpcConfig' => [ 'shape' => 'TestGridVpcConfig', ], ], ], 'CreateTestGridProjectResult' => [ 'type' => 'structure', 'members' => [ 'testGridProject' => [ 'shape' => 'TestGridProject', ], ], ], 'CreateTestGridUrlRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'expiresInSeconds', ], 'members' => [ 'projectArn' => [ 'shape' => 'DeviceFarmArn', ], 'expiresInSeconds' => [ 'shape' => 'TestGridUrlExpiresInSecondsInput', ], ], ], 'CreateTestGridUrlResult' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'SensitiveString', ], 'expires' => [ 'shape' => 'DateTime', ], ], ], 'CreateUploadRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'name', 'type', ], 'members' => [ 'projectArn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'UploadType', ], 'contentType' => [ 'shape' => 'ContentType', ], ], ], 'CreateUploadResult' => [ 'type' => 'structure', 'members' => [ 'upload' => [ 'shape' => 'Upload', ], ], ], 'CreateVPCEConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'vpceConfigurationName', 'vpceServiceName', 'serviceDnsName', ], 'members' => [ 'vpceConfigurationName' => [ 'shape' => 'VPCEConfigurationName', ], 'vpceServiceName' => [ 'shape' => 'VPCEServiceName', ], 'serviceDnsName' => [ 'shape' => 'ServiceDnsName', ], 'vpceConfigurationDescription' => [ 'shape' => 'VPCEConfigurationDescription', ], ], ], 'CreateVPCEConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'vpceConfiguration' => [ 'shape' => 'VPCEConfiguration', ], ], ], 'CurrencyCode' => [ 'type' => 'string', 'enum' => [ 'USD', ], ], 'CustomerArtifactPaths' => [ 'type' => 'structure', 'members' => [ 'iosPaths' => [ 'shape' => 'IosPaths', ], 'androidPaths' => [ 'shape' => 'AndroidPaths', ], 'deviceHostPaths' => [ 'shape' => 'DeviceHostPaths', ], ], ], 'DateTime' => [ 'type' => 'timestamp', ], 'DeleteDevicePoolRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteDevicePoolResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInstanceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteInstanceProfileResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteNetworkProfileResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProjectRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteProjectResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRemoteAccessSessionRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteRemoteAccessSessionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRunRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteRunResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTestGridProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DeviceFarmArn', ], ], ], 'DeleteTestGridProjectResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUploadRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteUploadResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVPCEConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'DeleteVPCEConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'Device' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'manufacturer' => [ 'shape' => 'String', ], 'model' => [ 'shape' => 'String', ], 'modelId' => [ 'shape' => 'String', ], 'formFactor' => [ 'shape' => 'DeviceFormFactor', ], 'platform' => [ 'shape' => 'DevicePlatform', ], 'os' => [ 'shape' => 'String', ], 'cpu' => [ 'shape' => 'CPU', ], 'resolution' => [ 'shape' => 'Resolution', ], 'heapSize' => [ 'shape' => 'Long', ], 'memory' => [ 'shape' => 'Long', ], 'image' => [ 'shape' => 'String', ], 'carrier' => [ 'shape' => 'String', ], 'radio' => [ 'shape' => 'String', ], 'remoteAccessEnabled' => [ 'shape' => 'Boolean', ], 'remoteDebugEnabled' => [ 'shape' => 'Boolean', ], 'fleetType' => [ 'shape' => 'String', ], 'fleetName' => [ 'shape' => 'String', ], 'instances' => [ 'shape' => 'DeviceInstances', ], 'availability' => [ 'shape' => 'DeviceAvailability', ], ], ], 'DeviceAttribute' => [ 'type' => 'string', 'enum' => [ 'ARN', 'PLATFORM', 'FORM_FACTOR', 'MANUFACTURER', 'REMOTE_ACCESS_ENABLED', 'REMOTE_DEBUG_ENABLED', 'APPIUM_VERSION', 'INSTANCE_ARN', 'INSTANCE_LABELS', 'FLEET_TYPE', 'OS_VERSION', 'MODEL', 'AVAILABILITY', ], ], 'DeviceAvailability' => [ 'type' => 'string', 'enum' => [ 'TEMPORARY_NOT_AVAILABLE', 'BUSY', 'AVAILABLE', 'HIGHLY_AVAILABLE', ], ], 'DeviceFarmArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 32, 'pattern' => '^arn:aws:devicefarm:.+', ], 'DeviceFilter' => [ 'type' => 'structure', 'required' => [ 'attribute', 'operator', 'values', ], 'members' => [ 'attribute' => [ 'shape' => 'DeviceFilterAttribute', ], 'operator' => [ 'shape' => 'RuleOperator', ], 'values' => [ 'shape' => 'DeviceFilterValues', ], ], ], 'DeviceFilterAttribute' => [ 'type' => 'string', 'enum' => [ 'ARN', 'PLATFORM', 'OS_VERSION', 'MODEL', 'AVAILABILITY', 'FORM_FACTOR', 'MANUFACTURER', 'REMOTE_ACCESS_ENABLED', 'REMOTE_DEBUG_ENABLED', 'INSTANCE_ARN', 'INSTANCE_LABELS', 'FLEET_TYPE', ], ], 'DeviceFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DeviceFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceFilter', ], ], 'DeviceFormFactor' => [ 'type' => 'string', 'enum' => [ 'PHONE', 'TABLET', ], ], 'DeviceHostPaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DeviceInstance' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'deviceArn' => [ 'shape' => 'AmazonResourceName', ], 'labels' => [ 'shape' => 'InstanceLabels', ], 'status' => [ 'shape' => 'InstanceStatus', ], 'udid' => [ 'shape' => 'String', ], 'instanceProfile' => [ 'shape' => 'InstanceProfile', ], ], ], 'DeviceInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceInstance', ], ], 'DeviceMinutes' => [ 'type' => 'structure', 'members' => [ 'total' => [ 'shape' => 'Double', ], 'metered' => [ 'shape' => 'Double', ], 'unmetered' => [ 'shape' => 'Double', ], ], ], 'DevicePlatform' => [ 'type' => 'string', 'enum' => [ 'ANDROID', 'IOS', ], ], 'DevicePool' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'type' => [ 'shape' => 'DevicePoolType', ], 'rules' => [ 'shape' => 'Rules', ], 'maxDevices' => [ 'shape' => 'Integer', ], ], ], 'DevicePoolCompatibilityResult' => [ 'type' => 'structure', 'members' => [ 'device' => [ 'shape' => 'Device', ], 'compatible' => [ 'shape' => 'Boolean', ], 'incompatibilityMessages' => [ 'shape' => 'IncompatibilityMessages', ], ], ], 'DevicePoolCompatibilityResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePoolCompatibilityResult', ], ], 'DevicePoolType' => [ 'type' => 'string', 'enum' => [ 'CURATED', 'PRIVATE', ], ], 'DevicePools' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePool', ], ], 'DeviceSelectionConfiguration' => [ 'type' => 'structure', 'required' => [ 'filters', 'maxDevices', ], 'members' => [ 'filters' => [ 'shape' => 'DeviceFilters', ], 'maxDevices' => [ 'shape' => 'Integer', ], ], ], 'DeviceSelectionResult' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'DeviceFilters', ], 'matchedDevicesCount' => [ 'shape' => 'Integer', ], 'maxDevices' => [ 'shape' => 'Integer', ], ], ], 'Devices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Device', ], ], 'Double' => [ 'type' => 'double', ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExecutionConfiguration' => [ 'type' => 'structure', 'members' => [ 'jobTimeoutMinutes' => [ 'shape' => 'JobTimeoutMinutes', ], 'accountsCleanup' => [ 'shape' => 'AccountsCleanup', ], 'appPackagesCleanup' => [ 'shape' => 'AppPackagesCleanup', ], 'videoCapture' => [ 'shape' => 'VideoCapture', ], 'skipAppResign' => [ 'shape' => 'SkipAppResign', ], ], ], 'ExecutionResult' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'PASSED', 'WARNED', 'FAILED', 'SKIPPED', 'ERRORED', 'STOPPED', ], ], 'ExecutionResultCode' => [ 'type' => 'string', 'enum' => [ 'PARSING_FAILED', 'VPC_ENDPOINT_SETUP_FAILED', ], ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'PENDING_CONCURRENCY', 'PENDING_DEVICE', 'PROCESSING', 'SCHEDULING', 'PREPARING', 'RUNNING', 'COMPLETED', 'STOPPING', ], ], 'Filter' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, ], 'GetAccountSettingsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountSettingsResult' => [ 'type' => 'structure', 'members' => [ 'accountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'GetDeviceInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetDeviceInstanceResult' => [ 'type' => 'structure', 'members' => [ 'deviceInstance' => [ 'shape' => 'DeviceInstance', ], ], ], 'GetDevicePoolCompatibilityRequest' => [ 'type' => 'structure', 'required' => [ 'devicePoolArn', ], 'members' => [ 'devicePoolArn' => [ 'shape' => 'AmazonResourceName', ], 'appArn' => [ 'shape' => 'AmazonResourceName', ], 'testType' => [ 'shape' => 'TestType', ], 'test' => [ 'shape' => 'ScheduleRunTest', ], 'configuration' => [ 'shape' => 'ScheduleRunConfiguration', ], ], ], 'GetDevicePoolCompatibilityResult' => [ 'type' => 'structure', 'members' => [ 'compatibleDevices' => [ 'shape' => 'DevicePoolCompatibilityResults', ], 'incompatibleDevices' => [ 'shape' => 'DevicePoolCompatibilityResults', ], ], ], 'GetDevicePoolRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetDevicePoolResult' => [ 'type' => 'structure', 'members' => [ 'devicePool' => [ 'shape' => 'DevicePool', ], ], ], 'GetDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetDeviceResult' => [ 'type' => 'structure', 'members' => [ 'device' => [ 'shape' => 'Device', ], ], ], 'GetInstanceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetInstanceProfileResult' => [ 'type' => 'structure', 'members' => [ 'instanceProfile' => [ 'shape' => 'InstanceProfile', ], ], ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetJobResult' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'GetNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetNetworkProfileResult' => [ 'type' => 'structure', 'members' => [ 'networkProfile' => [ 'shape' => 'NetworkProfile', ], ], ], 'GetOfferingStatusRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetOfferingStatusResult' => [ 'type' => 'structure', 'members' => [ 'current' => [ 'shape' => 'OfferingStatusMap', ], 'nextPeriod' => [ 'shape' => 'OfferingStatusMap', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetProjectRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetProjectResult' => [ 'type' => 'structure', 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'GetRemoteAccessSessionRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetRemoteAccessSessionResult' => [ 'type' => 'structure', 'members' => [ 'remoteAccessSession' => [ 'shape' => 'RemoteAccessSession', ], ], ], 'GetRunRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetRunResult' => [ 'type' => 'structure', 'members' => [ 'run' => [ 'shape' => 'Run', ], ], ], 'GetSuiteRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetSuiteResult' => [ 'type' => 'structure', 'members' => [ 'suite' => [ 'shape' => 'Suite', ], ], ], 'GetTestGridProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DeviceFarmArn', ], ], ], 'GetTestGridProjectResult' => [ 'type' => 'structure', 'members' => [ 'testGridProject' => [ 'shape' => 'TestGridProject', ], ], ], 'GetTestGridSessionRequest' => [ 'type' => 'structure', 'members' => [ 'projectArn' => [ 'shape' => 'DeviceFarmArn', ], 'sessionId' => [ 'shape' => 'ResourceId', ], 'sessionArn' => [ 'shape' => 'DeviceFarmArn', ], ], ], 'GetTestGridSessionResult' => [ 'type' => 'structure', 'members' => [ 'testGridSession' => [ 'shape' => 'TestGridSession', ], ], ], 'GetTestRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetTestResult' => [ 'type' => 'structure', 'members' => [ 'test' => [ 'shape' => 'Test', ], ], ], 'GetUploadRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetUploadResult' => [ 'type' => 'structure', 'members' => [ 'upload' => [ 'shape' => 'Upload', ], ], ], 'GetVPCEConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'GetVPCEConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'vpceConfiguration' => [ 'shape' => 'VPCEConfiguration', ], ], ], 'HostAddress' => [ 'type' => 'string', 'max' => 1024, ], 'IdempotencyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'IncompatibilityMessage' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], 'type' => [ 'shape' => 'DeviceAttribute', ], ], ], 'IncompatibilityMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'IncompatibilityMessage', ], ], 'InstallToRemoteAccessSessionRequest' => [ 'type' => 'structure', 'required' => [ 'remoteAccessSessionArn', 'appArn', ], 'members' => [ 'remoteAccessSessionArn' => [ 'shape' => 'AmazonResourceName', ], 'appArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'InstallToRemoteAccessSessionResult' => [ 'type' => 'structure', 'members' => [ 'appUpload' => [ 'shape' => 'Upload', ], ], ], 'InstanceLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'InstanceProfile' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'packageCleanup' => [ 'shape' => 'Boolean', ], 'excludeAppPackagesFromCleanup' => [ 'shape' => 'PackageIds', ], 'rebootAfterUse' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], ], ], 'InstanceProfiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceProfile', ], ], 'InstanceStatus' => [ 'type' => 'string', 'enum' => [ 'IN_USE', 'PREPARING', 'AVAILABLE', 'NOT_AVAILABLE', ], ], 'Integer' => [ 'type' => 'integer', ], 'InteractionMode' => [ 'type' => 'string', 'enum' => [ 'INTERACTIVE', 'NO_VIDEO', 'VIDEO_ONLY', ], 'max' => 64, 'min' => 0, ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, 'fault' => true, ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'IosPaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Job' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'TestType', ], 'created' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'ExecutionStatus', ], 'result' => [ 'shape' => 'ExecutionResult', ], 'started' => [ 'shape' => 'DateTime', ], 'stopped' => [ 'shape' => 'DateTime', ], 'counters' => [ 'shape' => 'Counters', ], 'message' => [ 'shape' => 'Message', ], 'device' => [ 'shape' => 'Device', ], 'instanceArn' => [ 'shape' => 'AmazonResourceName', ], 'deviceMinutes' => [ 'shape' => 'DeviceMinutes', ], 'videoEndpoint' => [ 'shape' => 'String', ], 'videoCapture' => [ 'shape' => 'VideoCapture', ], ], ], 'JobTimeoutMinutes' => [ 'type' => 'integer', ], 'Jobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ListArtifactsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'type', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'type' => [ 'shape' => 'ArtifactCategory', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListArtifactsResult' => [ 'type' => 'structure', 'members' => [ 'artifacts' => [ 'shape' => 'Artifacts', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDeviceInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'Integer', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDeviceInstancesResult' => [ 'type' => 'structure', 'members' => [ 'deviceInstances' => [ 'shape' => 'DeviceInstances', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDevicePoolsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'type' => [ 'shape' => 'DevicePoolType', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDevicePoolsResult' => [ 'type' => 'structure', 'members' => [ 'devicePools' => [ 'shape' => 'DevicePools', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'filters' => [ 'shape' => 'DeviceFilters', ], ], ], 'ListDevicesResult' => [ 'type' => 'structure', 'members' => [ 'devices' => [ 'shape' => 'Devices', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListInstanceProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'Integer', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListInstanceProfilesResult' => [ 'type' => 'structure', 'members' => [ 'instanceProfiles' => [ 'shape' => 'InstanceProfiles', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListJobsResult' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'Jobs', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListNetworkProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'type' => [ 'shape' => 'NetworkProfileType', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListNetworkProfilesResult' => [ 'type' => 'structure', 'members' => [ 'networkProfiles' => [ 'shape' => 'NetworkProfiles', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOfferingPromotionsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOfferingPromotionsResult' => [ 'type' => 'structure', 'members' => [ 'offeringPromotions' => [ 'shape' => 'OfferingPromotions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOfferingTransactionsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOfferingTransactionsResult' => [ 'type' => 'structure', 'members' => [ 'offeringTransactions' => [ 'shape' => 'OfferingTransactions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOfferingsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOfferingsResult' => [ 'type' => 'structure', 'members' => [ 'offerings' => [ 'shape' => 'Offerings', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListProjectsRequest' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListProjectsResult' => [ 'type' => 'structure', 'members' => [ 'projects' => [ 'shape' => 'Projects', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRemoteAccessSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRemoteAccessSessionsResult' => [ 'type' => 'structure', 'members' => [ 'remoteAccessSessions' => [ 'shape' => 'RemoteAccessSessions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRunsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRunsResult' => [ 'type' => 'structure', 'members' => [ 'runs' => [ 'shape' => 'Runs', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSamplesRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSamplesResult' => [ 'type' => 'structure', 'members' => [ 'samples' => [ 'shape' => 'Samples', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSuitesRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSuitesResult' => [ 'type' => 'structure', 'members' => [ 'suites' => [ 'shape' => 'Suites', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'DeviceFarmArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTestGridProjectsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResult' => [ 'shape' => 'MaxPageSize', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestGridProjectsResult' => [ 'type' => 'structure', 'members' => [ 'testGridProjects' => [ 'shape' => 'TestGridProjects', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestGridSessionActionsRequest' => [ 'type' => 'structure', 'required' => [ 'sessionArn', ], 'members' => [ 'sessionArn' => [ 'shape' => 'DeviceFarmArn', ], 'maxResult' => [ 'shape' => 'MaxPageSize', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestGridSessionActionsResult' => [ 'type' => 'structure', 'members' => [ 'actions' => [ 'shape' => 'TestGridSessionActions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestGridSessionArtifactsRequest' => [ 'type' => 'structure', 'required' => [ 'sessionArn', ], 'members' => [ 'sessionArn' => [ 'shape' => 'DeviceFarmArn', ], 'type' => [ 'shape' => 'TestGridSessionArtifactCategory', ], 'maxResult' => [ 'shape' => 'MaxPageSize', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestGridSessionArtifactsResult' => [ 'type' => 'structure', 'members' => [ 'artifacts' => [ 'shape' => 'TestGridSessionArtifacts', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestGridSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DeviceFarmArn', ], 'status' => [ 'shape' => 'TestGridSessionStatus', ], 'creationTimeAfter' => [ 'shape' => 'DateTime', ], 'creationTimeBefore' => [ 'shape' => 'DateTime', ], 'endTimeAfter' => [ 'shape' => 'DateTime', ], 'endTimeBefore' => [ 'shape' => 'DateTime', ], 'maxResult' => [ 'shape' => 'MaxPageSize', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestGridSessionsResult' => [ 'type' => 'structure', 'members' => [ 'testGridSessions' => [ 'shape' => 'TestGridSessions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTestsResult' => [ 'type' => 'structure', 'members' => [ 'tests' => [ 'shape' => 'Tests', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListUniqueProblemsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListUniqueProblemsResult' => [ 'type' => 'structure', 'members' => [ 'uniqueProblems' => [ 'shape' => 'UniqueProblemsByExecutionResultMap', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListUploadsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'type' => [ 'shape' => 'UploadType', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListUploadsResult' => [ 'type' => 'structure', 'members' => [ 'uploads' => [ 'shape' => 'Uploads', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListVPCEConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'Integer', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListVPCEConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'vpceConfigurations' => [ 'shape' => 'VPCEConfigurations', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'Location' => [ 'type' => 'structure', 'required' => [ 'latitude', 'longitude', ], 'members' => [ 'latitude' => [ 'shape' => 'Double', ], 'longitude' => [ 'shape' => 'Double', ], ], ], 'Long' => [ 'type' => 'long', ], 'MaxPageSize' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MaxSlotMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Integer', ], ], 'Message' => [ 'type' => 'string', 'max' => 16384, 'min' => 0, ], 'Metadata' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, ], 'MonetaryAmount' => [ 'type' => 'structure', 'members' => [ 'amount' => [ 'shape' => 'Double', ], 'currencyCode' => [ 'shape' => 'CurrencyCode', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'NetworkProfile' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'type' => [ 'shape' => 'NetworkProfileType', ], 'uplinkBandwidthBits' => [ 'shape' => 'Long', ], 'downlinkBandwidthBits' => [ 'shape' => 'Long', ], 'uplinkDelayMs' => [ 'shape' => 'Long', ], 'downlinkDelayMs' => [ 'shape' => 'Long', ], 'uplinkJitterMs' => [ 'shape' => 'Long', ], 'downlinkJitterMs' => [ 'shape' => 'Long', ], 'uplinkLossPercent' => [ 'shape' => 'PercentInteger', ], 'downlinkLossPercent' => [ 'shape' => 'PercentInteger', ], ], ], 'NetworkProfileType' => [ 'type' => 'string', 'enum' => [ 'CURATED', 'PRIVATE', ], ], 'NetworkProfiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkProfile', ], ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '.*\\S.*', ], 'NotEligibleException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'Offering' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'OfferingIdentifier', ], 'description' => [ 'shape' => 'Message', ], 'type' => [ 'shape' => 'OfferingType', ], 'platform' => [ 'shape' => 'DevicePlatform', ], 'recurringCharges' => [ 'shape' => 'RecurringCharges', ], ], ], 'OfferingIdentifier' => [ 'type' => 'string', 'min' => 32, ], 'OfferingPromotion' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'OfferingPromotionIdentifier', ], 'description' => [ 'shape' => 'Message', ], ], ], 'OfferingPromotionIdentifier' => [ 'type' => 'string', 'min' => 4, ], 'OfferingPromotions' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferingPromotion', ], ], 'OfferingStatus' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'OfferingTransactionType', ], 'offering' => [ 'shape' => 'Offering', ], 'quantity' => [ 'shape' => 'Integer', ], 'effectiveOn' => [ 'shape' => 'DateTime', ], ], ], 'OfferingStatusMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OfferingIdentifier', ], 'value' => [ 'shape' => 'OfferingStatus', ], ], 'OfferingTransaction' => [ 'type' => 'structure', 'members' => [ 'offeringStatus' => [ 'shape' => 'OfferingStatus', ], 'transactionId' => [ 'shape' => 'TransactionIdentifier', ], 'offeringPromotionId' => [ 'shape' => 'OfferingPromotionIdentifier', ], 'createdOn' => [ 'shape' => 'DateTime', ], 'cost' => [ 'shape' => 'MonetaryAmount', ], ], ], 'OfferingTransactionType' => [ 'type' => 'string', 'enum' => [ 'PURCHASE', 'RENEW', 'SYSTEM', ], ], 'OfferingTransactions' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferingTransaction', ], ], 'OfferingType' => [ 'type' => 'string', 'enum' => [ 'RECURRING', ], ], 'Offerings' => [ 'type' => 'list', 'member' => [ 'shape' => 'Offering', ], ], 'PackageIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 4, ], 'PercentInteger' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Problem' => [ 'type' => 'structure', 'members' => [ 'run' => [ 'shape' => 'ProblemDetail', ], 'job' => [ 'shape' => 'ProblemDetail', ], 'suite' => [ 'shape' => 'ProblemDetail', ], 'test' => [ 'shape' => 'ProblemDetail', ], 'device' => [ 'shape' => 'Device', ], 'result' => [ 'shape' => 'ExecutionResult', ], 'message' => [ 'shape' => 'Message', ], ], ], 'ProblemDetail' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], ], ], 'Problems' => [ 'type' => 'list', 'member' => [ 'shape' => 'Problem', ], ], 'Project' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'defaultJobTimeoutMinutes' => [ 'shape' => 'JobTimeoutMinutes', ], 'created' => [ 'shape' => 'DateTime', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'Projects' => [ 'type' => 'list', 'member' => [ 'shape' => 'Project', ], ], 'PurchaseOfferingRequest' => [ 'type' => 'structure', 'required' => [ 'offeringId', 'quantity', ], 'members' => [ 'offeringId' => [ 'shape' => 'OfferingIdentifier', ], 'quantity' => [ 'shape' => 'Integer', ], 'offeringPromotionId' => [ 'shape' => 'OfferingPromotionIdentifier', ], ], ], 'PurchaseOfferingResult' => [ 'type' => 'structure', 'members' => [ 'offeringTransaction' => [ 'shape' => 'OfferingTransaction', ], ], ], 'PurchasedDevicesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DevicePlatform', ], 'value' => [ 'shape' => 'Integer', ], ], 'Radios' => [ 'type' => 'structure', 'members' => [ 'wifi' => [ 'shape' => 'Boolean', ], 'bluetooth' => [ 'shape' => 'Boolean', ], 'nfc' => [ 'shape' => 'Boolean', ], 'gps' => [ 'shape' => 'Boolean', ], ], ], 'RecurringCharge' => [ 'type' => 'structure', 'members' => [ 'cost' => [ 'shape' => 'MonetaryAmount', ], 'frequency' => [ 'shape' => 'RecurringChargeFrequency', ], ], ], 'RecurringChargeFrequency' => [ 'type' => 'string', 'enum' => [ 'MONTHLY', ], ], 'RecurringCharges' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecurringCharge', ], ], 'RemoteAccessSession' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'created' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'ExecutionStatus', ], 'result' => [ 'shape' => 'ExecutionResult', ], 'message' => [ 'shape' => 'Message', ], 'started' => [ 'shape' => 'DateTime', ], 'stopped' => [ 'shape' => 'DateTime', ], 'device' => [ 'shape' => 'Device', ], 'instanceArn' => [ 'shape' => 'AmazonResourceName', ], 'remoteDebugEnabled' => [ 'shape' => 'Boolean', ], 'remoteRecordEnabled' => [ 'shape' => 'Boolean', ], 'remoteRecordAppArn' => [ 'shape' => 'AmazonResourceName', ], 'hostAddress' => [ 'shape' => 'HostAddress', ], 'clientId' => [ 'shape' => 'ClientId', ], 'billingMethod' => [ 'shape' => 'BillingMethod', ], 'deviceMinutes' => [ 'shape' => 'DeviceMinutes', ], 'endpoint' => [ 'shape' => 'String', ], 'deviceUdid' => [ 'shape' => 'String', ], 'interactionMode' => [ 'shape' => 'InteractionMode', ], 'skipAppResign' => [ 'shape' => 'SkipAppResign', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'RemoteAccessSessions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemoteAccessSession', ], ], 'RenewOfferingRequest' => [ 'type' => 'structure', 'required' => [ 'offeringId', 'quantity', ], 'members' => [ 'offeringId' => [ 'shape' => 'OfferingIdentifier', ], 'quantity' => [ 'shape' => 'Integer', ], ], ], 'RenewOfferingResult' => [ 'type' => 'structure', 'members' => [ 'offeringTransaction' => [ 'shape' => 'OfferingTransaction', ], ], ], 'Resolution' => [ 'type' => 'structure', 'members' => [ 'width' => [ 'shape' => 'Integer', ], 'height' => [ 'shape' => 'Integer', ], ], ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ResourceId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ResourceName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Rule' => [ 'type' => 'structure', 'members' => [ 'attribute' => [ 'shape' => 'DeviceAttribute', ], 'operator' => [ 'shape' => 'RuleOperator', ], 'value' => [ 'shape' => 'String', ], ], ], 'RuleOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'LESS_THAN', 'LESS_THAN_OR_EQUALS', 'GREATER_THAN', 'GREATER_THAN_OR_EQUALS', 'IN', 'NOT_IN', 'CONTAINS', ], ], 'Rules' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], ], 'Run' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'TestType', ], 'platform' => [ 'shape' => 'DevicePlatform', ], 'created' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'ExecutionStatus', ], 'result' => [ 'shape' => 'ExecutionResult', ], 'started' => [ 'shape' => 'DateTime', ], 'stopped' => [ 'shape' => 'DateTime', ], 'counters' => [ 'shape' => 'Counters', ], 'message' => [ 'shape' => 'Message', ], 'totalJobs' => [ 'shape' => 'Integer', ], 'completedJobs' => [ 'shape' => 'Integer', ], 'billingMethod' => [ 'shape' => 'BillingMethod', ], 'deviceMinutes' => [ 'shape' => 'DeviceMinutes', ], 'networkProfile' => [ 'shape' => 'NetworkProfile', ], 'parsingResultUrl' => [ 'shape' => 'String', ], 'resultCode' => [ 'shape' => 'ExecutionResultCode', ], 'seed' => [ 'shape' => 'Integer', ], 'appUpload' => [ 'shape' => 'AmazonResourceName', ], 'eventCount' => [ 'shape' => 'Integer', ], 'jobTimeoutMinutes' => [ 'shape' => 'JobTimeoutMinutes', ], 'devicePoolArn' => [ 'shape' => 'AmazonResourceName', ], 'locale' => [ 'shape' => 'String', ], 'radios' => [ 'shape' => 'Radios', ], 'location' => [ 'shape' => 'Location', ], 'customerArtifactPaths' => [ 'shape' => 'CustomerArtifactPaths', ], 'webUrl' => [ 'shape' => 'String', ], 'skipAppResign' => [ 'shape' => 'SkipAppResign', ], 'testSpecArn' => [ 'shape' => 'AmazonResourceName', ], 'deviceSelectionResult' => [ 'shape' => 'DeviceSelectionResult', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'Runs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Run', ], ], 'Sample' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'type' => [ 'shape' => 'SampleType', ], 'url' => [ 'shape' => 'URL', ], ], ], 'SampleType' => [ 'type' => 'string', 'enum' => [ 'CPU', 'MEMORY', 'THREADS', 'RX_RATE', 'TX_RATE', 'RX', 'TX', 'NATIVE_FRAMES', 'NATIVE_FPS', 'NATIVE_MIN_DRAWTIME', 'NATIVE_AVG_DRAWTIME', 'NATIVE_MAX_DRAWTIME', 'OPENGL_FRAMES', 'OPENGL_FPS', 'OPENGL_MIN_DRAWTIME', 'OPENGL_AVG_DRAWTIME', 'OPENGL_MAX_DRAWTIME', ], ], 'Samples' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sample', ], ], 'ScheduleRunConfiguration' => [ 'type' => 'structure', 'members' => [ 'extraDataPackageArn' => [ 'shape' => 'AmazonResourceName', ], 'networkProfileArn' => [ 'shape' => 'AmazonResourceName', ], 'locale' => [ 'shape' => 'String', ], 'location' => [ 'shape' => 'Location', ], 'vpceConfigurationArns' => [ 'shape' => 'AmazonResourceNames', ], 'customerArtifactPaths' => [ 'shape' => 'CustomerArtifactPaths', ], 'radios' => [ 'shape' => 'Radios', ], 'auxiliaryApps' => [ 'shape' => 'AmazonResourceNames', ], 'billingMethod' => [ 'shape' => 'BillingMethod', ], ], ], 'ScheduleRunRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'test', ], 'members' => [ 'projectArn' => [ 'shape' => 'AmazonResourceName', ], 'appArn' => [ 'shape' => 'AmazonResourceName', ], 'devicePoolArn' => [ 'shape' => 'AmazonResourceName', ], 'deviceSelectionConfiguration' => [ 'shape' => 'DeviceSelectionConfiguration', ], 'name' => [ 'shape' => 'Name', ], 'test' => [ 'shape' => 'ScheduleRunTest', ], 'configuration' => [ 'shape' => 'ScheduleRunConfiguration', ], 'executionConfiguration' => [ 'shape' => 'ExecutionConfiguration', ], ], ], 'ScheduleRunResult' => [ 'type' => 'structure', 'members' => [ 'run' => [ 'shape' => 'Run', ], ], ], 'ScheduleRunTest' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'TestType', ], 'testPackageArn' => [ 'shape' => 'AmazonResourceName', ], 'testSpecArn' => [ 'shape' => 'AmazonResourceName', ], 'filter' => [ 'shape' => 'Filter', ], 'parameters' => [ 'shape' => 'TestParameters', ], ], ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^sg-[0-9a-fA-F]{8,}$', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 5, 'min' => 1, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'SensitiveURL' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'sensitive' => true, ], 'ServiceAccountException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ServiceDnsName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'SkipAppResign' => [ 'type' => 'boolean', ], 'SshPublicKey' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, ], 'StopJobRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'StopJobResult' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StopRemoteAccessSessionRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'StopRemoteAccessSessionResult' => [ 'type' => 'structure', 'members' => [ 'remoteAccessSession' => [ 'shape' => 'RemoteAccessSession', ], ], ], 'StopRunRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'StopRunResult' => [ 'type' => 'structure', 'members' => [ 'run' => [ 'shape' => 'Run', ], ], ], 'String' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^subnet-[0-9a-fA-F]{8,}$', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 8, 'min' => 1, ], 'Suite' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'TestType', ], 'created' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'ExecutionStatus', ], 'result' => [ 'shape' => 'ExecutionResult', ], 'started' => [ 'shape' => 'DateTime', ], 'stopped' => [ 'shape' => 'DateTime', ], 'counters' => [ 'shape' => 'Counters', ], 'message' => [ 'shape' => 'Message', ], 'deviceMinutes' => [ 'shape' => 'DeviceMinutes', ], ], ], 'Suites' => [ 'type' => 'list', 'member' => [ 'shape' => 'Suite', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 150, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 150, ], 'TagOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'exception' => true, ], 'TagPolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'exception' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'DeviceFarmArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Test' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'TestType', ], 'created' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'ExecutionStatus', ], 'result' => [ 'shape' => 'ExecutionResult', ], 'started' => [ 'shape' => 'DateTime', ], 'stopped' => [ 'shape' => 'DateTime', ], 'counters' => [ 'shape' => 'Counters', ], 'message' => [ 'shape' => 'Message', ], 'deviceMinutes' => [ 'shape' => 'DeviceMinutes', ], ], ], 'TestGridProject' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'DeviceFarmArn', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'vpcConfig' => [ 'shape' => 'TestGridVpcConfig', ], 'created' => [ 'shape' => 'DateTime', ], ], ], 'TestGridProjects' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestGridProject', ], ], 'TestGridSession' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'DeviceFarmArn', ], 'status' => [ 'shape' => 'TestGridSessionStatus', ], 'created' => [ 'shape' => 'DateTime', ], 'ended' => [ 'shape' => 'DateTime', ], 'billingMinutes' => [ 'shape' => 'Double', ], 'seleniumProperties' => [ 'shape' => 'String', ], ], ], 'TestGridSessionAction' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'String', ], 'started' => [ 'shape' => 'DateTime', ], 'duration' => [ 'shape' => 'Long', ], 'statusCode' => [ 'shape' => 'String', ], 'requestMethod' => [ 'shape' => 'String', ], ], ], 'TestGridSessionActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestGridSessionAction', ], ], 'TestGridSessionArtifact' => [ 'type' => 'structure', 'members' => [ 'filename' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'TestGridSessionArtifactType', ], 'url' => [ 'shape' => 'SensitiveString', ], ], ], 'TestGridSessionArtifactCategory' => [ 'type' => 'string', 'enum' => [ 'VIDEO', 'LOG', ], ], 'TestGridSessionArtifactType' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'VIDEO', 'SELENIUM_LOG', ], ], 'TestGridSessionArtifacts' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestGridSessionArtifact', ], ], 'TestGridSessionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CLOSED', 'ERRORED', ], ], 'TestGridSessions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestGridSession', ], ], 'TestGridUrlExpiresInSecondsInput' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'TestGridVpcConfig' => [ 'type' => 'structure', 'required' => [ 'securityGroupIds', 'subnetIds', 'vpcId', ], 'members' => [ 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'subnetIds' => [ 'shape' => 'SubnetIds', ], 'vpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'TestParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TestType' => [ 'type' => 'string', 'enum' => [ 'BUILTIN_FUZZ', 'BUILTIN_EXPLORER', 'WEB_PERFORMANCE_PROFILE', 'APPIUM_JAVA_JUNIT', 'APPIUM_JAVA_TESTNG', 'APPIUM_PYTHON', 'APPIUM_NODE', 'APPIUM_RUBY', 'APPIUM_WEB_JAVA_JUNIT', 'APPIUM_WEB_JAVA_TESTNG', 'APPIUM_WEB_PYTHON', 'APPIUM_WEB_NODE', 'APPIUM_WEB_RUBY', 'CALABASH', 'INSTRUMENTATION', 'UIAUTOMATION', 'UIAUTOMATOR', 'XCTEST', 'XCTEST_UI', 'REMOTE_ACCESS_RECORD', 'REMOTE_ACCESS_REPLAY', ], ], 'Tests' => [ 'type' => 'list', 'member' => [ 'shape' => 'Test', ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'exception' => true, ], 'TransactionIdentifier' => [ 'type' => 'string', 'min' => 32, ], 'TrialMinutes' => [ 'type' => 'structure', 'members' => [ 'total' => [ 'shape' => 'Double', ], 'remaining' => [ 'shape' => 'Double', ], ], ], 'URL' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'UniqueProblem' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], 'problems' => [ 'shape' => 'Problems', ], ], ], 'UniqueProblems' => [ 'type' => 'list', 'member' => [ 'shape' => 'UniqueProblem', ], ], 'UniqueProblemsByExecutionResultMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExecutionResult', ], 'value' => [ 'shape' => 'UniqueProblems', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'DeviceFarmArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDeviceInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'profileArn' => [ 'shape' => 'AmazonResourceName', ], 'labels' => [ 'shape' => 'InstanceLabels', ], ], ], 'UpdateDeviceInstanceResult' => [ 'type' => 'structure', 'members' => [ 'deviceInstance' => [ 'shape' => 'DeviceInstance', ], ], ], 'UpdateDevicePoolRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'rules' => [ 'shape' => 'Rules', ], 'maxDevices' => [ 'shape' => 'Integer', ], 'clearMaxDevices' => [ 'shape' => 'Boolean', ], ], ], 'UpdateDevicePoolResult' => [ 'type' => 'structure', 'members' => [ 'devicePool' => [ 'shape' => 'DevicePool', ], ], ], 'UpdateInstanceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'packageCleanup' => [ 'shape' => 'Boolean', ], 'excludeAppPackagesFromCleanup' => [ 'shape' => 'PackageIds', ], 'rebootAfterUse' => [ 'shape' => 'Boolean', ], ], ], 'UpdateInstanceProfileResult' => [ 'type' => 'structure', 'members' => [ 'instanceProfile' => [ 'shape' => 'InstanceProfile', ], ], ], 'UpdateNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Message', ], 'type' => [ 'shape' => 'NetworkProfileType', ], 'uplinkBandwidthBits' => [ 'shape' => 'Long', ], 'downlinkBandwidthBits' => [ 'shape' => 'Long', ], 'uplinkDelayMs' => [ 'shape' => 'Long', ], 'downlinkDelayMs' => [ 'shape' => 'Long', ], 'uplinkJitterMs' => [ 'shape' => 'Long', ], 'downlinkJitterMs' => [ 'shape' => 'Long', ], 'uplinkLossPercent' => [ 'shape' => 'PercentInteger', ], 'downlinkLossPercent' => [ 'shape' => 'PercentInteger', ], ], ], 'UpdateNetworkProfileResult' => [ 'type' => 'structure', 'members' => [ 'networkProfile' => [ 'shape' => 'NetworkProfile', ], ], ], 'UpdateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'defaultJobTimeoutMinutes' => [ 'shape' => 'JobTimeoutMinutes', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'UpdateProjectResult' => [ 'type' => 'structure', 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'UpdateTestGridProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DeviceFarmArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'vpcConfig' => [ 'shape' => 'TestGridVpcConfig', ], ], ], 'UpdateTestGridProjectResult' => [ 'type' => 'structure', 'members' => [ 'testGridProject' => [ 'shape' => 'TestGridProject', ], ], ], 'UpdateUploadRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'contentType' => [ 'shape' => 'ContentType', ], 'editContent' => [ 'shape' => 'Boolean', ], ], ], 'UpdateUploadResult' => [ 'type' => 'structure', 'members' => [ 'upload' => [ 'shape' => 'Upload', ], ], ], 'UpdateVPCEConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'vpceConfigurationName' => [ 'shape' => 'VPCEConfigurationName', ], 'vpceServiceName' => [ 'shape' => 'VPCEServiceName', ], 'serviceDnsName' => [ 'shape' => 'ServiceDnsName', ], 'vpceConfigurationDescription' => [ 'shape' => 'VPCEConfigurationDescription', ], ], ], 'UpdateVPCEConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'vpceConfiguration' => [ 'shape' => 'VPCEConfiguration', ], ], ], 'Upload' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'name' => [ 'shape' => 'Name', ], 'created' => [ 'shape' => 'DateTime', ], 'type' => [ 'shape' => 'UploadType', ], 'status' => [ 'shape' => 'UploadStatus', ], 'url' => [ 'shape' => 'SensitiveURL', ], 'metadata' => [ 'shape' => 'Metadata', ], 'contentType' => [ 'shape' => 'ContentType', ], 'message' => [ 'shape' => 'Message', ], 'category' => [ 'shape' => 'UploadCategory', ], ], ], 'UploadCategory' => [ 'type' => 'string', 'enum' => [ 'CURATED', 'PRIVATE', ], ], 'UploadStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'PROCESSING', 'SUCCEEDED', 'FAILED', ], ], 'UploadType' => [ 'type' => 'string', 'enum' => [ 'ANDROID_APP', 'IOS_APP', 'WEB_APP', 'EXTERNAL_DATA', 'APPIUM_JAVA_JUNIT_TEST_PACKAGE', 'APPIUM_JAVA_TESTNG_TEST_PACKAGE', 'APPIUM_PYTHON_TEST_PACKAGE', 'APPIUM_NODE_TEST_PACKAGE', 'APPIUM_RUBY_TEST_PACKAGE', 'APPIUM_WEB_JAVA_JUNIT_TEST_PACKAGE', 'APPIUM_WEB_JAVA_TESTNG_TEST_PACKAGE', 'APPIUM_WEB_PYTHON_TEST_PACKAGE', 'APPIUM_WEB_NODE_TEST_PACKAGE', 'APPIUM_WEB_RUBY_TEST_PACKAGE', 'CALABASH_TEST_PACKAGE', 'INSTRUMENTATION_TEST_PACKAGE', 'UIAUTOMATION_TEST_PACKAGE', 'UIAUTOMATOR_TEST_PACKAGE', 'XCTEST_TEST_PACKAGE', 'XCTEST_UI_TEST_PACKAGE', 'APPIUM_JAVA_JUNIT_TEST_SPEC', 'APPIUM_JAVA_TESTNG_TEST_SPEC', 'APPIUM_PYTHON_TEST_SPEC', 'APPIUM_NODE_TEST_SPEC', 'APPIUM_RUBY_TEST_SPEC', 'APPIUM_WEB_JAVA_JUNIT_TEST_SPEC', 'APPIUM_WEB_JAVA_TESTNG_TEST_SPEC', 'APPIUM_WEB_PYTHON_TEST_SPEC', 'APPIUM_WEB_NODE_TEST_SPEC', 'APPIUM_WEB_RUBY_TEST_SPEC', 'INSTRUMENTATION_TEST_SPEC', 'XCTEST_UI_TEST_SPEC', ], ], 'Uploads' => [ 'type' => 'list', 'member' => [ 'shape' => 'Upload', ], ], 'VPCEConfiguration' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AmazonResourceName', ], 'vpceConfigurationName' => [ 'shape' => 'VPCEConfigurationName', ], 'vpceServiceName' => [ 'shape' => 'VPCEServiceName', ], 'serviceDnsName' => [ 'shape' => 'ServiceDnsName', ], 'vpceConfigurationDescription' => [ 'shape' => 'VPCEConfigurationDescription', ], ], ], 'VPCEConfigurationDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'VPCEConfigurationName' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'VPCEConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'VPCEConfiguration', ], ], 'VPCEServiceName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'VideoCapture' => [ 'type' => 'boolean', ], 'VpcConfig' => [ 'type' => 'structure', 'required' => [ 'securityGroupIds', 'subnetIds', 'vpcId', ], 'members' => [ 'securityGroupIds' => [ 'shape' => 'VpcSecurityGroupIds', ], 'subnetIds' => [ 'shape' => 'VpcSubnetIds', ], 'vpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'VpcSecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'VpcSubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 8, 'min' => 1, ], ],];
