{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at http://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file"], "hash": "5b2d87ea9e6c115194bd355b7b697cec", "packages": [], "packages-dev": [{"name": "phpunit/php-code-coverage", "version": "1.2.12", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "1.2.12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/1.2.12", "reference": "1.2.12", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": ">=1.3.0@stable", "phpunit/php-text-template": ">=1.1.1@stable", "phpunit/php-token-stream": ">=1.1.3@stable"}, "require-dev": {"phpunit/phpunit": "3.7.*@dev"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["PHP/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2013-07-06 06:26:16"}, {"name": "phpunit/php-file-iterator", "version": "1.3.3", "source": {"type": "git", "url": "git://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "1.3.3"}, "dist": {"type": "zip", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/zipball/1.3.3", "reference": "1.3.3", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["File/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "http://www.phpunit.de/", "keywords": ["filesystem", "iterator"], "time": "2012-10-11 04:44:38"}, {"name": "phpunit/php-text-template", "version": "1.1.4", "source": {"type": "git", "url": "git://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "1.1.4"}, "dist": {"type": "zip", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/zipball/1.1.4", "reference": "1.1.4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["Text/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2012-10-31 11:15:28"}, {"name": "phpunit/php-timer", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "1.0.5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/php-timer/zipball/1.0.5", "reference": "1.0.5", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["PHP/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2013-08-02 07:42:54"}, {"name": "phpunit/php-token-stream", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "1.2.0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/1.2.0", "reference": "1.2.0", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["PHP/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2013-08-04 05:57:48"}, {"name": "phpunit/phpunit", "version": "3.7.23", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "3.7.23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/3.7.23", "reference": "3.7.23", "shasum": ""}, "require": {"ext-dom": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpunit/php-code-coverage": "~1.2.1", "phpunit/php-file-iterator": ">=1.3.1", "phpunit/php-text-template": ">=1.1.1", "phpunit/php-timer": ">=1.0.4", "phpunit/phpunit-mock-objects": "~1.2.0", "symfony/yaml": "~2.0"}, "require-dev": {"pear-pear/pear": "1.9.4"}, "suggest": {"ext-json": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "phpunit/php-invoker": ">=1.1.0,<1.2.0"}, "bin": ["composer/bin/phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.7.x-dev"}}, "autoload": {"classmap": ["PHPUnit/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["", "../../symfony/yaml/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "http://www.phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2013-08-02 19:14:44"}, {"name": "phpunit/phpunit-mock-objects", "version": "1.2.3", "source": {"type": "git", "url": "git://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects.git", "reference": "1.2.3"}, "dist": {"type": "zip", "url": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/archive/1.2.3.zip", "reference": "1.2.3", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-text-template": ">=1.1.1@stable"}, "suggest": {"ext-soap": "*"}, "type": "library", "autoload": {"classmap": ["PHPUnit/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2013-01-13 10:24:48"}, {"name": "symfony/yaml", "version": "v2.3.2", "target-dir": "Symfony/Component/Yaml", "source": {"type": "git", "url": "https://github.com/symfony/Yaml.git", "reference": "v2.3.2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Yaml/zipball/v2.3.2", "reference": "v2.3.2", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-0": {"Symfony\\Component\\Yaml\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "http://symfony.com", "time": "2013-07-11 19:36:36"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "platform": {"php": ">=5.0.0"}, "platform-dev": []}