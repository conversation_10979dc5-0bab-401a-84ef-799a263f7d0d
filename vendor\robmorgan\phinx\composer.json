{"name": "robmorgan/phinx", "type": "library", "description": "Phinx makes it ridiculously easy to manage the database migrations for your PHP app.", "keywords": ["phinx", "migrations", "database", "db", "database migrations"], "homepage": "https://phinx.org", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://robmorgan.id.au", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://shadowhand.me", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=5.4", "symfony/console": "~2.8|~3.0", "symfony/config": "~2.8|~3.0", "symfony/yaml": "~2.8|~3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.26|^5.0"}, "autoload": {"psr-4": {"Phinx\\": "src/Phinx"}}, "autoload-dev": {"psr-4": {"Test\\Phinx\\": "tests/Phinx"}}, "bin": ["bin/phinx"]}