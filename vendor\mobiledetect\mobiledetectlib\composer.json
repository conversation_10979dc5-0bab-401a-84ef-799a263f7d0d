{"name": "mobiledetect/mobiledetectlib", "type": "library", "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "keywords": ["mobile", "mobile detect", "mobile detector", "php mobile detect", "detect mobile devices"], "homepage": "https://github.com/serbanghita/Mobile-Detect", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "*"}, "autoload": {"classmap": ["Mobile_Detect.php"], "psr-0": {"Detection": "namespaced/"}}}