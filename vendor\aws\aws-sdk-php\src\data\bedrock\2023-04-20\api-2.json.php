<?php
// This file was auto-generated from sdk-root/src/data/bedrock/2023-04-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-04-20', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'bedrock', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Bedrock', 'serviceId' => 'Bedrock', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-2023-04-20', ], 'operations' => [ 'CreateEvaluationJob' => [ 'name' => 'CreateEvaluationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/evaluation-jobs', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateEvaluationJobRequest', ], 'output' => [ 'shape' => 'CreateEvaluationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateGuardrail' => [ 'name' => 'CreateGuardrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/guardrails', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateGuardrailRequest', ], 'output' => [ 'shape' => 'CreateGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateGuardrailVersion' => [ 'name' => 'CreateGuardrailVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/guardrails/{guardrailIdentifier}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateGuardrailVersionRequest', ], 'output' => [ 'shape' => 'CreateGuardrailVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateModelCopyJob' => [ 'name' => 'CreateModelCopyJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/model-copy-jobs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateModelCopyJobRequest', ], 'output' => [ 'shape' => 'CreateModelCopyJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'CreateModelCustomizationJob' => [ 'name' => 'CreateModelCustomizationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/model-customization-jobs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateModelCustomizationJobRequest', ], 'output' => [ 'shape' => 'CreateModelCustomizationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateModelInvocationJob' => [ 'name' => 'CreateModelInvocationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/model-invocation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateModelInvocationJobRequest', ], 'output' => [ 'shape' => 'CreateModelInvocationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateProvisionedModelThroughput' => [ 'name' => 'CreateProvisionedModelThroughput', 'http' => [ 'method' => 'POST', 'requestUri' => '/provisioned-model-throughput', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'CreateProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteCustomModel' => [ 'name' => 'DeleteCustomModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/custom-models/{modelIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCustomModelRequest', ], 'output' => [ 'shape' => 'DeleteCustomModelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteGuardrail' => [ 'name' => 'DeleteGuardrail', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/guardrails/{guardrailIdentifier}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteGuardrailRequest', ], 'output' => [ 'shape' => 'DeleteGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteModelInvocationLoggingConfiguration' => [ 'name' => 'DeleteModelInvocationLoggingConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/logging/modelinvocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteModelInvocationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteModelInvocationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteProvisionedModelThroughput' => [ 'name' => 'DeleteProvisionedModelThroughput', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/provisioned-model-throughput/{provisionedModelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'DeleteProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'GetCustomModel' => [ 'name' => 'GetCustomModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-models/{modelIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCustomModelRequest', ], 'output' => [ 'shape' => 'GetCustomModelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEvaluationJob' => [ 'name' => 'GetEvaluationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/evaluation-jobs/{jobIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEvaluationJobRequest', ], 'output' => [ 'shape' => 'GetEvaluationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFoundationModel' => [ 'name' => 'GetFoundationModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/foundation-models/{modelIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFoundationModelRequest', ], 'output' => [ 'shape' => 'GetFoundationModelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetGuardrail' => [ 'name' => 'GetGuardrail', 'http' => [ 'method' => 'GET', 'requestUri' => '/guardrails/{guardrailIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGuardrailRequest', ], 'output' => [ 'shape' => 'GetGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetModelCopyJob' => [ 'name' => 'GetModelCopyJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-copy-jobs/{jobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetModelCopyJobRequest', ], 'output' => [ 'shape' => 'GetModelCopyJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetModelCustomizationJob' => [ 'name' => 'GetModelCustomizationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-customization-jobs/{jobIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetModelCustomizationJobRequest', ], 'output' => [ 'shape' => 'GetModelCustomizationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetModelInvocationJob' => [ 'name' => 'GetModelInvocationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-invocation-job/{jobIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetModelInvocationJobRequest', ], 'output' => [ 'shape' => 'GetModelInvocationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetModelInvocationLoggingConfiguration' => [ 'name' => 'GetModelInvocationLoggingConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/logging/modelinvocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetModelInvocationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'GetModelInvocationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetProvisionedModelThroughput' => [ 'name' => 'GetProvisionedModelThroughput', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioned-model-throughput/{provisionedModelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'GetProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListCustomModels' => [ 'name' => 'ListCustomModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-models', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCustomModelsRequest', ], 'output' => [ 'shape' => 'ListCustomModelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListEvaluationJobs' => [ 'name' => 'ListEvaluationJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/evaluation-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEvaluationJobsRequest', ], 'output' => [ 'shape' => 'ListEvaluationJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFoundationModels' => [ 'name' => 'ListFoundationModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/foundation-models', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFoundationModelsRequest', ], 'output' => [ 'shape' => 'ListFoundationModelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListGuardrails' => [ 'name' => 'ListGuardrails', 'http' => [ 'method' => 'GET', 'requestUri' => '/guardrails', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGuardrailsRequest', ], 'output' => [ 'shape' => 'ListGuardrailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListModelCopyJobs' => [ 'name' => 'ListModelCopyJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-copy-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListModelCopyJobsRequest', ], 'output' => [ 'shape' => 'ListModelCopyJobsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListModelCustomizationJobs' => [ 'name' => 'ListModelCustomizationJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-customization-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListModelCustomizationJobsRequest', ], 'output' => [ 'shape' => 'ListModelCustomizationJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListModelInvocationJobs' => [ 'name' => 'ListModelInvocationJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-invocation-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListModelInvocationJobsRequest', ], 'output' => [ 'shape' => 'ListModelInvocationJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListProvisionedModelThroughputs' => [ 'name' => 'ListProvisionedModelThroughputs', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioned-model-throughputs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProvisionedModelThroughputsRequest', ], 'output' => [ 'shape' => 'ListProvisionedModelThroughputsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/listTagsForResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutModelInvocationLoggingConfiguration' => [ 'name' => 'PutModelInvocationLoggingConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/logging/modelinvocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutModelInvocationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'PutModelInvocationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StopEvaluationJob' => [ 'name' => 'StopEvaluationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/evaluation-job/{jobIdentifier}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopEvaluationJobRequest', ], 'output' => [ 'shape' => 'StopEvaluationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StopModelCustomizationJob' => [ 'name' => 'StopModelCustomizationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/model-customization-jobs/{jobIdentifier}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopModelCustomizationJobRequest', ], 'output' => [ 'shape' => 'StopModelCustomizationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StopModelInvocationJob' => [ 'name' => 'StopModelInvocationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/model-invocation-job/{jobIdentifier}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopModelInvocationJobRequest', ], 'output' => [ 'shape' => 'StopModelInvocationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tagResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/untagResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateGuardrail' => [ 'name' => 'UpdateGuardrail', 'http' => [ 'method' => 'PUT', 'requestUri' => '/guardrails/{guardrailIdentifier}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateGuardrailRequest', ], 'output' => [ 'shape' => 'UpdateGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateProvisionedModelThroughput' => [ 'name' => 'UpdateProvisionedModelThroughput', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/provisioned-model-throughput/{provisionedModelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'UpdateProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'AutomatedEvaluationConfig' => [ 'type' => 'structure', 'required' => [ 'datasetMetricConfigs', ], 'members' => [ 'datasetMetricConfigs' => [ 'shape' => 'EvaluationDatasetMetricConfigs', ], ], ], 'BaseModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2})))|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})|(([0-9a-zA-Z][_-]?)+)', ], 'BedrockModelId' => [ 'type' => 'string', 'max' => 140, 'min' => 0, 'pattern' => '[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}(/[a-z0-9]{12}|)', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BrandedName' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '.*', ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'CloudWatchConfig' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'roleArn', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'largeDataDeliveryS3Config' => [ 'shape' => 'S3Config', ], ], ], 'CommitmentDuration' => [ 'type' => 'string', 'enum' => [ 'OneMonth', 'SixMonths', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CreateEvaluationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'roleArn', 'evaluationConfig', 'inferenceConfig', 'outputDataConfig', ], 'members' => [ 'jobName' => [ 'shape' => 'EvaluationJobName', ], 'jobDescription' => [ 'shape' => 'EvaluationJobDescription', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'customerEncryptionKeyId' => [ 'shape' => 'KmsKeyId', ], 'jobTags' => [ 'shape' => 'TagList', ], 'evaluationConfig' => [ 'shape' => 'EvaluationConfig', ], 'inferenceConfig' => [ 'shape' => 'EvaluationInferenceConfig', ], 'outputDataConfig' => [ 'shape' => 'EvaluationOutputDataConfig', ], ], ], 'CreateEvaluationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'EvaluationJobArn', ], ], ], 'CreateGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'blockedInputMessaging', 'blockedOutputsMessaging', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailName', ], 'description' => [ 'shape' => 'GuardrailDescription', ], 'topicPolicyConfig' => [ 'shape' => 'GuardrailTopicPolicyConfig', ], 'contentPolicyConfig' => [ 'shape' => 'GuardrailContentPolicyConfig', ], 'wordPolicyConfig' => [ 'shape' => 'GuardrailWordPolicyConfig', ], 'sensitiveInformationPolicyConfig' => [ 'shape' => 'GuardrailSensitiveInformationPolicyConfig', ], 'contextualGroundingPolicyConfig' => [ 'shape' => 'GuardrailContextualGroundingPolicyConfig', ], 'blockedInputMessaging' => [ 'shape' => 'GuardrailBlockedMessaging', ], 'blockedOutputsMessaging' => [ 'shape' => 'GuardrailBlockedMessaging', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'tags' => [ 'shape' => 'TagList', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'CreateGuardrailResponse' => [ 'type' => 'structure', 'required' => [ 'guardrailId', 'guardrailArn', 'version', 'createdAt', ], 'members' => [ 'guardrailId' => [ 'shape' => 'GuardrailId', ], 'guardrailArn' => [ 'shape' => 'GuardrailArn', ], 'version' => [ 'shape' => 'GuardrailDraftVersion', ], 'createdAt' => [ 'shape' => 'Timestamp', ], ], ], 'CreateGuardrailVersionRequest' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'uri', 'locationName' => 'guardrailIdentifier', ], 'description' => [ 'shape' => 'GuardrailDescription', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'CreateGuardrailVersionResponse' => [ 'type' => 'structure', 'required' => [ 'guardrailId', 'version', ], 'members' => [ 'guardrailId' => [ 'shape' => 'GuardrailId', ], 'version' => [ 'shape' => 'GuardrailNumericalVersion', ], ], ], 'CreateModelCopyJobRequest' => [ 'type' => 'structure', 'required' => [ 'sourceModelArn', 'targetModelName', ], 'members' => [ 'sourceModelArn' => [ 'shape' => 'ModelArn', ], 'targetModelName' => [ 'shape' => 'CustomModelName', ], 'modelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'targetModelTags' => [ 'shape' => 'TagList', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'CreateModelCopyJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCopyJobArn', ], ], ], 'CreateModelCustomizationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'customModelName', 'roleArn', 'baseModelIdentifier', 'trainingDataConfig', 'outputDataConfig', 'hyperParameters', ], 'members' => [ 'jobName' => [ 'shape' => 'JobName', ], 'customModelName' => [ 'shape' => 'CustomModelName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'baseModelIdentifier' => [ 'shape' => 'BaseModelIdentifier', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], 'customModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'jobTags' => [ 'shape' => 'TagList', ], 'customModelTags' => [ 'shape' => 'TagList', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'validationDataConfig' => [ 'shape' => 'ValidationDataConfig', ], 'outputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'hyperParameters' => [ 'shape' => 'ModelCustomizationHyperParameters', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'CreateModelCustomizationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], ], ], 'CreateModelInvocationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'roleArn', 'modelId', 'inputDataConfig', 'outputDataConfig', ], 'members' => [ 'jobName' => [ 'shape' => 'ModelInvocationJobName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientRequestToken' => [ 'shape' => 'ModelInvocationIdempotencyToken', 'idempotencyToken' => true, ], 'modelId' => [ 'shape' => 'ModelId', ], 'inputDataConfig' => [ 'shape' => 'ModelInvocationJobInputDataConfig', ], 'outputDataConfig' => [ 'shape' => 'ModelInvocationJobOutputDataConfig', ], 'timeoutDurationInHours' => [ 'shape' => 'ModelInvocationJobTimeoutDurationInHours', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateModelInvocationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelInvocationJobArn', ], ], ], 'CreateProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'modelUnits', 'provisionedModelName', 'modelId', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'modelUnits' => [ 'shape' => 'PositiveInteger', ], 'provisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'modelId' => [ 'shape' => 'ModelIdentifier', ], 'commitmentDuration' => [ 'shape' => 'CommitmentDuration', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'required' => [ 'provisionedModelArn', ], 'members' => [ 'provisionedModelArn' => [ 'shape' => 'ProvisionedModelArn', ], ], ], 'CustomModelArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}/[a-z0-9]{12}', ], 'CustomModelName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '([0-9a-zA-Z][_-]?){1,63}', ], 'CustomModelSummary' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelName', 'creationTime', 'baseModelArn', 'baseModelName', ], 'members' => [ 'modelArn' => [ 'shape' => 'CustomModelArn', ], 'modelName' => [ 'shape' => 'CustomModelName', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'baseModelArn' => [ 'shape' => 'ModelArn', ], 'baseModelName' => [ 'shape' => 'ModelName', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], ], ], 'CustomModelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomModelSummary', ], ], 'CustomizationType' => [ 'type' => 'string', 'enum' => [ 'FINE_TUNING', 'CONTINUED_PRE_TRAINING', ], ], 'DeleteCustomModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelIdentifier', ], 'members' => [ 'modelIdentifier' => [ 'shape' => 'ModelIdentifier', 'location' => 'uri', 'locationName' => 'modelIdentifier', ], ], ], 'DeleteCustomModelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'uri', 'locationName' => 'guardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailNumericalVersion', 'location' => 'querystring', 'locationName' => 'guardrailVersion', ], ], ], 'DeleteGuardrailResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteModelInvocationLoggingConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteModelInvocationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'provisionedModelId', ], 'members' => [ 'provisionedModelId' => [ 'shape' => 'ProvisionedModelId', 'location' => 'uri', 'locationName' => 'provisionedModelId', ], ], ], 'DeleteProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'members' => [], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'ErrorMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorMessage', ], 'max' => 20, 'min' => 0, ], 'EvaluationBedrockModel' => [ 'type' => 'structure', 'required' => [ 'modelIdentifier', 'inferenceParams', ], 'members' => [ 'modelIdentifier' => [ 'shape' => 'EvaluationModelIdentifier', ], 'inferenceParams' => [ 'shape' => 'EvaluationModelInferenceParams', ], ], ], 'EvaluationConfig' => [ 'type' => 'structure', 'members' => [ 'automated' => [ 'shape' => 'AutomatedEvaluationConfig', ], 'human' => [ 'shape' => 'HumanEvaluationConfig', ], ], 'union' => true, ], 'EvaluationDataset' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'EvaluationDatasetName', ], 'datasetLocation' => [ 'shape' => 'EvaluationDatasetLocation', ], ], ], 'EvaluationDatasetLocation' => [ 'type' => 'structure', 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], 'union' => true, ], 'EvaluationDatasetMetricConfig' => [ 'type' => 'structure', 'required' => [ 'taskType', 'dataset', 'metricNames', ], 'members' => [ 'taskType' => [ 'shape' => 'EvaluationTaskType', ], 'dataset' => [ 'shape' => 'EvaluationDataset', ], 'metricNames' => [ 'shape' => 'EvaluationMetricNames', ], ], ], 'EvaluationDatasetMetricConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationDatasetMetricConfig', ], 'max' => 5, 'min' => 1, ], 'EvaluationDatasetName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[0-9a-zA-Z-_.]+', 'sensitive' => true, ], 'EvaluationInferenceConfig' => [ 'type' => 'structure', 'members' => [ 'models' => [ 'shape' => 'EvaluationModelConfigs', ], ], 'union' => true, ], 'EvaluationJobArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:evaluation-job/[a-z0-9]{12}', ], 'EvaluationJobDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.+', 'sensitive' => true, ], 'EvaluationJobIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:evaluation-job/[a-z0-9]{12})', 'sensitive' => true, ], 'EvaluationJobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-z0-9](-*[a-z0-9]){0,62}', ], 'EvaluationJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'EvaluationJobType' => [ 'type' => 'string', 'enum' => [ 'Human', 'Automated', ], ], 'EvaluationMetricDescription' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '.+', 'sensitive' => true, ], 'EvaluationMetricName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[0-9a-zA-Z-_.]+', 'sensitive' => true, ], 'EvaluationMetricNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationMetricName', ], 'max' => 10, 'min' => 1, ], 'EvaluationModelConfig' => [ 'type' => 'structure', 'members' => [ 'bedrockModel' => [ 'shape' => 'EvaluationBedrockModel', ], ], 'union' => true, ], 'EvaluationModelConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationModelConfig', ], 'max' => 2, 'min' => 1, ], 'EvaluationModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}(([:][a-z0-9-]{1,63}){0,2})?/[a-z0-9]{12})|(:foundation-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})))|(([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2}))|(([0-9a-zA-Z][_-]?)+)', ], 'EvaluationModelIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationModelIdentifier', ], 'max' => 2, 'min' => 1, ], 'EvaluationModelInferenceParams' => [ 'type' => 'string', 'max' => 1023, 'min' => 1, 'sensitive' => true, ], 'EvaluationOutputDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'EvaluationRatingMethod' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[0-9a-zA-Z-_]+', ], 'EvaluationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationSummary', ], 'max' => 5, 'min' => 1, ], 'EvaluationSummary' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'jobName', 'status', 'creationTime', 'jobType', 'evaluationTaskTypes', 'modelIdentifiers', ], 'members' => [ 'jobArn' => [ 'shape' => 'EvaluationJobArn', ], 'jobName' => [ 'shape' => 'EvaluationJobName', ], 'status' => [ 'shape' => 'EvaluationJobStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'jobType' => [ 'shape' => 'EvaluationJobType', ], 'evaluationTaskTypes' => [ 'shape' => 'EvaluationTaskTypes', ], 'modelIdentifiers' => [ 'shape' => 'EvaluationModelIdentifiers', ], ], ], 'EvaluationTaskType' => [ 'type' => 'string', 'enum' => [ 'Summarization', 'Classification', 'QuestionAndAnswer', 'Generation', 'Custom', ], 'max' => 63, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'EvaluationTaskTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationTaskType', ], 'max' => 5, 'min' => 1, ], 'FineTuningJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'FoundationModelArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/[a-z0-9-]{1,63}[.]{1}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}', ], 'FoundationModelDetails' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelId', ], 'members' => [ 'modelArn' => [ 'shape' => 'FoundationModelArn', ], 'modelId' => [ 'shape' => 'BedrockModelId', ], 'modelName' => [ 'shape' => 'BrandedName', ], 'providerName' => [ 'shape' => 'BrandedName', ], 'inputModalities' => [ 'shape' => 'ModelModalityList', ], 'outputModalities' => [ 'shape' => 'ModelModalityList', ], 'responseStreamingSupported' => [ 'shape' => 'Boolean', ], 'customizationsSupported' => [ 'shape' => 'ModelCustomizationList', ], 'inferenceTypesSupported' => [ 'shape' => 'InferenceTypeList', ], 'modelLifecycle' => [ 'shape' => 'FoundationModelLifecycle', ], ], ], 'FoundationModelLifecycle' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'FoundationModelLifecycleStatus', ], ], ], 'FoundationModelLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'LEGACY', ], ], 'FoundationModelSummary' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelId', ], 'members' => [ 'modelArn' => [ 'shape' => 'FoundationModelArn', ], 'modelId' => [ 'shape' => 'BedrockModelId', ], 'modelName' => [ 'shape' => 'BrandedName', ], 'providerName' => [ 'shape' => 'BrandedName', ], 'inputModalities' => [ 'shape' => 'ModelModalityList', ], 'outputModalities' => [ 'shape' => 'ModelModalityList', ], 'responseStreamingSupported' => [ 'shape' => 'Boolean', ], 'customizationsSupported' => [ 'shape' => 'ModelCustomizationList', ], 'inferenceTypesSupported' => [ 'shape' => 'InferenceTypeList', ], 'modelLifecycle' => [ 'shape' => 'FoundationModelLifecycle', ], ], ], 'FoundationModelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FoundationModelSummary', ], ], 'GetCustomModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelIdentifier', ], 'members' => [ 'modelIdentifier' => [ 'shape' => 'ModelIdentifier', 'location' => 'uri', 'locationName' => 'modelIdentifier', ], ], ], 'GetCustomModelResponse' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelName', 'jobArn', 'baseModelArn', 'trainingDataConfig', 'outputDataConfig', 'creationTime', ], 'members' => [ 'modelArn' => [ 'shape' => 'ModelArn', ], 'modelName' => [ 'shape' => 'CustomModelName', ], 'jobName' => [ 'shape' => 'JobName', ], 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], 'baseModelArn' => [ 'shape' => 'ModelArn', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], 'modelKmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'hyperParameters' => [ 'shape' => 'ModelCustomizationHyperParameters', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'validationDataConfig' => [ 'shape' => 'ValidationDataConfig', ], 'outputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'trainingMetrics' => [ 'shape' => 'TrainingMetrics', ], 'validationMetrics' => [ 'shape' => 'ValidationMetrics', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetEvaluationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'EvaluationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'GetEvaluationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobName', 'status', 'jobArn', 'roleArn', 'jobType', 'evaluationConfig', 'inferenceConfig', 'outputDataConfig', 'creationTime', ], 'members' => [ 'jobName' => [ 'shape' => 'EvaluationJobName', ], 'status' => [ 'shape' => 'EvaluationJobStatus', ], 'jobArn' => [ 'shape' => 'EvaluationJobArn', ], 'jobDescription' => [ 'shape' => 'EvaluationJobDescription', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'customerEncryptionKeyId' => [ 'shape' => 'KmsKeyId', ], 'jobType' => [ 'shape' => 'EvaluationJobType', ], 'evaluationConfig' => [ 'shape' => 'EvaluationConfig', ], 'inferenceConfig' => [ 'shape' => 'EvaluationInferenceConfig', ], 'outputDataConfig' => [ 'shape' => 'EvaluationOutputDataConfig', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'failureMessages' => [ 'shape' => 'ErrorMessages', ], ], ], 'GetFoundationModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelIdentifier', ], 'members' => [ 'modelIdentifier' => [ 'shape' => 'ModelIdentifier', 'location' => 'uri', 'locationName' => 'modelIdentifier', ], ], ], 'GetFoundationModelResponse' => [ 'type' => 'structure', 'members' => [ 'modelDetails' => [ 'shape' => 'FoundationModelDetails', ], ], ], 'GetGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'uri', 'locationName' => 'guardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', 'location' => 'querystring', 'locationName' => 'guardrailVersion', ], ], ], 'GetGuardrailResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'guardrailId', 'guardrailArn', 'version', 'status', 'createdAt', 'updatedAt', 'blockedInputMessaging', 'blockedOutputsMessaging', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailName', ], 'description' => [ 'shape' => 'GuardrailDescription', ], 'guardrailId' => [ 'shape' => 'GuardrailId', ], 'guardrailArn' => [ 'shape' => 'GuardrailArn', ], 'version' => [ 'shape' => 'GuardrailVersion', ], 'status' => [ 'shape' => 'GuardrailStatus', ], 'topicPolicy' => [ 'shape' => 'GuardrailTopicPolicy', ], 'contentPolicy' => [ 'shape' => 'GuardrailContentPolicy', ], 'wordPolicy' => [ 'shape' => 'GuardrailWordPolicy', ], 'sensitiveInformationPolicy' => [ 'shape' => 'GuardrailSensitiveInformationPolicy', ], 'contextualGroundingPolicy' => [ 'shape' => 'GuardrailContextualGroundingPolicy', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'statusReasons' => [ 'shape' => 'GuardrailStatusReasons', ], 'failureRecommendations' => [ 'shape' => 'GuardrailFailureRecommendations', ], 'blockedInputMessaging' => [ 'shape' => 'GuardrailBlockedMessaging', ], 'blockedOutputsMessaging' => [ 'shape' => 'GuardrailBlockedMessaging', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'GetModelCopyJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCopyJobArn', 'location' => 'uri', 'locationName' => 'jobArn', ], ], ], 'GetModelCopyJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'status', 'creationTime', 'targetModelArn', 'sourceAccountId', 'sourceModelArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCopyJobArn', ], 'status' => [ 'shape' => 'ModelCopyJobStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'targetModelArn' => [ 'shape' => 'CustomModelArn', ], 'targetModelName' => [ 'shape' => 'CustomModelName', ], 'sourceAccountId' => [ 'shape' => 'AccountId', ], 'sourceModelArn' => [ 'shape' => 'ModelArn', ], 'targetModelKmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'targetModelTags' => [ 'shape' => 'TagList', ], 'failureMessage' => [ 'shape' => 'ErrorMessage', ], 'sourceModelName' => [ 'shape' => 'CustomModelName', ], ], ], 'GetModelCustomizationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'ModelCustomizationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'GetModelCustomizationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'jobName', 'outputModelName', 'roleArn', 'creationTime', 'baseModelArn', 'hyperParameters', 'trainingDataConfig', 'validationDataConfig', 'outputDataConfig', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], 'jobName' => [ 'shape' => 'JobName', ], 'outputModelName' => [ 'shape' => 'CustomModelName', ], 'outputModelArn' => [ 'shape' => 'CustomModelArn', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ModelCustomizationJobStatus', ], 'failureMessage' => [ 'shape' => 'ErrorMessage', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'baseModelArn' => [ 'shape' => 'FoundationModelArn', ], 'hyperParameters' => [ 'shape' => 'ModelCustomizationHyperParameters', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'validationDataConfig' => [ 'shape' => 'ValidationDataConfig', ], 'outputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], 'outputModelKmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'trainingMetrics' => [ 'shape' => 'TrainingMetrics', ], 'validationMetrics' => [ 'shape' => 'ValidationMetrics', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'GetModelInvocationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'ModelInvocationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'GetModelInvocationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'modelId', 'roleArn', 'submitTime', 'inputDataConfig', 'outputDataConfig', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelInvocationJobArn', ], 'jobName' => [ 'shape' => 'ModelInvocationJobName', ], 'modelId' => [ 'shape' => 'ModelId', ], 'clientRequestToken' => [ 'shape' => 'ModelInvocationIdempotencyToken', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ModelInvocationJobStatus', ], 'message' => [ 'shape' => 'Message', ], 'submitTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'inputDataConfig' => [ 'shape' => 'ModelInvocationJobInputDataConfig', ], 'outputDataConfig' => [ 'shape' => 'ModelInvocationJobOutputDataConfig', ], 'timeoutDurationInHours' => [ 'shape' => 'ModelInvocationJobTimeoutDurationInHours', ], 'jobExpirationTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetModelInvocationLoggingConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetModelInvocationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], ], ], 'GetProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'provisionedModelId', ], 'members' => [ 'provisionedModelId' => [ 'shape' => 'ProvisionedModelId', 'location' => 'uri', 'locationName' => 'provisionedModelId', ], ], ], 'GetProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'required' => [ 'modelUnits', 'desiredModelUnits', 'provisionedModelName', 'provisionedModelArn', 'modelArn', 'desiredModelArn', 'foundationModelArn', 'status', 'creationTime', 'lastModifiedTime', ], 'members' => [ 'modelUnits' => [ 'shape' => 'PositiveInteger', ], 'desiredModelUnits' => [ 'shape' => 'PositiveInteger', ], 'provisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'provisionedModelArn' => [ 'shape' => 'ProvisionedModelArn', ], 'modelArn' => [ 'shape' => 'ModelArn', ], 'desiredModelArn' => [ 'shape' => 'ModelArn', ], 'foundationModelArn' => [ 'shape' => 'FoundationModelArn', ], 'status' => [ 'shape' => 'ProvisionedModelStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'failureMessage' => [ 'shape' => 'ErrorMessage', ], 'commitmentDuration' => [ 'shape' => 'CommitmentDuration', ], 'commitmentExpirationTime' => [ 'shape' => 'Timestamp', ], ], ], 'GuardrailArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+', ], 'GuardrailBlockedMessaging' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'sensitive' => true, ], 'GuardrailContentFilter' => [ 'type' => 'structure', 'required' => [ 'type', 'inputStrength', 'outputStrength', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContentFilterType', ], 'inputStrength' => [ 'shape' => 'GuardrailFilterStrength', ], 'outputStrength' => [ 'shape' => 'GuardrailFilterStrength', ], ], ], 'GuardrailContentFilterConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'inputStrength', 'outputStrength', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContentFilterType', ], 'inputStrength' => [ 'shape' => 'GuardrailFilterStrength', ], 'outputStrength' => [ 'shape' => 'GuardrailFilterStrength', ], ], ], 'GuardrailContentFilterType' => [ 'type' => 'string', 'enum' => [ 'SEXUAL', 'VIOLENCE', 'HATE', 'INSULTS', 'MISCONDUCT', 'PROMPT_ATTACK', ], ], 'GuardrailContentFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentFilter', ], 'max' => 6, 'min' => 1, ], 'GuardrailContentFiltersConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentFilterConfig', ], 'max' => 6, 'min' => 1, ], 'GuardrailContentPolicy' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'GuardrailContentFilters', ], ], ], 'GuardrailContentPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'filtersConfig', ], 'members' => [ 'filtersConfig' => [ 'shape' => 'GuardrailContentFiltersConfig', ], ], ], 'GuardrailContextualGroundingFilter' => [ 'type' => 'structure', 'required' => [ 'type', 'threshold', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContextualGroundingFilterType', ], 'threshold' => [ 'shape' => 'GuardrailContextualGroundingFilterThresholdDouble', ], ], ], 'GuardrailContextualGroundingFilterConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'threshold', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContextualGroundingFilterType', ], 'threshold' => [ 'shape' => 'GuardrailContextualGroundingFilterConfigThresholdDouble', ], ], ], 'GuardrailContextualGroundingFilterConfigThresholdDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'GuardrailContextualGroundingFilterThresholdDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'GuardrailContextualGroundingFilterType' => [ 'type' => 'string', 'enum' => [ 'GROUNDING', 'RELEVANCE', ], ], 'GuardrailContextualGroundingFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContextualGroundingFilter', ], 'min' => 1, ], 'GuardrailContextualGroundingFiltersConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContextualGroundingFilterConfig', ], 'min' => 1, ], 'GuardrailContextualGroundingPolicy' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'GuardrailContextualGroundingFilters', ], ], ], 'GuardrailContextualGroundingPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'filtersConfig', ], 'members' => [ 'filtersConfig' => [ 'shape' => 'GuardrailContextualGroundingFiltersConfig', ], ], ], 'GuardrailDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'GuardrailDraftVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => 'DRAFT', ], 'GuardrailFailureRecommendation' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'GuardrailFailureRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailFailureRecommendation', ], 'max' => 100, 'min' => 0, ], 'GuardrailFilterStrength' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', ], ], 'GuardrailId' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[a-z0-9]+', ], 'GuardrailIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(([a-z0-9]+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+))', ], 'GuardrailManagedWordLists' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailManagedWords', ], ], 'GuardrailManagedWordListsConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailManagedWordsConfig', ], ], 'GuardrailManagedWords' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailManagedWordsType', ], ], ], 'GuardrailManagedWordsConfig' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailManagedWordsType', ], ], ], 'GuardrailManagedWordsType' => [ 'type' => 'string', 'enum' => [ 'PROFANITY', ], ], 'GuardrailName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[0-9a-zA-Z-_]+', 'sensitive' => true, ], 'GuardrailNumericalVersion' => [ 'type' => 'string', 'pattern' => '[1-9][0-9]{0,7}', ], 'GuardrailPiiEntities' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPiiEntity', ], 'min' => 1, ], 'GuardrailPiiEntitiesConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPiiEntityConfig', ], 'min' => 1, ], 'GuardrailPiiEntity' => [ 'type' => 'structure', 'required' => [ 'type', 'action', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailPiiEntityType', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationAction', ], ], ], 'GuardrailPiiEntityConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'action', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailPiiEntityType', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationAction', ], ], ], 'GuardrailPiiEntityType' => [ 'type' => 'string', 'enum' => [ 'ADDRESS', 'AGE', 'AWS_ACCESS_KEY', 'AWS_SECRET_KEY', 'CA_HEALTH_NUMBER', 'CA_SOCIAL_INSURANCE_NUMBER', 'CREDIT_DEBIT_CARD_CVV', 'CREDIT_DEBIT_CARD_EXPIRY', 'CREDIT_DEBIT_CARD_NUMBER', 'DRIVER_ID', 'EMAIL', 'INTERNATIONAL_BANK_ACCOUNT_NUMBER', 'IP_ADDRESS', 'LICENSE_PLATE', 'MAC_ADDRESS', 'NAME', 'PASSWORD', 'PHONE', 'PIN', 'SWIFT_CODE', 'UK_NATIONAL_HEALTH_SERVICE_NUMBER', 'UK_NATIONAL_INSURANCE_NUMBER', 'UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER', 'URL', 'USERNAME', 'US_BANK_ACCOUNT_NUMBER', 'US_BANK_ROUTING_NUMBER', 'US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER', 'US_PASSPORT_NUMBER', 'US_SOCIAL_SECURITY_NUMBER', 'VEHICLE_IDENTIFICATION_NUMBER', ], ], 'GuardrailRegex' => [ 'type' => 'structure', 'required' => [ 'name', 'pattern', 'action', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailRegexNameString', ], 'description' => [ 'shape' => 'GuardrailRegexDescriptionString', ], 'pattern' => [ 'shape' => 'GuardrailRegexPatternString', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationAction', ], ], ], 'GuardrailRegexConfig' => [ 'type' => 'structure', 'required' => [ 'name', 'pattern', 'action', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailRegexConfigNameString', ], 'description' => [ 'shape' => 'GuardrailRegexConfigDescriptionString', ], 'pattern' => [ 'shape' => 'GuardrailRegexConfigPatternString', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationAction', ], ], ], 'GuardrailRegexConfigDescriptionString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'GuardrailRegexConfigNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'GuardrailRegexConfigPatternString' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'GuardrailRegexDescriptionString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'GuardrailRegexNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'GuardrailRegexPatternString' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'GuardrailRegexes' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailRegex', ], ], 'GuardrailRegexesConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailRegexConfig', ], 'max' => 10, 'min' => 1, ], 'GuardrailSensitiveInformationAction' => [ 'type' => 'string', 'enum' => [ 'BLOCK', 'ANONYMIZE', ], ], 'GuardrailSensitiveInformationPolicy' => [ 'type' => 'structure', 'members' => [ 'piiEntities' => [ 'shape' => 'GuardrailPiiEntities', ], 'regexes' => [ 'shape' => 'GuardrailRegexes', ], ], ], 'GuardrailSensitiveInformationPolicyConfig' => [ 'type' => 'structure', 'members' => [ 'piiEntitiesConfig' => [ 'shape' => 'GuardrailPiiEntitiesConfig', ], 'regexesConfig' => [ 'shape' => 'GuardrailRegexesConfig', ], ], ], 'GuardrailStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'VERSIONING', 'READY', 'FAILED', 'DELETING', ], ], 'GuardrailStatusReason' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'GuardrailStatusReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailStatusReason', ], 'max' => 100, 'min' => 0, ], 'GuardrailSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailSummary', ], 'max' => 1000, 'min' => 0, ], 'GuardrailSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'status', 'name', 'version', 'createdAt', 'updatedAt', ], 'members' => [ 'id' => [ 'shape' => 'GuardrailId', ], 'arn' => [ 'shape' => 'GuardrailArn', ], 'status' => [ 'shape' => 'GuardrailStatus', ], 'name' => [ 'shape' => 'GuardrailName', ], 'description' => [ 'shape' => 'GuardrailDescription', ], 'version' => [ 'shape' => 'GuardrailVersion', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GuardrailTopic' => [ 'type' => 'structure', 'required' => [ 'name', 'definition', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailTopicName', ], 'definition' => [ 'shape' => 'GuardrailTopicDefinition', ], 'examples' => [ 'shape' => 'GuardrailTopicExamples', ], 'type' => [ 'shape' => 'GuardrailTopicType', ], ], ], 'GuardrailTopicConfig' => [ 'type' => 'structure', 'required' => [ 'name', 'definition', 'type', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailTopicName', ], 'definition' => [ 'shape' => 'GuardrailTopicDefinition', ], 'examples' => [ 'shape' => 'GuardrailTopicExamples', ], 'type' => [ 'shape' => 'GuardrailTopicType', ], ], ], 'GuardrailTopicDefinition' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'GuardrailTopicExample' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'GuardrailTopicExamples' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopicExample', ], 'max' => 5, 'min' => 0, ], 'GuardrailTopicName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[0-9a-zA-Z-_ !?.]+', 'sensitive' => true, ], 'GuardrailTopicPolicy' => [ 'type' => 'structure', 'required' => [ 'topics', ], 'members' => [ 'topics' => [ 'shape' => 'GuardrailTopics', ], ], ], 'GuardrailTopicPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'topicsConfig', ], 'members' => [ 'topicsConfig' => [ 'shape' => 'GuardrailTopicsConfig', ], ], ], 'GuardrailTopicType' => [ 'type' => 'string', 'enum' => [ 'DENY', ], ], 'GuardrailTopics' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopic', ], 'max' => 30, 'min' => 1, ], 'GuardrailTopicsConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopicConfig', ], 'max' => 30, 'min' => 1, ], 'GuardrailVersion' => [ 'type' => 'string', 'pattern' => '(([1-9][0-9]{0,7})|(DRAFT))', ], 'GuardrailWord' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'GuardrailWordTextString', ], ], ], 'GuardrailWordConfig' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'GuardrailWordConfigTextString', ], ], ], 'GuardrailWordConfigTextString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'GuardrailWordPolicy' => [ 'type' => 'structure', 'members' => [ 'words' => [ 'shape' => 'GuardrailWords', ], 'managedWordLists' => [ 'shape' => 'GuardrailManagedWordLists', ], ], ], 'GuardrailWordPolicyConfig' => [ 'type' => 'structure', 'members' => [ 'wordsConfig' => [ 'shape' => 'GuardrailWordsConfig', ], 'managedWordListsConfig' => [ 'shape' => 'GuardrailManagedWordListsConfig', ], ], ], 'GuardrailWordTextString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'GuardrailWords' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailWord', ], 'max' => 10000, 'min' => 1, ], 'GuardrailWordsConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailWordConfig', ], 'max' => 10000, 'min' => 1, ], 'HumanEvaluationConfig' => [ 'type' => 'structure', 'required' => [ 'datasetMetricConfigs', ], 'members' => [ 'humanWorkflowConfig' => [ 'shape' => 'HumanWorkflowConfig', ], 'customMetrics' => [ 'shape' => 'HumanEvaluationCustomMetrics', ], 'datasetMetricConfigs' => [ 'shape' => 'EvaluationDatasetMetricConfigs', ], ], ], 'HumanEvaluationCustomMetric' => [ 'type' => 'structure', 'required' => [ 'name', 'ratingMethod', ], 'members' => [ 'name' => [ 'shape' => 'EvaluationMetricName', ], 'description' => [ 'shape' => 'EvaluationMetricDescription', ], 'ratingMethod' => [ 'shape' => 'EvaluationRatingMethod', ], ], ], 'HumanEvaluationCustomMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'HumanEvaluationCustomMetric', ], 'max' => 10, 'min' => 1, ], 'HumanTaskInstructions' => [ 'type' => 'string', 'max' => 5000, 'min' => 1, 'pattern' => '[\\S\\s]+', 'sensitive' => true, ], 'HumanWorkflowConfig' => [ 'type' => 'structure', 'required' => [ 'flowDefinitionArn', ], 'members' => [ 'flowDefinitionArn' => [ 'shape' => 'SageMakerFlowDefinitionArn', ], 'instructions' => [ 'shape' => 'HumanTaskInstructions', ], ], ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'InferenceType' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'PROVISIONED', ], ], 'InferenceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferenceType', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z0-9](-*[a-zA-Z0-9\\+\\-\\.])*', ], 'KeyPrefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:kms:[a-zA-Z0-9-]*:[0-9]{12}:((key/[a-zA-Z0-9-]{36})|(alias/[a-zA-Z0-9-_/]+))', ], 'ListCustomModelsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'nameContains' => [ 'shape' => 'CustomModelName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'baseModelArnEquals' => [ 'shape' => 'ModelArn', 'location' => 'querystring', 'locationName' => 'baseModelArnEquals', ], 'foundationModelArnEquals' => [ 'shape' => 'FoundationModelArn', 'location' => 'querystring', 'locationName' => 'foundationModelArnEquals', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortModelsBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], 'isOwned' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'isOwned', ], ], ], 'ListCustomModelsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'modelSummaries' => [ 'shape' => 'CustomModelSummaryList', ], ], ], 'ListEvaluationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'statusEquals' => [ 'shape' => 'EvaluationJobStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'nameContains' => [ 'shape' => 'EvaluationJobName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortJobsBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListEvaluationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'jobSummaries' => [ 'shape' => 'EvaluationSummaries', ], ], ], 'ListFoundationModelsRequest' => [ 'type' => 'structure', 'members' => [ 'byProvider' => [ 'shape' => 'Provider', 'location' => 'querystring', 'locationName' => 'byProvider', ], 'byCustomizationType' => [ 'shape' => 'ModelCustomization', 'location' => 'querystring', 'locationName' => 'byCustomizationType', ], 'byOutputModality' => [ 'shape' => 'ModelModality', 'location' => 'querystring', 'locationName' => 'byOutputModality', ], 'byInferenceType' => [ 'shape' => 'InferenceType', 'location' => 'querystring', 'locationName' => 'byInferenceType', ], ], ], 'ListFoundationModelsResponse' => [ 'type' => 'structure', 'members' => [ 'modelSummaries' => [ 'shape' => 'FoundationModelSummaryList', ], ], ], 'ListGuardrailsRequest' => [ 'type' => 'structure', 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'querystring', 'locationName' => 'guardrailIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListGuardrailsResponse' => [ 'type' => 'structure', 'required' => [ 'guardrails', ], 'members' => [ 'guardrails' => [ 'shape' => 'GuardrailSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListModelCopyJobsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'statusEquals' => [ 'shape' => 'ModelCopyJobStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'sourceAccountEquals' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'sourceAccountEquals', ], 'sourceModelArnEquals' => [ 'shape' => 'ModelArn', 'location' => 'querystring', 'locationName' => 'sourceModelArnEquals', ], 'targetModelNameContains' => [ 'shape' => 'CustomModelName', 'location' => 'querystring', 'locationName' => 'outputModelNameContains', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortJobsBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListModelCopyJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'modelCopyJobSummaries' => [ 'shape' => 'ModelCopyJobSummaries', ], ], ], 'ListModelCustomizationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'statusEquals' => [ 'shape' => 'FineTuningJobStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'nameContains' => [ 'shape' => 'JobName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortJobsBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListModelCustomizationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'modelCustomizationJobSummaries' => [ 'shape' => 'ModelCustomizationJobSummaries', ], ], ], 'ListModelInvocationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'submitTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'submitTimeAfter', ], 'submitTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'submitTimeBefore', ], 'statusEquals' => [ 'shape' => 'ModelInvocationJobStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'nameContains' => [ 'shape' => 'ModelInvocationJobName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortJobsBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListModelInvocationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'invocationJobSummaries' => [ 'shape' => 'ModelInvocationJobSummaries', ], ], ], 'ListProvisionedModelThroughputsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'statusEquals' => [ 'shape' => 'ProvisionedModelStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'modelArnEquals' => [ 'shape' => 'ModelArn', 'location' => 'querystring', 'locationName' => 'modelArnEquals', ], 'nameContains' => [ 'shape' => 'ProvisionedModelName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortByProvisionedModels', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListProvisionedModelThroughputsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'provisionedModelSummaries' => [ 'shape' => 'ProvisionedModelSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourcesArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LoggingConfig' => [ 'type' => 'structure', 'members' => [ 'cloudWatchConfig' => [ 'shape' => 'CloudWatchConfig', ], 's3Config' => [ 'shape' => 'S3Config', ], 'textDataDeliveryEnabled' => [ 'shape' => 'Boolean', ], 'imageDataDeliveryEnabled' => [ 'shape' => 'Boolean', ], 'embeddingDataDeliveryEnabled' => [ 'shape' => 'Boolean', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'Message' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'sensitive' => true, ], 'MetricFloat' => [ 'type' => 'float', 'box' => true, ], 'ModelArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}))', ], 'ModelCopyJobArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:model-copy-job/[a-z0-9]{12}', ], 'ModelCopyJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', ], ], 'ModelCopyJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelCopyJobSummary', ], ], 'ModelCopyJobSummary' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'status', 'creationTime', 'targetModelArn', 'sourceAccountId', 'sourceModelArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCopyJobArn', ], 'status' => [ 'shape' => 'ModelCopyJobStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'targetModelArn' => [ 'shape' => 'CustomModelArn', ], 'targetModelName' => [ 'shape' => 'CustomModelName', ], 'sourceAccountId' => [ 'shape' => 'AccountId', ], 'sourceModelArn' => [ 'shape' => 'ModelArn', ], 'targetModelKmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'targetModelTags' => [ 'shape' => 'TagList', ], 'failureMessage' => [ 'shape' => 'ErrorMessage', ], 'sourceModelName' => [ 'shape' => 'CustomModelName', ], ], ], 'ModelCustomization' => [ 'type' => 'string', 'enum' => [ 'FINE_TUNING', 'CONTINUED_PRE_TRAINING', ], ], 'ModelCustomizationHyperParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ModelCustomizationJobArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:model-customization-job/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}/[a-z0-9]{12}', ], 'ModelCustomizationJobIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:model-customization-job/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}/[a-z0-9]{12})|([a-zA-Z0-9](-*[a-zA-Z0-9\\+\\-\\.])*)', ], 'ModelCustomizationJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'ModelCustomizationJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelCustomizationJobSummary', ], ], 'ModelCustomizationJobSummary' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'baseModelArn', 'jobName', 'status', 'creationTime', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], 'baseModelArn' => [ 'shape' => 'ModelArn', ], 'jobName' => [ 'shape' => 'JobName', ], 'status' => [ 'shape' => 'ModelCustomizationJobStatus', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'customModelArn' => [ 'shape' => 'CustomModelArn', ], 'customModelName' => [ 'shape' => 'CustomModelName', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], ], ], 'ModelCustomizationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelCustomization', ], ], 'ModelId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-:]{1,63}/[a-z0-9]{12}$)|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}$)))|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})|(([0-9a-zA-Z][_-]?)+)', ], 'ModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}(([:][a-z0-9-]{1,63}){0,2})?/[a-z0-9]{12})|(:foundation-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})))|(([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2}))|(([0-9a-zA-Z][_-]?)+)', ], 'ModelInvocationIdempotencyToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9]{1,256}(-*[a-zA-Z0-9]){0,256}', ], 'ModelInvocationJobArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:model-invocation-job/[a-z0-9]{12})', ], 'ModelInvocationJobIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => '((arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:model-invocation-job/)?[a-z0-9]{12})', ], 'ModelInvocationJobInputDataConfig' => [ 'type' => 'structure', 'members' => [ 's3InputDataConfig' => [ 'shape' => 'ModelInvocationJobS3InputDataConfig', ], ], 'union' => true, ], 'ModelInvocationJobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z0-9]{1,63}(-*[a-zA-Z0-9\\+\\-\\.]){0,63}', ], 'ModelInvocationJobOutputDataConfig' => [ 'type' => 'structure', 'members' => [ 's3OutputDataConfig' => [ 'shape' => 'ModelInvocationJobS3OutputDataConfig', ], ], 'union' => true, ], 'ModelInvocationJobS3InputDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3InputFormat' => [ 'shape' => 'S3InputFormat', ], 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'ModelInvocationJobS3OutputDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], 's3EncryptionKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ModelInvocationJobStatus' => [ 'type' => 'string', 'enum' => [ 'Submitted', 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', 'PartiallyCompleted', 'Expired', 'Validating', 'Scheduled', ], ], 'ModelInvocationJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelInvocationJobSummary', ], ], 'ModelInvocationJobSummary' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'jobName', 'modelId', 'roleArn', 'submitTime', 'inputDataConfig', 'outputDataConfig', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelInvocationJobArn', ], 'jobName' => [ 'shape' => 'ModelInvocationJobName', ], 'modelId' => [ 'shape' => 'ModelId', ], 'clientRequestToken' => [ 'shape' => 'ModelInvocationIdempotencyToken', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ModelInvocationJobStatus', ], 'message' => [ 'shape' => 'Message', ], 'submitTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'inputDataConfig' => [ 'shape' => 'ModelInvocationJobInputDataConfig', ], 'outputDataConfig' => [ 'shape' => 'ModelInvocationJobOutputDataConfig', ], 'timeoutDurationInHours' => [ 'shape' => 'ModelInvocationJobTimeoutDurationInHours', ], 'jobExpirationTime' => [ 'shape' => 'Timestamp', ], ], ], 'ModelInvocationJobTimeoutDurationInHours' => [ 'type' => 'integer', 'box' => true, 'max' => 168, 'min' => 24, ], 'ModelModality' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'IMAGE', 'EMBEDDING', ], ], 'ModelModalityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelModality', ], ], 'ModelName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63})', ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '[\\s\\S]*', ], 'OutputDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '\\S*', ], 'PositiveInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Provider' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9- ]{1,63}', ], 'ProvisionedModelArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:provisioned-model/[a-z0-9]{12}', ], 'ProvisionedModelId' => [ 'type' => 'string', 'pattern' => '((([0-9a-zA-Z][_-]?)+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:provisioned-model/[a-z0-9]{12}))', ], 'ProvisionedModelName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '([0-9a-zA-Z][_-]?)+', ], 'ProvisionedModelStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'InService', 'Updating', 'Failed', ], ], 'ProvisionedModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisionedModelSummary', ], ], 'ProvisionedModelSummary' => [ 'type' => 'structure', 'required' => [ 'provisionedModelName', 'provisionedModelArn', 'modelArn', 'desiredModelArn', 'foundationModelArn', 'modelUnits', 'desiredModelUnits', 'status', 'creationTime', 'lastModifiedTime', ], 'members' => [ 'provisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'provisionedModelArn' => [ 'shape' => 'ProvisionedModelArn', ], 'modelArn' => [ 'shape' => 'ModelArn', ], 'desiredModelArn' => [ 'shape' => 'ModelArn', ], 'foundationModelArn' => [ 'shape' => 'FoundationModelArn', ], 'modelUnits' => [ 'shape' => 'PositiveInteger', ], 'desiredModelUnits' => [ 'shape' => 'PositiveInteger', ], 'status' => [ 'shape' => 'ProvisionedModelStatus', ], 'commitmentDuration' => [ 'shape' => 'CommitmentDuration', ], 'commitmentExpirationTime' => [ 'shape' => 'Timestamp', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'PutModelInvocationLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'loggingConfig', ], 'members' => [ 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], ], ], 'PutModelInvocationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/.+', ], 'S3Config' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'keyPrefix' => [ 'shape' => 'KeyPrefix', ], ], ], 'S3InputFormat' => [ 'type' => 'string', 'enum' => [ 'JSONL', ], ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?', ], 'SageMakerFlowDefinitionArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:sagemaker:[a-z0-9-]{1,20}:[0-9]{12}:flow-definition/.*', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '[-0-9a-zA-Z]+', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SortByProvisionedModels' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'SortJobsBy' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'SortModelsBy' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'StopEvaluationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'EvaluationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'StopEvaluationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopModelCustomizationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'ModelCustomizationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'StopModelCustomizationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopModelInvocationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'ModelInvocationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'StopModelInvocationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '[-0-9a-zA-Z]+', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\s._:/=+@-]*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourcesArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9\\s._:/=+@-]*', ], 'TaggableResourcesArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '.*(^[a-zA-Z0-9][a-zA-Z0-9\\-]*$)|(^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:([0-9]{12}|)((:(fine-tuning-job|model-customization-job|custom-model)/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}(/[a-z0-9]{12})$)|(:guardrail/[a-z0-9]+$)|(:(provisioned-model|model-invocation-job|model-evaluation-job|evaluation-job|model-import-job|imported-model)/[a-z0-9]{12}$))).*', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'TaggableResourcesArn', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrainingDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'TrainingMetrics' => [ 'type' => 'structure', 'members' => [ 'trainingLoss' => [ 'shape' => 'MetricFloat', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourcesArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', 'name', 'blockedInputMessaging', 'blockedOutputsMessaging', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'uri', 'locationName' => 'guardrailIdentifier', ], 'name' => [ 'shape' => 'GuardrailName', ], 'description' => [ 'shape' => 'GuardrailDescription', ], 'topicPolicyConfig' => [ 'shape' => 'GuardrailTopicPolicyConfig', ], 'contentPolicyConfig' => [ 'shape' => 'GuardrailContentPolicyConfig', ], 'wordPolicyConfig' => [ 'shape' => 'GuardrailWordPolicyConfig', ], 'sensitiveInformationPolicyConfig' => [ 'shape' => 'GuardrailSensitiveInformationPolicyConfig', ], 'contextualGroundingPolicyConfig' => [ 'shape' => 'GuardrailContextualGroundingPolicyConfig', ], 'blockedInputMessaging' => [ 'shape' => 'GuardrailBlockedMessaging', ], 'blockedOutputsMessaging' => [ 'shape' => 'GuardrailBlockedMessaging', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'UpdateGuardrailResponse' => [ 'type' => 'structure', 'required' => [ 'guardrailId', 'guardrailArn', 'version', 'updatedAt', ], 'members' => [ 'guardrailId' => [ 'shape' => 'GuardrailId', ], 'guardrailArn' => [ 'shape' => 'GuardrailArn', ], 'version' => [ 'shape' => 'GuardrailDraftVersion', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'provisionedModelId', ], 'members' => [ 'provisionedModelId' => [ 'shape' => 'ProvisionedModelId', 'location' => 'uri', 'locationName' => 'provisionedModelId', ], 'desiredProvisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'desiredModelId' => [ 'shape' => 'ModelIdentifier', ], ], ], 'UpdateProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationDataConfig' => [ 'type' => 'structure', 'required' => [ 'validators', ], 'members' => [ 'validators' => [ 'shape' => 'Validators', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidatorMetric', ], ], 'Validator' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'ValidatorMetric' => [ 'type' => 'structure', 'members' => [ 'validationLoss' => [ 'shape' => 'MetricFloat', ], ], ], 'Validators' => [ 'type' => 'list', 'member' => [ 'shape' => 'Validator', ], 'max' => 10, 'min' => 0, ], 'VpcConfig' => [ 'type' => 'structure', 'required' => [ 'subnetIds', 'securityGroupIds', ], 'members' => [ 'subnetIds' => [ 'shape' => 'SubnetIds', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], ], ], ],];
