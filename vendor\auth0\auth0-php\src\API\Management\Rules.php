<?php

declare(strict_types=1);

namespace Auth0\SDK\API\Management;

use Auth0\SDK\Contract\API\Management\RulesInterface;
use Auth0\SDK\Utility\Request\RequestOptions;
use Auth0\SDK\Utility\Toolkit;
use Psr\Http\Message\ResponseInterface;

/**
 * <PERSON>les requests to the Rules endpoint of the v2 Management API.
 *
 * @see https://auth0.com/docs/api/management/v2#!/Rules
 */
final class Rules extends ManagementEndpoint implements RulesInterface
{
    public function create(
        string $name,
        string $script,
        ?array $body = null,
        ?RequestOptions $options = null,
    ): ResponseInterface {
        [$name, $script] = Toolkit::filter([$name, $script])->string()->trim();
        [$body] = Toolkit::filter([$body])->array()->trim();

        Toolkit::assert([
            [$name, \Auth0\SDK\Exception\ArgumentException::missing('name')],
            [$script, \Auth0\SDK\Exception\ArgumentException::missing('script')],
        ])->isString();

        /** @var array<mixed> $body */

        return $this->getHttpClient()
            ->method('post')
            ->addPath(['rules'])
            ->withBody(
                (object) Toolkit::merge([[
                    'name' => $name,
                    'script' => $script,
                ], $body]),
            )
            ->withOptions($options)
            ->call();
    }

    public function delete(
        string $id,
        ?RequestOptions $options = null,
    ): ResponseInterface {
        [$id] = Toolkit::filter([$id])->string()->trim();

        Toolkit::assert([
            [$id, \Auth0\SDK\Exception\ArgumentException::missing('id')],
        ])->isString();

        return $this->getHttpClient()
            ->method('delete')->addPath(['rules', $id])
            ->withOptions($options)
            ->call();
    }

    public function get(
        string $id,
        ?RequestOptions $options = null,
    ): ResponseInterface {
        [$id] = Toolkit::filter([$id])->string()->trim();

        Toolkit::assert([
            [$id, \Auth0\SDK\Exception\ArgumentException::missing('id')],
        ])->isString();

        return $this->getHttpClient()
            ->method('get')->addPath(['rules', $id])
            ->withOptions($options)
            ->call();
    }

    public function getAll(
        ?array $parameters = null,
        ?RequestOptions $options = null,
    ): ResponseInterface {
        [$parameters] = Toolkit::filter([$parameters])->array()->trim();

        /** @var array<null|int|string> $parameters */

        return $this->getHttpClient()
            ->method('get')
            ->addPath(['rules'])
            ->withParams($parameters)
            ->withOptions($options)
            ->call();
    }

    public function update(
        string $id,
        array $body,
        ?RequestOptions $options = null,
    ): ResponseInterface {
        [$id] = Toolkit::filter([$id])->string()->trim();
        [$body] = Toolkit::filter([$body])->array()->trim();

        Toolkit::assert([
            [$id, \Auth0\SDK\Exception\ArgumentException::missing('id')],
        ])->isString();

        Toolkit::assert([
            [$body, \Auth0\SDK\Exception\ArgumentException::missing('body')],
        ])->isArray();

        return $this->getHttpClient()
            ->method('patch')->addPath(['rules', $id])
            ->withBody((object) $body)
            ->withOptions($options)
            ->call();
    }
}
