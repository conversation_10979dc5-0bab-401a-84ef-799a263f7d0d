{"name": "php-http/multipart-stream-builder", "description": "A builder class that help you create a multipart stream", "license": "MIT", "keywords": ["http", "factory", "message", "stream", "multipart stream"], "homepage": "http://php-http.org", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1 || ^8.0", "php-http/discovery": "^1.15", "psr/http-factory-implementation": "^1.0"}, "require-dev": {"phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3", "php-http/message": "^1.5", "php-http/message-factory": "^1.0.2", "nyholm/psr7": "^1.0"}, "autoload": {"psr-4": {"Http\\Message\\MultipartStream\\": "src/"}}, "autoload-dev": {"psr-4": {"tests\\Http\\Message\\MultipartStream\\": "tests/"}}, "scripts": {"test": "vendor/bin/phpunit", "test-ci": "vendor/bin/phpunit --coverage-text --coverage-clover=build/coverage.xml"}, "config": {"allow-plugins": {"php-http/discovery": false}}}