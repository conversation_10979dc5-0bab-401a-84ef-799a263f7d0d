<?php
// This file was auto-generated from sdk-root/src/data/chatbot/2017-10-11/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-10-11', 'endpointPrefix' => 'chatbot', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Chatbot', 'serviceId' => 'chatbot', 'signatureVersion' => 'v4', 'uid' => 'chatbot-2017-10-11', ], 'operations' => [ 'CreateChimeWebhookConfiguration' => [ 'name' => 'CreateChimeWebhookConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-chime-webhook-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChimeWebhookConfigurationRequest', ], 'output' => [ 'shape' => 'CreateChimeWebhookConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'CreateChimeWebhookConfigurationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateMicrosoftTeamsChannelConfiguration' => [ 'name' => 'CreateMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-ms-teams-channel-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'CreateTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'CreateTeamsChannelConfigurationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateSlackChannelConfiguration' => [ 'name' => 'CreateSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-slack-channel-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'CreateSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'CreateSlackChannelConfigurationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteChimeWebhookConfiguration' => [ 'name' => 'DeleteChimeWebhookConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-chime-webhook-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChimeWebhookConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteChimeWebhookConfigurationResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DeleteChimeWebhookConfigurationException', ], ], ], 'DeleteMicrosoftTeamsChannelConfiguration' => [ 'name' => 'DeleteMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-ms-teams-channel-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DeleteTeamsChannelConfigurationException', ], ], ], 'DeleteMicrosoftTeamsConfiguredTeam' => [ 'name' => 'DeleteMicrosoftTeamsConfiguredTeam', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-ms-teams-configured-teams', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTeamsConfiguredTeamRequest', ], 'output' => [ 'shape' => 'DeleteTeamsConfiguredTeamResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DeleteTeamsConfiguredTeamException', ], ], ], 'DeleteMicrosoftTeamsUserIdentity' => [ 'name' => 'DeleteMicrosoftTeamsUserIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-ms-teams-user-identity', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMicrosoftTeamsUserIdentityRequest', ], 'output' => [ 'shape' => 'DeleteMicrosoftTeamsUserIdentityResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DeleteMicrosoftTeamsUserIdentityException', ], ], ], 'DeleteSlackChannelConfiguration' => [ 'name' => 'DeleteSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-slack-channel-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DeleteSlackChannelConfigurationException', ], ], ], 'DeleteSlackUserIdentity' => [ 'name' => 'DeleteSlackUserIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-slack-user-identity', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlackUserIdentityRequest', ], 'output' => [ 'shape' => 'DeleteSlackUserIdentityResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DeleteSlackUserIdentityException', ], ], ], 'DeleteSlackWorkspaceAuthorization' => [ 'name' => 'DeleteSlackWorkspaceAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-slack-workspace-authorization', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlackWorkspaceAuthorizationRequest', ], 'output' => [ 'shape' => 'DeleteSlackWorkspaceAuthorizationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DeleteSlackWorkspaceAuthorizationFault', ], ], ], 'DescribeChimeWebhookConfigurations' => [ 'name' => 'DescribeChimeWebhookConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-chime-webhook-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChimeWebhookConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeChimeWebhookConfigurationsResult', ], 'errors' => [ [ 'shape' => 'DescribeChimeWebhookConfigurationsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeSlackChannelConfigurations' => [ 'name' => 'DescribeSlackChannelConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-slack-channel-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlackChannelConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeSlackChannelConfigurationsResult', ], 'errors' => [ [ 'shape' => 'DescribeSlackChannelConfigurationsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeSlackUserIdentities' => [ 'name' => 'DescribeSlackUserIdentities', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-slack-user-identities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlackUserIdentitiesRequest', ], 'output' => [ 'shape' => 'DescribeSlackUserIdentitiesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DescribeSlackUserIdentitiesException', ], ], ], 'DescribeSlackWorkspaces' => [ 'name' => 'DescribeSlackWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-slack-workspaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlackWorkspacesRequest', ], 'output' => [ 'shape' => 'DescribeSlackWorkspacesResult', ], 'errors' => [ [ 'shape' => 'DescribeSlackWorkspacesException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'GetAccountPreferences' => [ 'name' => 'GetAccountPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-account-preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccountPreferencesRequest', ], 'output' => [ 'shape' => 'GetAccountPreferencesResult', ], 'errors' => [ [ 'shape' => 'GetAccountPreferencesException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'GetMicrosoftTeamsChannelConfiguration' => [ 'name' => 'GetMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-ms-teams-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'GetTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'GetTeamsChannelConfigurationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListMicrosoftTeamsChannelConfigurations' => [ 'name' => 'ListMicrosoftTeamsChannelConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-ms-teams-channel-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTeamsChannelConfigurationsRequest', ], 'output' => [ 'shape' => 'ListTeamsChannelConfigurationsResult', ], 'errors' => [ [ 'shape' => 'ListTeamsChannelConfigurationsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListMicrosoftTeamsConfiguredTeams' => [ 'name' => 'ListMicrosoftTeamsConfiguredTeams', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-ms-teams-configured-teams', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMicrosoftTeamsConfiguredTeamsRequest', ], 'output' => [ 'shape' => 'ListMicrosoftTeamsConfiguredTeamsResult', ], 'errors' => [ [ 'shape' => 'ListMicrosoftTeamsConfiguredTeamsException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListMicrosoftTeamsUserIdentities' => [ 'name' => 'ListMicrosoftTeamsUserIdentities', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-ms-teams-user-identities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMicrosoftTeamsUserIdentitiesRequest', ], 'output' => [ 'shape' => 'ListMicrosoftTeamsUserIdentitiesResult', ], 'errors' => [ [ 'shape' => 'ListMicrosoftTeamsUserIdentitiesException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-tags-for-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tag-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/untag-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateAccountPreferences' => [ 'name' => 'UpdateAccountPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-account-preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccountPreferencesRequest', ], 'output' => [ 'shape' => 'UpdateAccountPreferencesResult', ], 'errors' => [ [ 'shape' => 'UpdateAccountPreferencesException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateChimeWebhookConfiguration' => [ 'name' => 'UpdateChimeWebhookConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-chime-webhook-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChimeWebhookConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateChimeWebhookConfigurationResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UpdateChimeWebhookConfigurationException', ], ], ], 'UpdateMicrosoftTeamsChannelConfiguration' => [ 'name' => 'UpdateMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-ms-teams-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UpdateTeamsChannelConfigurationException', ], ], ], 'UpdateSlackChannelConfiguration' => [ 'name' => 'UpdateSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-slack-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UpdateSlackChannelConfigurationException', ], ], ], ], 'shapes' => [ 'AccountPreferences' => [ 'type' => 'structure', 'members' => [ 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'TrainingDataCollectionEnabled' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '^arn:aws:(wheatley|chatbot):[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'Arn' => [ 'type' => 'string', 'max' => 1224, 'min' => 12, 'pattern' => '^arn:aws:[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'AwsUserIdentity' => [ 'type' => 'string', 'max' => 1101, 'min' => 15, 'pattern' => '^arn:aws:(iam|sts)::[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'BooleanAccountPreference' => [ 'type' => 'boolean', ], 'ChatConfigurationArn' => [ 'type' => 'string', 'max' => 1169, 'min' => 19, 'pattern' => '^arn:aws:(wheatley|chatbot):[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'ChimeWebhookConfiguration' => [ 'type' => 'structure', 'required' => [ 'WebhookDescription', 'ChatConfigurationArn', 'IamRoleArn', 'SnsTopicArns', ], 'members' => [ 'WebhookDescription' => [ 'shape' => 'ChimeWebhookDescription', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ChimeWebhookConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChimeWebhookConfiguration', ], ], 'ChimeWebhookDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ChimeWebhookUrl' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^https://hooks\\.chime\\.aws/incomingwebhooks/[A-Za-z0-9\\-]+?\\?token=[A-Za-z0-9\\-]+$', ], 'ConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9-_]+$', ], 'ConfiguredTeam' => [ 'type' => 'structure', 'required' => [ 'TenantId', 'TeamId', ], 'members' => [ 'TenantId' => [ 'shape' => 'UUID', ], 'TeamId' => [ 'shape' => 'UUID', ], 'TeamName' => [ 'shape' => 'UUID', 'box' => true, ], ], ], 'ConfiguredTeamsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredTeam', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateChimeWebhookConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'CreateChimeWebhookConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'WebhookDescription', 'WebhookUrl', 'SnsTopicArns', 'IamRoleArn', 'ConfigurationName', ], 'members' => [ 'WebhookDescription' => [ 'shape' => 'ChimeWebhookDescription', ], 'WebhookUrl' => [ 'shape' => 'ChimeWebhookUrl', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateChimeWebhookConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'WebhookConfiguration' => [ 'shape' => 'ChimeWebhookConfiguration', ], ], ], 'CreateSlackChannelConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'CreateSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'SlackTeamId', 'SlackChannelId', 'IamRoleArn', 'ConfigurationName', ], 'members' => [ 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackChannelId' => [ 'shape' => 'SlackChannelId', ], 'SlackChannelName' => [ 'shape' => 'SlackChannelDisplayName', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'SlackChannelConfiguration', ], ], ], 'CreateTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'CreateTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelId', 'TeamId', 'TenantId', 'IamRoleArn', 'ConfigurationName', ], 'members' => [ 'ChannelId' => [ 'shape' => 'TeamsChannelId', ], 'ChannelName' => [ 'shape' => 'TeamsChannelName', ], 'TeamId' => [ 'shape' => 'UUID', ], 'TeamName' => [ 'shape' => 'TeamName', ], 'TenantId' => [ 'shape' => 'UUID', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'TeamsChannelConfiguration', ], ], ], 'CustomerCwLogLevel' => [ 'type' => 'string', 'max' => 5, 'min' => 4, 'pattern' => '^(ERROR|INFO|NONE)$', ], 'DeleteChimeWebhookConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteChimeWebhookConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DeleteChimeWebhookConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMicrosoftTeamsUserIdentityException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteMicrosoftTeamsUserIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'UserId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'UserId' => [ 'shape' => 'UUID', ], ], ], 'DeleteMicrosoftTeamsUserIdentityResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackChannelConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DeleteSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackUserIdentityException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteSlackUserIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'SlackTeamId', 'SlackUserId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackUserId' => [ 'shape' => 'SlackUserId', ], ], ], 'DeleteSlackUserIdentityResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackWorkspaceAuthorizationFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteSlackWorkspaceAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'SlackTeamId', ], 'members' => [ 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], ], ], 'DeleteSlackWorkspaceAuthorizationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DeleteTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTeamsConfiguredTeamException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteTeamsConfiguredTeamRequest' => [ 'type' => 'structure', 'required' => [ 'TeamId', ], 'members' => [ 'TeamId' => [ 'shape' => 'UUID', ], ], ], 'DeleteTeamsConfiguredTeamResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeChimeWebhookConfigurationsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeChimeWebhookConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'PaginationToken', 'box' => true, ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', 'box' => true, ], ], ], 'DescribeChimeWebhookConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'WebhookConfigurations' => [ 'shape' => 'ChimeWebhookConfigurationList', ], ], ], 'DescribeSlackChannelConfigurationsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeSlackChannelConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'PaginationToken', 'box' => true, ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', 'box' => true, ], ], ], 'DescribeSlackChannelConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'SlackChannelConfigurations' => [ 'shape' => 'SlackChannelConfigurationList', ], ], ], 'DescribeSlackUserIdentitiesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeSlackUserIdentitiesRequest' => [ 'type' => 'structure', 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeSlackUserIdentitiesResult' => [ 'type' => 'structure', 'members' => [ 'SlackUserIdentities' => [ 'shape' => 'SlackUserIdentitiesList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSlackWorkspacesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeSlackWorkspacesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSlackWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'SlackWorkspaces' => [ 'shape' => 'SlackWorkspacesList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetAccountPreferencesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'GetAccountPreferencesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountPreferencesResult' => [ 'type' => 'structure', 'members' => [ 'AccountPreferences' => [ 'shape' => 'AccountPreferences', ], ], ], 'GetTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'GetTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'GetTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'TeamsChannelConfiguration', ], ], ], 'GuardrailPolicyArn' => [ 'type' => 'string', 'max' => 1163, 'min' => 11, 'pattern' => '^(^$|(?!.*\\/aws-service-role\\/.*)arn:aws:iam:[A-Za-z0-9_\\/.-]{0,63}:[A-Za-z0-9_\\/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_\\/+=,@.-]{0,1023})$', ], 'GuardrailPolicyArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPolicyArn', ], ], 'InternalServiceError' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'ListMicrosoftTeamsConfiguredTeamsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListMicrosoftTeamsConfiguredTeamsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMicrosoftTeamsConfiguredTeamsResult' => [ 'type' => 'structure', 'members' => [ 'ConfiguredTeams' => [ 'shape' => 'ConfiguredTeamsList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMicrosoftTeamsUserIdentitiesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListMicrosoftTeamsUserIdentitiesRequest' => [ 'type' => 'structure', 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMicrosoftTeamsUserIdentitiesResult' => [ 'type' => 'structure', 'members' => [ 'TeamsUserIdentities' => [ 'shape' => 'TeamsUserIdentitiesList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTeamsChannelConfigurationsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListTeamsChannelConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'PaginationToken', 'box' => true, ], 'TeamId' => [ 'shape' => 'UUID', 'box' => true, ], ], ], 'ListTeamsChannelConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'TeamChannelConfigurations' => [ 'shape' => 'TeamChannelConfigurationsList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1276, 'min' => 1, 'pattern' => '^[a-zA-Z0-9=\\/+_.\\-,#:\\\\"{}]{4,1276}$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'SlackChannelConfiguration' => [ 'type' => 'structure', 'required' => [ 'SlackTeamName', 'SlackTeamId', 'SlackChannelId', 'SlackChannelName', 'ChatConfigurationArn', 'IamRoleArn', 'SnsTopicArns', ], 'members' => [ 'SlackTeamName' => [ 'shape' => 'SlackTeamName', ], 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackChannelId' => [ 'shape' => 'SlackChannelId', ], 'SlackChannelName' => [ 'shape' => 'SlackChannelDisplayName', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', 'box' => true, ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'SlackChannelConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackChannelConfiguration', ], ], 'SlackChannelDisplayName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SlackChannelId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[A-Za-z0-9]+$', ], 'SlackTeamId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[0-9A-Z]{1,255}$', ], 'SlackTeamName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SlackUserId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.*)$', ], 'SlackUserIdentitiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackUserIdentity', ], ], 'SlackUserIdentity' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'ChatConfigurationArn', 'SlackTeamId', 'SlackUserId', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackUserId' => [ 'shape' => 'SlackUserId', ], 'AwsUserIdentity' => [ 'shape' => 'AwsUserIdentity', ], ], ], 'SlackWorkspace' => [ 'type' => 'structure', 'required' => [ 'SlackTeamId', 'SlackTeamName', ], 'members' => [ 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackTeamName' => [ 'shape' => 'SlackTeamName', ], ], ], 'SlackWorkspacesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackWorkspace', ], ], 'SnsTopicArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValue', ], 'members' => [ 'TagKey' => [ 'shape' => 'TagKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TeamChannelConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TeamsChannelConfiguration', ], ], 'TeamName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.*)$', ], 'TeamsChannelConfiguration' => [ 'type' => 'structure', 'required' => [ 'ChannelId', 'TeamId', 'TenantId', 'ChatConfigurationArn', 'IamRoleArn', 'SnsTopicArns', ], 'members' => [ 'ChannelId' => [ 'shape' => 'TeamsChannelId', ], 'ChannelName' => [ 'shape' => 'TeamsChannelName', ], 'TeamId' => [ 'shape' => 'UUID', ], 'TeamName' => [ 'shape' => 'String', ], 'TenantId' => [ 'shape' => 'UUID', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', 'box' => true, ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TeamsChannelId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^([a-zA-Z0-9-_=+\\/.,])*%3[aA]([a-zA-Z0-9-_=+\\/.,])*%40([a-zA-Z0-9-_=+\\/.,])*$', ], 'TeamsChannelName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^(.*)$', ], 'TeamsUserIdentitiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TeamsUserIdentity', ], ], 'TeamsUserIdentity' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'ChatConfigurationArn', 'TeamId', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'TeamId' => [ 'shape' => 'UUID', ], 'UserId' => [ 'shape' => 'UUID', ], 'AwsUserIdentity' => [ 'shape' => 'AwsUserIdentity', ], 'TeamsChannelId' => [ 'shape' => 'TeamsChannelId', ], 'TeamsTenantId' => [ 'shape' => 'UUID', ], ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9A-Fa-f]{8}(?:-[0-9A-Fa-f]{4}){3}-[0-9A-Fa-f]{12}$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccountPreferencesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateAccountPreferencesRequest' => [ 'type' => 'structure', 'members' => [ 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'TrainingDataCollectionEnabled' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'UpdateAccountPreferencesResult' => [ 'type' => 'structure', 'members' => [ 'AccountPreferences' => [ 'shape' => 'AccountPreferences', ], ], ], 'UpdateChimeWebhookConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateChimeWebhookConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'WebhookDescription' => [ 'shape' => 'ChimeWebhookDescription', ], 'WebhookUrl' => [ 'shape' => 'ChimeWebhookUrl', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], ], ], 'UpdateChimeWebhookConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'WebhookConfiguration' => [ 'shape' => 'ChimeWebhookConfiguration', ], ], ], 'UpdateSlackChannelConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'SlackChannelId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'SlackChannelId' => [ 'shape' => 'SlackChannelId', ], 'SlackChannelName' => [ 'shape' => 'SlackChannelDisplayName', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'UpdateSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'SlackChannelConfiguration', ], ], ], 'UpdateTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'ChannelId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'ChannelId' => [ 'shape' => 'TeamsChannelId', ], 'ChannelName' => [ 'shape' => 'TeamsChannelName', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'UpdateTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'TeamsChannelConfiguration', ], ], ], ],];
