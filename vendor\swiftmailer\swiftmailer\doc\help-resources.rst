Getting Help
============

There are a number of ways you can get help when using Swift Mailer, depending
upon the nature of your problem. For bug reports and feature requests create a
new ticket in GitHub. For general advice ask on the Google Group
(swiftmailer).

Submitting Bugs & Feature Requests
----------------------------------

Bugs and feature requests should be posted on GitHub.

If you post a bug or request a feature in the forum, or on the Google Group
you will most likely be asked to create a ticket in `GitHub`_ since it is
simply not feasible to manage such requests from a number of a different
sources.

When you go to GitHub you will be asked to create a username and password
before you can create a ticket. This is free and takes very little time.

When you create your ticket, do not assign it to any milestones. A developer
will assess your ticket and re-assign it as needed.

If your ticket is reporting a bug present in the current version, which was
not present in the previous version please include the tag "regression" in
your ticket.

GitHub will update you when work is performed on your ticket.

Ask on the Google Group
-----------------------

You can seek advice at Google Groups, within the "swiftmailer" `group`_.

You can post messages to this group if you want help, or there's something you
wish to discuss with the developers and with other users.

This is probably the fastest way to get help since it is primarily email-based
for most users, though bug reports should not be posted here since they may
not be resolved.

.. _`GitHub`: https://github.com/swiftmailer/swiftmailer/issues
.. _`group`:  http://groups.google.com/group/swiftmailer
