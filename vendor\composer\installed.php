<?php return array(
    'root' => array(
        'pretty_version' => '14.3.7.x-dev',
        'version' => '14.3.7.9999999-dev',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => 'ac91db3c0f9f140ade951edbbb93e2b1ec6070bd',
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '14.3.7.x-dev',
            'version' => '14.3.7.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => 'ac91db3c0f9f140ade951edbbb93e2b1ec6070bd',
            'dev_requirement' => false,
        ),
        'auth0/auth0-php' => array(
            'pretty_version' => '8.11.1',
            'version' => '8.11.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../auth0/auth0-php',
            'aliases' => array(),
            'reference' => '5d132ad4b3b95c5d5d342d09088d469568bfa627',
            'dev_requirement' => false,
        ),
        'aws/aws-crt-php' => array(
            'pretty_version' => 'v1.2.6',
            'version' => '1.2.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-crt-php',
            'aliases' => array(),
            'reference' => 'a63485b65b6b3367039306496d49737cf1995408',
            'dev_requirement' => false,
        ),
        'aws/aws-sdk-php' => array(
            'pretty_version' => '3.320.5',
            'version' => '3.320.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-sdk-php',
            'aliases' => array(),
            'reference' => 'afda5aefd59da90208d2f59427ce81e91535b1f2',
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.2',
            'version' => '3.4.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'reference' => 'c51258e759afdb17f1fd1fe83bc12baaef6309d6',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => '6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'dev_requirement' => false,
        ),
        'http-interop/http-factory-guzzle' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../http-interop/http-factory-guzzle',
            'aliases' => array(),
            'reference' => '8f06e92b95405216b237521cc64c804dd44c4a81',
            'dev_requirement' => false,
        ),
        'mobiledetect/mobiledetectlib' => array(
            'pretty_version' => '2.8.3',
            'version' => '2.8.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mobiledetect/mobiledetectlib',
            'aliases' => array(),
            'reference' => 'f5753e4b90daffe50c902e99df5ce3c58fca3fee',
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'reference' => 'bbb69a935c2cbb0c03d7f481a238027430f6440b',
            'dev_requirement' => false,
        ),
        'php-amqplib/php-amqplib' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '2.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-amqplib/php-amqplib',
            'aliases' => array(),
            'reference' => 'adc2a425ab214517a5e76557a9370bdc6f212576',
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.19.4',
            'version' => '********',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'reference' => '0700efda8d7526335132360167315fdab3aeb599',
            'dev_requirement' => false,
        ),
        'php-http/multipart-stream-builder' => array(
            'pretty_version' => '1.3.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/multipart-stream-builder',
            'aliases' => array(),
            'reference' => 'ed56da23b95949ae4747378bed8a5b61a2fdae24',
            'dev_requirement' => false,
        ),
        'psr-discovery/all' => array(
            'pretty_version' => '1.0.1',
            'version' => '*******',
            'type' => 'metapackage',
            'install_path' => NULL,
            'aliases' => array(),
            'reference' => 'e353ca0cac46b2e954f4a3ee3a13f0de8be7b87b',
            'dev_requirement' => false,
        ),
        'psr-discovery/cache-implementations' => array(
            'pretty_version' => '1.1.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr-discovery/cache-implementations',
            'aliases' => array(),
            'reference' => 'ebede0af34a7fd3c5564809e659ee69c0ab85ff6',
            'dev_requirement' => false,
        ),
        'psr-discovery/container-implementations' => array(
            'pretty_version' => '1.1.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr-discovery/container-implementations',
            'aliases' => array(),
            'reference' => '728a452b32b0bb60c4bac43b18db2e3105bb8d7e',
            'dev_requirement' => false,
        ),
        'psr-discovery/discovery' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr-discovery/discovery',
            'aliases' => array(),
            'reference' => 'f94a41c150efaffd6f4c23ef95e31cae7a83704f',
            'dev_requirement' => false,
        ),
        'psr-discovery/event-dispatcher-implementations' => array(
            'pretty_version' => '1.1.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr-discovery/event-dispatcher-implementations',
            'aliases' => array(),
            'reference' => '9033bb984613703e4c4f795ef0657184dc1c70eb',
            'dev_requirement' => false,
        ),
        'psr-discovery/http-client-implementations' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr-discovery/http-client-implementations',
            'aliases' => array(),
            'reference' => 'a05c54087d13504d8e48c27395fbab638fb0a114',
            'dev_requirement' => false,
        ),
        'psr-discovery/http-factory-implementations' => array(
            'pretty_version' => '1.1.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr-discovery/http-factory-implementations',
            'aliases' => array(),
            'reference' => '4ee07ae795b794e61578db32b5422a780b01b833',
            'dev_requirement' => false,
        ),
        'psr-discovery/log-implementations' => array(
            'pretty_version' => '1.0.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr-discovery/log-implementations',
            'aliases' => array(),
            'reference' => '384894384663fa5e1b2186112fb8ffe3f81a0b22',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '^1.0',
                2 => '*',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => '79dff0b268932c640297f5208d6298f71855c03e',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'robmorgan/phinx' => array(
            'pretty_version' => 'v0.6.6',
            'version' => '0.6.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../robmorgan/phinx',
            'aliases' => array(),
            'reference' => 'cc97b79f62c2180caba0be1d3744a335a296a678',
            'dev_requirement' => false,
        ),
        'steampixel/simple-php-router' => array(
            'pretty_version' => '0.7.1',
            'version' => '0.7.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../steampixel/simple-php-router',
            'aliases' => array(),
            'reference' => 'c996d75b34c2c6da45d4dc737f61c0ee8fed5d2b',
            'dev_requirement' => false,
        ),
        'swiftmailer/swiftmailer' => array(
            'pretty_version' => 'v5.4.12',
            'version' => '5.4.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../swiftmailer/swiftmailer',
            'aliases' => array(),
            'reference' => '181b89f18a90f8925ef805f950d47a7190e9b950',
            'dev_requirement' => false,
        ),
        'symfony/config' => array(
            'pretty_version' => 'v3.4.47',
            'version' => '3.4.47.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'reference' => 'bc6b3fd3930d4b53a60b42fe2ed6fc466b75f03f',
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v3.4.47',
            'version' => '3.4.47.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'reference' => 'a10b1da6fc93080c180bba7219b5ff5b7518fe81',
            'dev_requirement' => false,
        ),
        'symfony/debug' => array(
            'pretty_version' => 'v4.4.44',
            'version' => '4.4.44.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/debug',
            'aliases' => array(),
            'reference' => '1a692492190773c5310bc7877cb590c04c2f05be',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'dev_requirement' => false,
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v4.4.42',
            'version' => '4.4.42.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'reference' => '815412ee8971209bd4c1eecd5f4f481eacd44bf5',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => '0424dff1c58f028c451efff2045f5d92410bd540',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => 'fd22ab50000ef01661e2a31d850ebaa297f8e03c',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '77fa7995ac1b21ab60769b7323d600a991a90433',
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v3.4.47',
            'version' => '3.4.47.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'reference' => '88289caa3c166321883f67fe5130188ebbb47094',
            'dev_requirement' => false,
        ),
    ),
);
