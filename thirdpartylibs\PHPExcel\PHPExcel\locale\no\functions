##
## PHPExcel
##

## Copyright (c) 2006 - 2013 PHPExcel
##
## This library is free software; you can redistribute it and/or
## modify it under the terms of the GNU Lesser General Public
## License as published by the Free Software Foundation; either
## version 2.1 of the License, or (at your option) any later version.
##
## This library is distributed in the hope that it will be useful,
## but WITHOUT ANY WARRANTY; without even the implied warranty of
## MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
## Lesser General Public License for more details.
##
## You should have received a copy of the GNU Lesser General Public
## License along with this library; if not, write to the Free Software
## Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
##
## @category   PHPExcel
## @package    PHPExcel_Calculation
## @copyright  Copyright (c) 2006 - 2013 PHPExcel (http://www.codeplex.com/PHPExcel)
## @license    http://www.gnu.org/licenses/old-licenses/lgpl-2.1.txt	LGPL
## @version    1.7.9, 2013-06-02
##
## Data in this file derived from http://www.piuha.fi/excel-function-name-translation/
##
##


##
##	Add-in and Automation functions				Funksjonene Tillegg og Automatisering
##
GETPIVOTDATA		= HENTPIVOTDATA				##	Returnerer data som er lagret i en pivottabellrapport


##
##	Cube functions						Kubefunksjoner
##
CUBEKPIMEMBER		= KUBEKPIMEDLEM				##	Returnerer navnet, egenskapen og målet for en viktig ytelsesindikator (KPI), og viser navnet og egenskapen i cellen. En KPI er en målbar enhet, for eksempel månedlig bruttoinntjening eller kvartalsvis inntjening per ansatt, og brukes til å overvåke ytelsen i en organisasjon.
CUBEMEMBER		= KUBEMEDLEM				##	Returnerer et medlem eller en tuppel i et kubehierarki. Brukes til å validere at medlemmet eller tuppelen finnes i kuben.
CUBEMEMBERPROPERTY	= KUBEMEDLEMEGENSKAP			##	Returnerer verdien til en medlemsegenskap i kuben. Brukes til å validere at et medlemsnavn finnes i kuben, og til å returnere den angitte egenskapen for dette medlemmet.
CUBERANKEDMEMBER	= KUBERANGERTMEDLEM			##	Returnerer det n-te, eller rangerte, medlemmet i et sett. Brukes til å returnere ett eller flere elementer i et sett, for eksempel de 10 beste studentene.
CUBESET			= KUBESETT				##	Definerer et beregnet sett av medlemmer eller tuppeler ved å sende et settuttrykk til kuben på serveren, noe som oppretter settet og deretter returnerer dette settet til Microsoft Office Excel.
CUBESETCOUNT		= KUBESETTANTALL			##	Returnerer antallet elementer i et sett.
CUBEVALUE		= KUBEVERDI				##	Returnerer en aggregert verdi fra en kube.


##
##	Database functions					Databasefunksjoner
##
DAVERAGE		= DGJENNOMSNITT				##	Returnerer gjennomsnittet av merkede databaseposter
DCOUNT			= DANTALL				##	Teller celler som inneholder tall i en database
DCOUNTA			= DANTALLA				##	Teller celler som ikke er tomme i en database
DGET			= DHENT					##	Trekker ut fra en database en post som oppfyller angitte vilkår
DMAX			= DMAKS					##	Returnerer maksimumsverdien fra merkede databaseposter
DMIN			= DMIN					##	Returnerer minimumsverdien fra merkede databaseposter
DPRODUCT		= DPRODUKT				##	Multipliserer verdiene i et bestemt felt med poster som oppfyller vilkårene i en database
DSTDEV			= DSTDAV				##	Estimerer standardavviket basert på et utvalg av merkede databaseposter
DSTDEVP			= DSTAVP				##	Beregner standardavviket basert på at merkede databaseposter utgjør hele populasjonen
DSUM			= DSUMMER				##	Legger til tallene i feltkolonnen med poster, i databasen som oppfyller vilkårene
DVAR			= DVARIANS				##	Estimerer variansen basert på et utvalg av merkede databaseposter
DVARP			= DVARIANSP				##	Beregner variansen basert på at merkede databaseposter utgjør hele populasjonen


##
##	Date and time functions					Dato- og tidsfunksjoner
##
DATE			= DATO					##	Returnerer serienummeret som svarer til en bestemt dato
DATEVALUE		= DATOVERDI				##	Konverterer en dato med tekstformat til et serienummer
DAY			= DAG					##	Konverterer et serienummer til en dag i måneden
DAYS360			= DAGER360				##	Beregner antall dager mellom to datoer basert på et år med 360 dager
EDATE			= DAG.ETTER				##	Returnerer serienummeret som svarer til datoen som er det indikerte antall måneder før eller etter startdatoen
EOMONTH			= MÅNEDSSLUTT				##	Returnerer serienummeret som svarer til siste dag i måneden, før eller etter et angitt antall måneder
HOUR			= TIME					##	Konverterer et serienummer til en time
MINUTE			= MINUTT				##	Konverterer et serienummer til et minutt
MONTH			= MÅNED					##	Konverterer et serienummer til en måned
NETWORKDAYS		= NETT.ARBEIDSDAGER			##	Returnerer antall hele arbeidsdager mellom to datoer
NOW			= NÅ					##	Returnerer serienummeret som svarer til gjeldende dato og klokkeslett
SECOND			= SEKUND				##	Konverterer et serienummer til et sekund
TIME			= TID					##	Returnerer serienummeret som svarer til et bestemt klokkeslett
TIMEVALUE		= TIDSVERDI				##	Konverterer et klokkeslett i tekstformat til et serienummer
TODAY			= IDAG					##	Returnerer serienummeret som svarer til dagens dato
WEEKDAY			= UKEDAG				##	Konverterer et serienummer til en ukedag
WEEKNUM			= UKENR					##	Konverterer et serienummer til et tall som representerer hvilket nummer uken har i et år
WORKDAY			= ARBEIDSDAG				##	Returnerer serienummeret som svarer til datoen før eller etter et angitt antall arbeidsdager
YEAR			= ÅR					##	Konverterer et serienummer til et år
YEARFRAC		= ÅRDEL					##	Returnerer brøkdelen for året, som svarer til antall hele dager mellom startdato og sluttdato


##
##	Engineering functions					Tekniske funksjoner
##
BESSELI			= BESSELI				##	Returnerer den endrede Bessel-funksjonen In(x)
BESSELJ			= BESSELJ				##	Returnerer Bessel-funksjonen Jn(x)
BESSELK			= BESSELK				##	Returnerer den endrede Bessel-funksjonen Kn(x)
BESSELY			= BESSELY				##	Returnerer Bessel-funksjonen Yn(x)
BIN2DEC			= BINTILDES				##	Konverterer et binært tall til et desimaltall
BIN2HEX			= BINTILHEKS				##	Konverterer et binært tall til et heksadesimaltall
BIN2OCT			= BINTILOKT				##	Konverterer et binært tall til et oktaltall
COMPLEX			= KOMPLEKS				##	Konverterer reelle og imaginære koeffisienter til et komplekst tall
CONVERT			= KONVERTER				##	Konverterer et tall fra ett målsystem til et annet
DEC2BIN			= DESTILBIN				##	Konverterer et desimaltall til et binærtall
DEC2HEX			= DESTILHEKS				##	Konverterer et heltall i 10-tallsystemet til et heksadesimalt tall
DEC2OCT			= DESTILOKT				##	Konverterer et heltall i 10-tallsystemet til et oktaltall
DELTA			= DELTA					##	Undersøker om to verdier er like
ERF			= FEILF					##	Returnerer feilfunksjonen
ERFC			= FEILFK				##	Returnerer den komplementære feilfunksjonen
GESTEP			= GRENSEVERDI				##	Tester om et tall er større enn en terskelverdi
HEX2BIN			= HEKSTILBIN				##	Konverterer et heksadesimaltall til et binært tall
HEX2DEC			= HEKSTILDES				##	Konverterer et heksadesimalt tall til et heltall i 10-tallsystemet
HEX2OCT			= HEKSTILOKT				##	Konverterer et heksadesimalt tall til et oktaltall
IMABS			= IMABS					##	Returnerer absoluttverdien (koeffisienten) til et komplekst tall
IMAGINARY		= IMAGINÆR				##	Returnerer den imaginære koeffisienten til et komplekst tall
IMARGUMENT		= IMARGUMENT				##	Returnerer argumentet theta, som er en vinkel uttrykt i radianer
IMCONJUGATE		= IMKONJUGERT				##	Returnerer den komplekse konjugaten til et komplekst tall
IMCOS			= IMCOS					##	Returnerer cosinus til et komplekst tall
IMDIV			= IMDIV					##	Returnerer kvotienten til to komplekse tall
IMEXP			= IMEKSP				##	Returnerer eksponenten til et komplekst tall
IMLN			= IMLN					##	Returnerer den naturlige logaritmen for et komplekst tall
IMLOG10			= IMLOG10				##	Returnerer logaritmen med grunntall 10 for et komplekst tall
IMLOG2			= IMLOG2				##	Returnerer logaritmen med grunntall 2 for et komplekst tall
IMPOWER			= IMOPPHØY				##	Returnerer et komplekst tall opphøyd til en heltallspotens
IMPRODUCT		= IMPRODUKT				##	Returnerer produktet av komplekse tall
IMREAL			= IMREELL				##	Returnerer den reelle koeffisienten til et komplekst tall
IMSIN			= IMSIN					##	Returnerer sinus til et komplekst tall
IMSQRT			= IMROT					##	Returnerer kvadratroten av et komplekst tall
IMSUB			= IMSUB					##	Returnerer differansen mellom to komplekse tall
IMSUM			= IMSUMMER				##	Returnerer summen av komplekse tall
OCT2BIN			= OKTTILBIN				##	Konverterer et oktaltall til et binært tall
OCT2DEC			= OKTTILDES				##	Konverterer et oktaltall til et desimaltall
OCT2HEX			= OKTTILHEKS				##	Konverterer et oktaltall til et heksadesimaltall


##
##	Financial functions					Økonomiske funksjoner
##
ACCRINT			= PÅLØPT.PERIODISK.RENTE		##	Returnerer påløpte renter for et verdipapir som betaler periodisk rente
ACCRINTM		= PÅLØPT.FORFALLSRENTE			##	Returnerer den påløpte renten for et verdipapir som betaler rente ved forfall
AMORDEGRC		= AMORDEGRC				##	Returnerer avskrivningen for hver regnskapsperiode ved hjelp av en avskrivingskoeffisient
AMORLINC		= AMORLINC				##	Returnerer avskrivingen for hver regnskapsperiode
COUPDAYBS		= OBLIG.DAGER.FF			##	Returnerer antall dager fra begynnelsen av den rentebærende perioden til innløsningsdatoen
COUPDAYS		= OBLIG.DAGER				##	Returnerer antall dager i den rentebærende perioden som inneholder innløsningsdatoen
COUPDAYSNC		= OBLIG.DAGER.NF			##	Returnerer antall dager fra betalingsdato til neste renteinnbetalingsdato
COUPNCD			= OBLIG.DAGER.EF			##	Returnerer obligasjonsdatoen som kommer etter oppgjørsdatoen
COUPNUM			= OBLIG.ANTALL				##	Returnerer antall obligasjoner som skal betales mellom oppgjørsdatoen og forfallsdatoen
COUPPCD			= OBLIG.DAG.FORRIGE			##	Returnerer obligasjonsdatoen som kommer før oppgjørsdatoen
CUMIPMT			= SAMLET.RENTE				##	Returnerer den kumulative renten som er betalt mellom to perioder
CUMPRINC		= SAMLET.HOVEDSTOL			##	Returnerer den kumulative hovedstolen som er betalt for et lån mellom to perioder
DB			= DAVSKR				##	Returnerer avskrivningen for et aktivum i en angitt periode, foretatt med fast degressiv avskrivning
DDB			= DEGRAVS				##	Returnerer avskrivningen for et aktivum for en gitt periode, ved hjelp av dobbel degressiv avskrivning eller en metode som du selv angir
DISC			= DISKONTERT				##	Returnerer diskonteringsraten for et verdipapir
DOLLARDE		= DOLLARDE				##	Konverterer en valutapris uttrykt som en brøk, til en valutapris uttrykt som et desimaltall
DOLLARFR		= DOLLARBR				##	Konverterer en valutapris uttrykt som et desimaltall, til en valutapris uttrykt som en brøk
DURATION		= VARIGHET				##	Returnerer årlig varighet for et verdipapir med renter som betales periodisk
EFFECT			= EFFEKTIV.RENTE			##	Returnerer den effektive årlige rentesatsen
FV			= SLUTTVERDI				##	Returnerer fremtidig verdi for en investering
FVSCHEDULE		= SVPLAN				##	Returnerer den fremtidige verdien av en inngående hovedstol etter å ha anvendt en serie med sammensatte rentesatser
INTRATE			= RENTESATS				##	Returnerer rentefoten av et fullfinansiert verdipapir
IPMT			= RAVDRAG				##	Returnerer betalte renter på en investering for en gitt periode
IRR			= IR					##	Returnerer internrenten for en serie kontantstrømmer
ISPMT			= ER.AVDRAG				##	Beregner renten som er betalt for en investering i løpet av en bestemt periode
MDURATION		= MVARIGHET				##	Returnerer Macauleys modifiserte varighet for et verdipapir med en antatt pålydende verdi på kr 100,00
MIRR			= MODIR					##	Returnerer internrenten der positive og negative kontantstrømmer finansieres med forskjellige satser
NOMINAL			= NOMINELL				##	Returnerer årlig nominell rentesats
NPER			= PERIODER				##	Returnerer antall perioder for en investering
NPV			= NNV					##	Returnerer netto nåverdi for en investering, basert på en serie periodiske kontantstrømmer og en rentesats
ODDFPRICE		= AVVIKFP.PRIS				##	Returnerer pris pålydende kr 100 for et verdipapir med en odde første periode
ODDFYIELD		= AVVIKFP.AVKASTNING			##	Returnerer avkastingen for et verdipapir med en odde første periode
ODDLPRICE		= AVVIKSP.PRIS				##	Returnerer pris pålydende kr 100 for et verdipapir med en odde siste periode
ODDLYIELD		= AVVIKSP.AVKASTNING			##	Returnerer avkastingen for et verdipapir med en odde siste periode
PMT			= AVDRAG				##	Returnerer periodisk betaling for en annuitet
PPMT			= AMORT					##	Returnerer betalingen på hovedstolen for en investering i en gitt periode
PRICE			= PRIS					##	Returnerer prisen per pålydende kr 100 for et verdipapir som gir periodisk avkastning
PRICEDISC		= PRIS.DISKONTERT			##	Returnerer prisen per pålydende kr 100 for et diskontert verdipapir
PRICEMAT		= PRIS.FORFALL				##	Returnerer prisen per pålydende kr 100 av et verdipapir som betaler rente ved forfall
PV			= NÅVERDI				##	Returnerer nåverdien av en investering
RATE			= RENTE					##	Returnerer rentesatsen per periode for en annuitet
RECEIVED		= MOTTATT.AVKAST			##	Returnerer summen som mottas ved forfallsdato for et fullinvestert verdipapir
SLN			= LINAVS				##	Returnerer den lineære avskrivningen for et aktivum i én periode
SYD			= ÅRSAVS				##	Returnerer årsavskrivningen for et aktivum i en angitt periode
TBILLEQ			= TBILLEKV				##	Returnerer den obligasjonsekvivalente avkastningen for en statsobligasjon
TBILLPRICE		= TBILLPRIS				##	Returnerer prisen per pålydende kr 100 for en statsobligasjon
TBILLYIELD		= TBILLAVKASTNING			##	Returnerer avkastningen til en statsobligasjon
VDB			= VERDIAVS				##	Returnerer avskrivningen for et aktivum i en angitt periode eller delperiode, ved hjelp av degressiv avskrivning
XIRR			= XIR					##	Returnerer internrenten for en serie kontantstrømmer som ikke nødvendigvis er periodiske
XNPV			= XNNV					##	Returnerer netto nåverdi for en serie kontantstrømmer som ikke nødvendigvis er periodiske
YIELD			= AVKAST				##	Returnerer avkastningen på et verdipapir som betaler periodisk rente
YIELDDISC		= AVKAST.DISKONTERT			##	Returnerer årlig avkastning for et diskontert verdipapir, for eksempel en statskasseveksel
YIELDMAT		= AVKAST.FORFALL			##	Returnerer den årlige avkastningen for et verdipapir som betaler rente ved forfallsdato


##
##	Information functions					Informasjonsfunksjoner
##
CELL			= CELLE					##	Returnerer informasjon om formatering, plassering eller innholdet til en celle
ERROR.TYPE		= FEIL.TYPE				##	Returnerer et tall som svarer til en feiltype
INFO			= INFO					##	Returnerer informasjon om gjeldende operativmiljø
ISBLANK			= ERTOM					##	Returnerer SANN hvis verdien er tom
ISERR			= ERFEIL				##	Returnerer SANN hvis verdien er en hvilken som helst annen feilverdi enn #I/T
ISERROR			= ERFEIL				##	Returnerer SANN hvis verdien er en hvilken som helst feilverdi
ISEVEN			= ERPARTALL				##	Returnerer SANN hvis tallet er et partall
ISLOGICAL		= ERLOGISK				##	Returnerer SANN hvis verdien er en logisk verdi
ISNA			= ERIT					##	Returnerer SANN hvis verdien er feilverdien #I/T
ISNONTEXT		= ERIKKETEKST				##	Returnerer SANN hvis verdien ikke er tekst
ISNUMBER		= ERTALL				##	Returnerer SANN hvis verdien er et tall
ISODD			= ERODDETALL				##	Returnerer SANN hvis tallet er et oddetall
ISREF			= ERREF					##	Returnerer SANN hvis verdien er en referanse
ISTEXT			= ERTEKST				##	Returnerer SANN hvis verdien er tekst
N			= N					##	Returnerer en verdi som er konvertert til et tall
NA			= IT					##	Returnerer feilverdien #I/T
TYPE			= VERDITYPE				##	Returnerer et tall som indikerer datatypen til en verdi


##
##	Logical functions					Logiske funksjoner
##
AND			= OG					##	Returnerer SANN hvis alle argumentene er lik SANN
FALSE			= USANN					##	Returnerer den logiske verdien USANN
IF			= HVIS					##	Angir en logisk test som skal utføres
IFERROR			= HVISFEIL				##	Returnerer en verdi du angir hvis en formel evaluerer til en feil. Ellers returnerer den resultatet av formelen.
NOT			= IKKE					##	Reverserer logikken til argumentet
OR			= ELLER					##	Returnerer SANN hvis ett eller flere argumenter er lik SANN
TRUE			= SANN					##	Returnerer den logiske verdien SANN


##
##	Lookup and reference functions				Oppslag- og referansefunksjoner
##
ADDRESS			= ADRESSE				##	Returnerer en referanse som tekst til en enkelt celle i et regneark
AREAS			= OMRÅDER				##	Returnerer antall områder i en referanse
CHOOSE			= VELG					##	Velger en verdi fra en liste med verdier
COLUMN			= KOLONNE				##	Returnerer kolonnenummeret for en referanse
COLUMNS			= KOLONNER				##	Returnerer antall kolonner i en referanse
HLOOKUP			= FINN.KOLONNE				##	Leter i den øverste raden i en matrise og returnerer verdien for den angitte cellen
HYPERLINK		= HYPERKOBLING				##	Oppretter en snarvei eller et hopp som åpner et dokument som er lagret på en nettverksserver, et intranett eller Internett
INDEX			= INDEKS				##	Bruker en indeks til å velge en verdi fra en referanse eller matrise
INDIRECT		= INDIREKTE				##	Returnerer en referanse angitt av en tekstverdi
LOOKUP			= SLÅ.OPP				##	Slår opp verdier i en vektor eller matrise
MATCH			= SAMMENLIGNE				##	Slår opp verdier i en referanse eller matrise
OFFSET			= FORSKYVNING				##	Returnerer en referanseforskyvning fra en gitt referanse
ROW			= RAD					##	Returnerer radnummeret for en referanse
ROWS			= RADER					##	Returnerer antall rader i en referanse
RTD			= RTD					##	Henter sanntidsdata fra et program som støtter COM-automatisering (automatisering: En måte å arbeide på med programobjekter fra et annet program- eller utviklingsverktøy. Tidligere kalt OLE-automatisering. Automatisering er en bransjestandard og en funksjon i Component Object Model (COM).)
TRANSPOSE		= TRANSPONER				##	Returnerer transponeringen av en matrise
VLOOKUP			= FINN.RAD				##	Leter i den første kolonnen i en matrise og flytter bortover raden for å returnere verdien til en celle


##
##	Math and trigonometry functions				Matematikk- og trigonometrifunksjoner
##
ABS			= ABS					##	Returnerer absoluttverdien til et tall
ACOS			= ARCCOS				##	Returnerer arcus cosinus til et tall
ACOSH			= ARCCOSH				##	Returnerer den inverse hyperbolske cosinus til et tall
ASIN			= ARCSIN				##	Returnerer arcus sinus til et tall
ASINH			= ARCSINH				##	Returnerer den inverse hyperbolske sinus til et tall
ATAN			= ARCTAN				##	Returnerer arcus tangens til et tall
ATAN2			= ARCTAN2				##	Returnerer arcus tangens fra x- og y-koordinater
ATANH			= ARCTANH				##	Returnerer den inverse hyperbolske tangens til et tall
CEILING			= AVRUND.GJELDENDE.MULTIPLUM		##	Runder av et tall til nærmeste heltall eller til nærmeste signifikante multiplum
COMBIN			= KOMBINASJON				##	Returnerer antall kombinasjoner for ett gitt antall objekter
COS			= COS					##	Returnerer cosinus til et tall
COSH			= COSH					##	Returnerer den hyperbolske cosinus til et tall
DEGREES			= GRADER				##	Konverterer radianer til grader
EVEN			= AVRUND.TIL.PARTALL			##	Runder av et tall oppover til nærmeste heltall som er et partall
EXP			= EKSP					##	Returnerer e opphøyd i en angitt potens
FACT			= FAKULTET				##	Returnerer fakultet til et tall
FACTDOUBLE		= DOBBELFAKT				##	Returnerer et talls doble fakultet
FLOOR			= AVRUND.GJELDENDE.MULTIPLUM.NED	##	Avrunder et tall nedover, mot null
GCD			= SFF					##	Returnerer høyeste felles divisor
INT			= HELTALL				##	Avrunder et tall nedover til nærmeste heltall
LCM			= MFM					##	Returnerer minste felles multiplum
LN			= LN					##	Returnerer den naturlige logaritmen til et tall
LOG			= LOG					##	Returnerer logaritmen for et tall til et angitt grunntall
LOG10			= LOG10					##	Returnerer logaritmen med grunntall 10 for et tall
MDETERM			= MDETERM				##	Returnerer matrisedeterminanten til en matrise
MINVERSE		= MINVERS				##	Returnerer den inverse matrisen til en matrise
MMULT			= MMULT					##	Returnerer matriseproduktet av to matriser
MOD			= REST					##	Returnerer resten fra en divisjon
MROUND			= MRUND					##	Returnerer et tall avrundet til det ønskede multiplum
MULTINOMIAL		= MULTINOMINELL				##	Returnerer det multinominelle for et sett med tall
ODD			= AVRUND.TIL.ODDETALL			##	Runder av et tall oppover til nærmeste heltall som er et oddetall
PI			= PI					##	Returnerer verdien av pi
POWER			= OPPHØYD.I				##	Returnerer resultatet av et tall opphøyd i en potens
PRODUCT			= PRODUKT				##	Multipliserer argumentene
QUOTIENT		= KVOTIENT				##	Returnerer heltallsdelen av en divisjon
RADIANS			= RADIANER				##	Konverterer grader til radianer
RAND			= TILFELDIG				##	Returnerer et tilfeldig tall mellom 0 og 1
RANDBETWEEN		= TILFELDIGMELLOM			##	Returnerer et tilfeldig tall innenfor et angitt område
ROMAN			= ROMERTALL				##	Konverterer vanlige tall til romertall, som tekst
ROUND			= AVRUND				##	Avrunder et tall til et angitt antall sifre
ROUNDDOWN		= AVRUND.NED				##	Avrunder et tall nedover, mot null
ROUNDUP			= AVRUND.OPP				##	Runder av et tall oppover, bort fra null
SERIESSUM		= SUMMER.REKKE				##	Returnerer summen av en geometrisk rekke, basert på formelen
SIGN			= FORTEGN				##	Returnerer fortegnet for et tall
SIN			= SIN					##	Returnerer sinus til en gitt vinkel
SINH			= SINH					##	Returnerer den hyperbolske sinus til et tall
SQRT			= ROT					##	Returnerer en positiv kvadratrot
SQRTPI			= ROTPI					##	Returnerer kvadratroten av (tall * pi)
SUBTOTAL		= DELSUM				##	Returnerer en delsum i en liste eller database
SUM			= SUMMER				##	Legger sammen argumentene
SUMIF			= SUMMERHVIS				##	Legger sammen cellene angitt ved et gitt vilkår
SUMIFS			= SUMMER.HVIS.SETT			##	Legger sammen cellene i et område som oppfyller flere vilkår
SUMPRODUCT		= SUMMERPRODUKT				##	Returnerer summen av produktene av tilsvarende matrisekomponenter
SUMSQ			= SUMMERKVADRAT				##	Returnerer kvadratsummen av argumentene
SUMX2MY2		= SUMMERX2MY2				##	Returnerer summen av differansen av kvadratene for tilsvarende verdier i to matriser
SUMX2PY2		= SUMMERX2PY2				##	Returnerer summen av kvadratsummene for tilsvarende verdier i to matriser
SUMXMY2			= SUMMERXMY2				##	Returnerer summen av kvadratene av differansen for tilsvarende verdier i to matriser
TAN			= TAN					##	Returnerer tangens for et tall
TANH			= TANH					##	Returnerer den hyperbolske tangens for et tall
TRUNC			= AVKORT				##	Korter av et tall til et heltall


##
##	Statistical functions					Statistiske funksjoner
##
AVEDEV			= GJENNOMSNITTSAVVIK			##	Returnerer datapunktenes gjennomsnittlige absoluttavvik fra middelverdien
AVERAGE			= GJENNOMSNITT				##	Returnerer gjennomsnittet for argumentene
AVERAGEA		= GJENNOMSNITTA				##	Returnerer gjennomsnittet for argumentene, inkludert tall, tekst og logiske verdier
AVERAGEIF		= GJENNOMSNITTHVIS			##	Returnerer gjennomsnittet (aritmetisk gjennomsnitt) av alle cellene i et område som oppfyller et bestemt vilkår
AVERAGEIFS		= GJENNOMSNITT.HVIS.SETT		##	Returnerer gjennomsnittet (aritmetisk middelverdi) av alle celler som oppfyller flere vilkår.
BETADIST		= BETA.FORDELING			##	Returnerer den kumulative betafordelingsfunksjonen
BETAINV			= INVERS.BETA.FORDELING			##	Returnerer den inverse verdien til fordelingsfunksjonen for en angitt betafordeling
BINOMDIST		= BINOM.FORDELING			##	Returnerer den individuelle binomiske sannsynlighetsfordelingen
CHIDIST			= KJI.FORDELING				##	Returnerer den ensidige sannsynligheten for en kjikvadrert fordeling
CHIINV			= INVERS.KJI.FORDELING			##	Returnerer den inverse av den ensidige sannsynligheten for den kjikvadrerte fordelingen
CHITEST			= KJI.TEST				##	Utfører testen for uavhengighet
CONFIDENCE		= KONFIDENS				##	Returnerer konfidensintervallet til gjennomsnittet for en populasjon
CORREL			= KORRELASJON				##	Returnerer korrelasjonskoeffisienten mellom to datasett
COUNT			= ANTALL				##	Teller hvor mange tall som er i argumentlisten
COUNTA			= ANTALLA				##	Teller hvor mange verdier som er i argumentlisten
COUNTBLANK		= TELLBLANKE				##	Teller antall tomme celler i et område.
COUNTIF			= ANTALL.HVIS				##	Teller antall celler i et område som oppfyller gitte vilkår
COUNTIFS		= ANTALL.HVIS.SETT			##	Teller antallet ikke-tomme celler i et område som oppfyller flere vilkår
COVAR			= KOVARIANS				##	Returnerer kovariansen, gjennomsnittet av produktene av parvise avvik
CRITBINOM		= GRENSE.BINOM				##	Returnerer den minste verdien der den kumulative binomiske fordelingen er mindre enn eller lik en vilkårsverdi
DEVSQ			= AVVIK.KVADRERT			##	Returnerer summen av kvadrerte avvik
EXPONDIST		= EKSP.FORDELING			##	Returnerer eksponentialfordelingen
FDIST			= FFORDELING				##	Returnerer F-sannsynlighetsfordelingen
FINV			= FFORDELING.INVERS			##	Returnerer den inverse av den sannsynlige F-fordelingen
FISHER			= FISHER				##	Returnerer Fisher-transformasjonen
FISHERINV		= FISHERINV				##	Returnerer den inverse av Fisher-transformasjonen
FORECAST		= PROGNOSE				##	Returnerer en verdi langs en lineær trend
FREQUENCY		= FREKVENS				##	Returnerer en frekvensdistribusjon som en loddrett matrise
FTEST			= FTEST					##	Returnerer resultatet av en F-test
GAMMADIST		= GAMMAFORDELING			##	Returnerer gammafordelingen
GAMMAINV		= GAMMAINV				##	Returnerer den inverse av den gammakumulative fordelingen
GAMMALN			= GAMMALN				##	Returnerer den naturlige logaritmen til gammafunksjonen G(x)
GEOMEAN			= GJENNOMSNITT.GEOMETRISK		##	Returnerer den geometriske middelverdien
GROWTH			= VEKST					##	Returnerer verdier langs en eksponentiell trend
HARMEAN			= GJENNOMSNITT.HARMONISK		##	Returnerer den harmoniske middelverdien
HYPGEOMDIST		= HYPGEOM.FORDELING			##	Returnerer den hypergeometriske fordelingen
INTERCEPT		= SKJÆRINGSPUNKT			##	Returnerer skjæringspunktet til den lineære regresjonslinjen
KURT			= KURT					##	Returnerer kurtosen til et datasett
LARGE			= N.STØRST				##	Returnerer den n-te største verdien i et datasett
LINEST			= RETTLINJE				##	Returnerer parameterne til en lineær trend
LOGEST			= KURVE					##	Returnerer parameterne til en eksponentiell trend
LOGINV			= LOGINV				##	Returnerer den inverse lognormale fordelingen
LOGNORMDIST		= LOGNORMFORD				##	Returnerer den kumulative lognormale fordelingen
MAX			= STØRST				##	Returnerer maksimumsverdien i en argumentliste
MAXA			= MAKSA					##	Returnerer maksimumsverdien i en argumentliste, inkludert tall, tekst og logiske verdier
MEDIAN			= MEDIAN				##	Returnerer medianen til tallene som er gitt
MIN			= MIN					##	Returnerer minimumsverdien i en argumentliste
MINA			= MINA					##	Returnerer den minste verdien i en argumentliste, inkludert tall, tekst og logiske verdier
MODE			= MODUS					##	Returnerer den vanligste verdien i et datasett
NEGBINOMDIST		= NEGBINOM.FORDELING			##	Returnerer den negative binomiske fordelingen
NORMDIST		= NORMALFORDELING			##	Returnerer den kumulative normalfordelingen
NORMINV			= NORMINV				##	Returnerer den inverse kumulative normalfordelingen
NORMSDIST		= NORMSFORDELING			##	Returnerer standard kumulativ normalfordeling
NORMSINV		= NORMSINV				##	Returnerer den inverse av den den kumulative standard normalfordelingen
PEARSON			= PEARSON				##	Returnerer produktmomentkorrelasjonskoeffisienten, Pearson
PERCENTILE		= PERSENTIL				##	Returnerer den n-te persentil av verdiene i et område
PERCENTRANK		= PROSENTDEL				##	Returnerer prosentrangeringen av en verdi i et datasett
PERMUT			= PERMUTER				##	Returnerer antall permutasjoner for et gitt antall objekter
POISSON			= POISSON				##	Returnerer Poissons sannsynlighetsfordeling
PROB			= SANNSYNLIG				##	Returnerer sannsynligheten for at verdier i et område ligger mellom to grenser
QUARTILE		= KVARTIL				##	Returnerer kvartilen til et datasett
RANK			= RANG					##	Returnerer rangeringen av et tall, eller plassen tallet har i en rekke
RSQ			= RKVADRAT				##	Returnerer kvadratet av produktmomentkorrelasjonskoeffisienten (Pearsons r)
SKEW			= SKJEVFORDELING			##	Returnerer skjevheten i en fordeling
SLOPE			= STIGNINGSTALL				##	Returnerer stigningtallet for den lineære regresjonslinjen
SMALL			= N.MINST				##	Returnerer den n-te minste verdien i et datasett
STANDARDIZE		= NORMALISER				##	Returnerer en normalisert verdi
STDEV			= STDAV					##	Estimere standardavvik på grunnlag av et utvalg
STDEVA			= STDAVVIKA				##	Estimerer standardavvik basert på et utvalg, inkludert tall, tekst og logiske verdier
STDEVP			= STDAVP				##	Beregner standardavvik basert på hele populasjonen
STDEVPA			= STDAVVIKPA				##	Beregner standardavvik basert på hele populasjonen, inkludert tall, tekst og logiske verdier
STEYX			= STANDARDFEIL				##	Returnerer standardfeilen for den predikerte y-verdien for hver x i regresjonen
TDIST			= TFORDELING				##	Returnerer en Student t-fordeling
TINV			= TINV					##	Returnerer den inverse Student t-fordelingen
TREND			= TREND					##	Returnerer verdier langs en lineær trend
TRIMMEAN		= TRIMMET.GJENNOMSNITT			##	Returnerer den interne middelverdien til et datasett
TTEST			= TTEST					##	Returnerer sannsynligheten assosiert med en Student t-test
VAR			= VARIANS				##	Estimerer varians basert på et utvalg
VARA			= VARIANSA				##	Estimerer varians basert på et utvalg, inkludert tall, tekst og logiske verdier
VARP			= VARIANSP				##	Beregner varians basert på hele populasjonen
VARPA			= VARIANSPA				##	Beregner varians basert på hele populasjonen, inkludert tall, tekst og logiske verdier
WEIBULL			= WEIBULL.FORDELING			##	Returnerer Weibull-fordelingen
ZTEST			= ZTEST					##	Returnerer den ensidige sannsynlighetsverdien for en z-test


##
##	Text functions						Tekstfunksjoner
##
ASC			= STIGENDE				##	Endrer fullbreddes (dobbeltbyte) engelske bokstaver eller katakana i en tegnstreng, til halvbreddes (enkeltbyte) tegn
BAHTTEXT		= BAHTTEKST				##	Konverterer et tall til tekst, og bruker valutaformatet ß (baht)
CHAR			= TEGNKODE				##	Returnerer tegnet som svarer til kodenummeret
CLEAN			= RENSK					##	Fjerner alle tegn som ikke kan skrives ut, fra teksten
CODE			= KODE					##	Returnerer en numerisk kode for det første tegnet i en tekststreng
CONCATENATE		= KJEDE.SAMMEN				##	Slår sammen flere tekstelementer til ett tekstelement
DOLLAR			= VALUTA				##	Konverterer et tall til tekst, og bruker valutaformatet $ (dollar)
EXACT			= EKSAKT				##	Kontrollerer om to tekstverdier er like
FIND			= FINN					##	Finner en tekstverdi inne i en annen (skiller mellom store og små bokstaver)
FINDB			= FINNB					##	Finner en tekstverdi inne i en annen (skiller mellom store og små bokstaver)
FIXED			= FASTSATT				##	Formaterer et tall som tekst med et bestemt antall desimaler
JIS			= JIS					##	Endrer halvbreddes (enkeltbyte) engelske bokstaver eller katakana i en tegnstreng, til fullbreddes (dobbeltbyte) tegn
LEFT			= VENSTRE				##	Returnerer tegnene lengst til venstre i en tekstverdi
LEFTB			= VENSTREB				##	Returnerer tegnene lengst til venstre i en tekstverdi
LEN			= LENGDE				##	Returnerer antall tegn i en tekststreng
LENB			= LENGDEB				##	Returnerer antall tegn i en tekststreng
LOWER			= SMÅ					##	Konverterer tekst til små bokstaver
MID			= DELTEKST				##	Returnerer et angitt antall tegn fra en tekststreng, og begynner fra posisjonen du angir
MIDB			= DELTEKSTB				##	Returnerer et angitt antall tegn fra en tekststreng, og begynner fra posisjonen du angir
PHONETIC		= FURIGANA				##	Trekker ut fonetiske tegn (furigana) fra en tekststreng
PROPER			= STOR.FORBOKSTAV			##	Gir den første bokstaven i hvert ord i en tekstverdi stor forbokstav
REPLACE			= ERSTATT				##	Erstatter tegn i en tekst
REPLACEB		= ERSTATTB				##	Erstatter tegn i en tekst
REPT			= GJENTA				##	Gjentar tekst et gitt antall ganger
RIGHT			= HØYRE					##	Returnerer tegnene lengst til høyre i en tekstverdi
RIGHTB			= HØYREB				##	Returnerer tegnene lengst til høyre i en tekstverdi
SEARCH			= SØK					##	Finner en tekstverdi inne i en annen (skiller ikke mellom store og små bokstaver)
SEARCHB			= SØKB					##	Finner en tekstverdi inne i en annen (skiller ikke mellom store og små bokstaver)
SUBSTITUTE		= BYTT.UT				##	Bytter ut gammel tekst med ny tekst i en tekststreng
T			= T					##	Konverterer argumentene til tekst
TEXT			= TEKST					##	Formaterer et tall og konverterer det til tekst
TRIM			= TRIMME				##	Fjerner mellomrom fra tekst
UPPER			= STORE					##	Konverterer tekst til store bokstaver
VALUE			= VERDI					##	Konverterer et tekstargument til et tall
