{"name": "psr-discovery/container-implementations", "description": "Lightweight library that discovers available PSR-11 Container implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "license": "MIT", "type": "library", "keywords": ["psr", "discovery", "psr-11"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "homepage": "https://github.com/psr-discovery/http-client-implementations", "require": {"php": "^8.1", "psr/container": "^1.0 | ^2.0", "psr-discovery/discovery": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "autoload-dev": {"psr-4": {"PsrDiscovery\\Tests\\": "tests"}}, "config": {"allow-plugins": {"infection/extension-installer": true, "pestphp/pest-plugin": true, "wikimedia/composer-merge-plugin": true}, "optimize-autoloader": true, "preferred-install": "dist", "process-timeout": 0, "sort-packages": true}, "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "scripts": {"mutate": "@php ./vendor/bin/infection --test-framework=pest --show-mutations", "pest:coverage": "@php vendor/bin/pest --order-by random --compact --coverage", "pest": "@php vendor/bin/pest --order-by random --compact", "phpcs:fix": "@php vendor/bin/php-cs-fixer fix src", "phpcs": "@php vendor/bin/php-cs-fixer fix src --dry-run --diff", "phpstan": "@php vendor/bin/phpstan analyze", "psalm:fix": "@php vendor/bin/psalter --issues=all", "psalm": "@php vendor/bin/psalm", "rector:fix": "@php vendor/bin/rector process src", "rector": "@php vendor/bin/rector process src --dry-run", "test": ["@pest", "@phpstan", "@psalm", "@rector", "@phpcs"]}}