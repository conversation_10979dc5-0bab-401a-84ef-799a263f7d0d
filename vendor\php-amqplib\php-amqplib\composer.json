{"name": "videlalvaro/php-amqplib", "type": "library", "description": "This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "keywords": ["rabbitmq", "message", "queue"], "homepage": "https://github.com/videlalvaro/php-amqplib/", "authors": [{"name": "<PERSON><PERSON>"}], "require": {"php": ">=5.3.0", "ext-bcmath": "*"}, "autoload": {"psr-0": {"PhpAmqpLib": ""}}, "license": "LGPL-2.1"}