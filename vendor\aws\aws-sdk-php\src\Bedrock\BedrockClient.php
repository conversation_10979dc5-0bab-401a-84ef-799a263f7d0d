<?php
namespace Aws\Bedrock;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Bedrock** service.
 * @method \Aws\Result createEvaluationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEvaluationJobAsync(array $args = [])
 * @method \Aws\Result createGuardrail(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createGuardrailAsync(array $args = [])
 * @method \Aws\Result createGuardrailVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createGuardrailVersionAsync(array $args = [])
 * @method \Aws\Result createModelCopyJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createModelCopyJobAsync(array $args = [])
 * @method \Aws\Result createModelCustomizationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createModelCustomizationJobAsync(array $args = [])
 * @method \Aws\Result createModelInvocationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createModelInvocationJobAsync(array $args = [])
 * @method \Aws\Result createProvisionedModelThroughput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createProvisionedModelThroughputAsync(array $args = [])
 * @method \Aws\Result deleteCustomModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCustomModelAsync(array $args = [])
 * @method \Aws\Result deleteGuardrail(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteGuardrailAsync(array $args = [])
 * @method \Aws\Result deleteModelInvocationLoggingConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteModelInvocationLoggingConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteProvisionedModelThroughput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteProvisionedModelThroughputAsync(array $args = [])
 * @method \Aws\Result getCustomModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCustomModelAsync(array $args = [])
 * @method \Aws\Result getEvaluationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEvaluationJobAsync(array $args = [])
 * @method \Aws\Result getFoundationModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFoundationModelAsync(array $args = [])
 * @method \Aws\Result getGuardrail(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getGuardrailAsync(array $args = [])
 * @method \Aws\Result getModelCopyJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getModelCopyJobAsync(array $args = [])
 * @method \Aws\Result getModelCustomizationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getModelCustomizationJobAsync(array $args = [])
 * @method \Aws\Result getModelInvocationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getModelInvocationJobAsync(array $args = [])
 * @method \Aws\Result getModelInvocationLoggingConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getModelInvocationLoggingConfigurationAsync(array $args = [])
 * @method \Aws\Result getProvisionedModelThroughput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getProvisionedModelThroughputAsync(array $args = [])
 * @method \Aws\Result listCustomModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCustomModelsAsync(array $args = [])
 * @method \Aws\Result listEvaluationJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEvaluationJobsAsync(array $args = [])
 * @method \Aws\Result listFoundationModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFoundationModelsAsync(array $args = [])
 * @method \Aws\Result listGuardrails(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGuardrailsAsync(array $args = [])
 * @method \Aws\Result listModelCopyJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listModelCopyJobsAsync(array $args = [])
 * @method \Aws\Result listModelCustomizationJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listModelCustomizationJobsAsync(array $args = [])
 * @method \Aws\Result listModelInvocationJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listModelInvocationJobsAsync(array $args = [])
 * @method \Aws\Result listProvisionedModelThroughputs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProvisionedModelThroughputsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putModelInvocationLoggingConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putModelInvocationLoggingConfigurationAsync(array $args = [])
 * @method \Aws\Result stopEvaluationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopEvaluationJobAsync(array $args = [])
 * @method \Aws\Result stopModelCustomizationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopModelCustomizationJobAsync(array $args = [])
 * @method \Aws\Result stopModelInvocationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopModelInvocationJobAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateGuardrail(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateGuardrailAsync(array $args = [])
 * @method \Aws\Result updateProvisionedModelThroughput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateProvisionedModelThroughputAsync(array $args = [])
 */
class BedrockClient extends AwsClient {}
