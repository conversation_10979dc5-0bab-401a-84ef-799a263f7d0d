<?php
// This file was auto-generated from sdk-root/src/data/personalize/2018-05-22/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-22', 'endpointPrefix' => 'personalize', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon Personalize', 'serviceId' => 'Personalize', 'signatureVersion' => 'v4', 'signingName' => 'personalize', 'targetPrefix' => 'AmazonPersonalize', 'uid' => 'personalize-2018-05-22', ], 'operations' => [ 'CreateBatchInferenceJob' => [ 'name' => 'CreateBatchInferenceJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBatchInferenceJobRequest', ], 'output' => [ 'shape' => 'CreateBatchInferenceJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateBatchSegmentJob' => [ 'name' => 'CreateBatchSegmentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBatchSegmentJobRequest', ], 'output' => [ 'shape' => 'CreateBatchSegmentJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateCampaign' => [ 'name' => 'CreateCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCampaignRequest', ], 'output' => [ 'shape' => 'CreateCampaignResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'CreateDataDeletionJob' => [ 'name' => 'CreateDataDeletionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDataDeletionJobRequest', ], 'output' => [ 'shape' => 'CreateDataDeletionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'CreateDatasetExportJob' => [ 'name' => 'CreateDatasetExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetExportJobRequest', ], 'output' => [ 'shape' => 'CreateDatasetExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'CreateDatasetGroup' => [ 'name' => 'CreateDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetGroupRequest', ], 'output' => [ 'shape' => 'CreateDatasetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateDatasetImportJob' => [ 'name' => 'CreateDatasetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetImportJobRequest', ], 'output' => [ 'shape' => 'CreateDatasetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateEventTracker' => [ 'name' => 'CreateEventTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventTrackerRequest', ], 'output' => [ 'shape' => 'CreateEventTrackerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'CreateFilter' => [ 'name' => 'CreateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFilterRequest', ], 'output' => [ 'shape' => 'CreateFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateMetricAttribution' => [ 'name' => 'CreateMetricAttribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMetricAttributionRequest', ], 'output' => [ 'shape' => 'CreateMetricAttributionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateRecommender' => [ 'name' => 'CreateRecommender', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRecommenderRequest', ], 'output' => [ 'shape' => 'CreateRecommenderResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'CreateSchema' => [ 'name' => 'CreateSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSchemaRequest', ], 'output' => [ 'shape' => 'CreateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateSolution' => [ 'name' => 'CreateSolution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSolutionRequest', ], 'output' => [ 'shape' => 'CreateSolutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateSolutionVersion' => [ 'name' => 'CreateSolutionVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSolutionVersionRequest', ], 'output' => [ 'shape' => 'CreateSolutionVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'DeleteCampaign' => [ 'name' => 'DeleteCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCampaignRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteDatasetGroup' => [ 'name' => 'DeleteDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteEventTracker' => [ 'name' => 'DeleteEventTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventTrackerRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteFilter' => [ 'name' => 'DeleteFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFilterRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteMetricAttribution' => [ 'name' => 'DeleteMetricAttribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMetricAttributionRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteRecommender' => [ 'name' => 'DeleteRecommender', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRecommenderRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteSchema' => [ 'name' => 'DeleteSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSchemaRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteSolution' => [ 'name' => 'DeleteSolution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSolutionRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DescribeAlgorithm' => [ 'name' => 'DescribeAlgorithm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAlgorithmRequest', ], 'output' => [ 'shape' => 'DescribeAlgorithmResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeBatchInferenceJob' => [ 'name' => 'DescribeBatchInferenceJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBatchInferenceJobRequest', ], 'output' => [ 'shape' => 'DescribeBatchInferenceJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeBatchSegmentJob' => [ 'name' => 'DescribeBatchSegmentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBatchSegmentJobRequest', ], 'output' => [ 'shape' => 'DescribeBatchSegmentJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeCampaign' => [ 'name' => 'DescribeCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCampaignRequest', ], 'output' => [ 'shape' => 'DescribeCampaignResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDataDeletionJob' => [ 'name' => 'DescribeDataDeletionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDataDeletionJobRequest', ], 'output' => [ 'shape' => 'DescribeDataDeletionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetExportJob' => [ 'name' => 'DescribeDatasetExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetExportJobRequest', ], 'output' => [ 'shape' => 'DescribeDatasetExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetGroup' => [ 'name' => 'DescribeDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetGroupRequest', ], 'output' => [ 'shape' => 'DescribeDatasetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetImportJob' => [ 'name' => 'DescribeDatasetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetImportJobRequest', ], 'output' => [ 'shape' => 'DescribeDatasetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeEventTracker' => [ 'name' => 'DescribeEventTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventTrackerRequest', ], 'output' => [ 'shape' => 'DescribeEventTrackerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeFeatureTransformation' => [ 'name' => 'DescribeFeatureTransformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFeatureTransformationRequest', ], 'output' => [ 'shape' => 'DescribeFeatureTransformationResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeFilter' => [ 'name' => 'DescribeFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFilterRequest', ], 'output' => [ 'shape' => 'DescribeFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeMetricAttribution' => [ 'name' => 'DescribeMetricAttribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMetricAttributionRequest', ], 'output' => [ 'shape' => 'DescribeMetricAttributionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeRecipe' => [ 'name' => 'DescribeRecipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRecipeRequest', ], 'output' => [ 'shape' => 'DescribeRecipeResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeRecommender' => [ 'name' => 'DescribeRecommender', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRecommenderRequest', ], 'output' => [ 'shape' => 'DescribeRecommenderResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeSchema' => [ 'name' => 'DescribeSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSchemaRequest', ], 'output' => [ 'shape' => 'DescribeSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeSolution' => [ 'name' => 'DescribeSolution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSolutionRequest', ], 'output' => [ 'shape' => 'DescribeSolutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeSolutionVersion' => [ 'name' => 'DescribeSolutionVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSolutionVersionRequest', ], 'output' => [ 'shape' => 'DescribeSolutionVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetSolutionMetrics' => [ 'name' => 'GetSolutionMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSolutionMetricsRequest', ], 'output' => [ 'shape' => 'GetSolutionMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'ListBatchInferenceJobs' => [ 'name' => 'ListBatchInferenceJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBatchInferenceJobsRequest', ], 'output' => [ 'shape' => 'ListBatchInferenceJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListBatchSegmentJobs' => [ 'name' => 'ListBatchSegmentJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBatchSegmentJobsRequest', ], 'output' => [ 'shape' => 'ListBatchSegmentJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListCampaigns' => [ 'name' => 'ListCampaigns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCampaignsRequest', ], 'output' => [ 'shape' => 'ListCampaignsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDataDeletionJobs' => [ 'name' => 'ListDataDeletionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataDeletionJobsRequest', ], 'output' => [ 'shape' => 'ListDataDeletionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasetExportJobs' => [ 'name' => 'ListDatasetExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetExportJobsRequest', ], 'output' => [ 'shape' => 'ListDatasetExportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasetGroups' => [ 'name' => 'ListDatasetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetGroupsRequest', ], 'output' => [ 'shape' => 'ListDatasetGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasetImportJobs' => [ 'name' => 'ListDatasetImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetImportJobsRequest', ], 'output' => [ 'shape' => 'ListDatasetImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListEventTrackers' => [ 'name' => 'ListEventTrackers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEventTrackersRequest', ], 'output' => [ 'shape' => 'ListEventTrackersResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListFilters' => [ 'name' => 'ListFilters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFiltersRequest', ], 'output' => [ 'shape' => 'ListFiltersResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListMetricAttributionMetrics' => [ 'name' => 'ListMetricAttributionMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMetricAttributionMetricsRequest', ], 'output' => [ 'shape' => 'ListMetricAttributionMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListMetricAttributions' => [ 'name' => 'ListMetricAttributions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMetricAttributionsRequest', ], 'output' => [ 'shape' => 'ListMetricAttributionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListRecipes' => [ 'name' => 'ListRecipes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRecipesRequest', ], 'output' => [ 'shape' => 'ListRecipesResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListRecommenders' => [ 'name' => 'ListRecommenders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRecommendersRequest', ], 'output' => [ 'shape' => 'ListRecommendersResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListSchemas' => [ 'name' => 'ListSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemasRequest', ], 'output' => [ 'shape' => 'ListSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListSolutionVersions' => [ 'name' => 'ListSolutionVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSolutionVersionsRequest', ], 'output' => [ 'shape' => 'ListSolutionVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListSolutions' => [ 'name' => 'ListSolutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSolutionsRequest', ], 'output' => [ 'shape' => 'ListSolutionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'StartRecommender' => [ 'name' => 'StartRecommender', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartRecommenderRequest', ], 'output' => [ 'shape' => 'StartRecommenderResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'StopRecommender' => [ 'name' => 'StopRecommender', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopRecommenderRequest', ], 'output' => [ 'shape' => 'StopRecommenderResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'StopSolutionVersionCreation' => [ 'name' => 'StopSolutionVersionCreation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopSolutionVersionCreationRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagKeysException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'UpdateCampaign' => [ 'name' => 'UpdateCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCampaignRequest', ], 'output' => [ 'shape' => 'UpdateCampaignResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'UpdateDataset' => [ 'name' => 'UpdateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDatasetRequest', ], 'output' => [ 'shape' => 'UpdateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'UpdateMetricAttribution' => [ 'name' => 'UpdateMetricAttribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMetricAttributionRequest', ], 'output' => [ 'shape' => 'UpdateMetricAttributionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'UpdateRecommender' => [ 'name' => 'UpdateRecommender', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRecommenderRequest', ], 'output' => [ 'shape' => 'UpdateRecommenderResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccountId' => [ 'type' => 'string', 'max' => 256, ], 'Algorithm' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'algorithmArn' => [ 'shape' => 'Arn', ], 'algorithmImage' => [ 'shape' => 'AlgorithmImage', ], 'defaultHyperParameters' => [ 'shape' => 'HyperParameters', ], 'defaultHyperParameterRanges' => [ 'shape' => 'DefaultHyperParameterRanges', ], 'defaultResourceConfig' => [ 'shape' => 'ResourceConfig', ], 'trainingInputMode' => [ 'shape' => 'TrainingInputMode', ], 'roleArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'AlgorithmImage' => [ 'type' => 'structure', 'required' => [ 'dockerURI', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'dockerURI' => [ 'shape' => 'DockerURI', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:([a-z\\d-]+):personalize:.*:.*:.+', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 100, ], 'AutoMLConfig' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'MetricName', ], 'recipeList' => [ 'shape' => 'ArnList', ], ], ], 'AutoMLResult' => [ 'type' => 'structure', 'members' => [ 'bestRecipeArn' => [ 'shape' => 'Arn', ], ], ], 'AutoTrainingConfig' => [ 'type' => 'structure', 'members' => [ 'schedulingExpression' => [ 'shape' => 'SchedulingExpression', ], ], ], 'AvroSchema' => [ 'type' => 'string', 'max' => 20000, ], 'BatchInferenceJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], 'filterArn' => [ 'shape' => 'Arn', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'numResults' => [ 'shape' => 'NumBatchResults', ], 'jobInput' => [ 'shape' => 'BatchInferenceJobInput', ], 'jobOutput' => [ 'shape' => 'BatchInferenceJobOutput', ], 'batchInferenceJobConfig' => [ 'shape' => 'BatchInferenceJobConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'batchInferenceJobMode' => [ 'shape' => 'BatchInferenceJobMode', ], 'themeGenerationConfig' => [ 'shape' => 'ThemeGenerationConfig', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'BatchInferenceJobConfig' => [ 'type' => 'structure', 'members' => [ 'itemExplorationConfig' => [ 'shape' => 'HyperParameters', ], ], ], 'BatchInferenceJobInput' => [ 'type' => 'structure', 'required' => [ 's3DataSource', ], 'members' => [ 's3DataSource' => [ 'shape' => 'S3DataConfig', ], ], ], 'BatchInferenceJobMode' => [ 'type' => 'string', 'enum' => [ 'BATCH_INFERENCE', 'THEME_GENERATION', ], ], 'BatchInferenceJobOutput' => [ 'type' => 'structure', 'required' => [ 's3DataDestination', ], 'members' => [ 's3DataDestination' => [ 'shape' => 'S3DataConfig', ], ], ], 'BatchInferenceJobSummary' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'batchInferenceJobMode' => [ 'shape' => 'BatchInferenceJobMode', ], ], ], 'BatchInferenceJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchInferenceJobSummary', ], 'max' => 100, ], 'BatchSegmentJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'batchSegmentJobArn' => [ 'shape' => 'Arn', ], 'filterArn' => [ 'shape' => 'Arn', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'numResults' => [ 'shape' => 'NumBatchResults', ], 'jobInput' => [ 'shape' => 'BatchSegmentJobInput', ], 'jobOutput' => [ 'shape' => 'BatchSegmentJobOutput', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'BatchSegmentJobInput' => [ 'type' => 'structure', 'required' => [ 's3DataSource', ], 'members' => [ 's3DataSource' => [ 'shape' => 'S3DataConfig', ], ], ], 'BatchSegmentJobOutput' => [ 'type' => 'structure', 'required' => [ 's3DataDestination', ], 'members' => [ 's3DataDestination' => [ 'shape' => 'S3DataConfig', ], ], ], 'BatchSegmentJobSummary' => [ 'type' => 'structure', 'members' => [ 'batchSegmentJobArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'BatchSegmentJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchSegmentJobSummary', ], 'max' => 100, ], 'Boolean' => [ 'type' => 'boolean', ], 'Campaign' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'campaignArn' => [ 'shape' => 'Arn', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'latestCampaignUpdate' => [ 'shape' => 'CampaignUpdateSummary', ], ], ], 'CampaignConfig' => [ 'type' => 'structure', 'members' => [ 'itemExplorationConfig' => [ 'shape' => 'HyperParameters', ], 'enableMetadataWithRecommendations' => [ 'shape' => 'Boolean', ], 'syncWithLatestSolutionVersion' => [ 'shape' => 'Boolean', ], ], ], 'CampaignSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'campaignArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'CampaignUpdateSummary' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'Campaigns' => [ 'type' => 'list', 'member' => [ 'shape' => 'CampaignSummary', ], 'max' => 100, ], 'CategoricalHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'values' => [ 'shape' => 'CategoricalValues', ], ], ], 'CategoricalHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoricalHyperParameterRange', ], 'max' => 100, ], 'CategoricalValue' => [ 'type' => 'string', 'max' => 1000, ], 'CategoricalValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoricalValue', ], 'max' => 100, ], 'ColumnName' => [ 'type' => 'string', 'max' => 150, ], 'ColumnNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], 'max' => 50, ], 'ContinuousHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'ContinuousMinValue', ], 'maxValue' => [ 'shape' => 'ContinuousMaxValue', ], ], ], 'ContinuousHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContinuousHyperParameterRange', ], 'max' => 100, ], 'ContinuousMaxValue' => [ 'type' => 'double', 'min' => -1000000, ], 'ContinuousMinValue' => [ 'type' => 'double', 'min' => -1000000, ], 'CreateBatchInferenceJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'solutionVersionArn', 'jobInput', 'jobOutput', 'roleArn', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'filterArn' => [ 'shape' => 'Arn', ], 'numResults' => [ 'shape' => 'NumBatchResults', ], 'jobInput' => [ 'shape' => 'BatchInferenceJobInput', ], 'jobOutput' => [ 'shape' => 'BatchInferenceJobOutput', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'batchInferenceJobConfig' => [ 'shape' => 'BatchInferenceJobConfig', ], 'tags' => [ 'shape' => 'Tags', ], 'batchInferenceJobMode' => [ 'shape' => 'BatchInferenceJobMode', ], 'themeGenerationConfig' => [ 'shape' => 'ThemeGenerationConfig', ], ], ], 'CreateBatchInferenceJobResponse' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateBatchSegmentJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'solutionVersionArn', 'jobInput', 'jobOutput', 'roleArn', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'filterArn' => [ 'shape' => 'Arn', ], 'numResults' => [ 'shape' => 'NumBatchResults', ], 'jobInput' => [ 'shape' => 'BatchSegmentJobInput', ], 'jobOutput' => [ 'shape' => 'BatchSegmentJobOutput', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateBatchSegmentJobResponse' => [ 'type' => 'structure', 'members' => [ 'batchSegmentJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'solutionVersionArn', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDataDeletionJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'datasetGroupArn', 'dataSource', 'roleArn', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDataDeletionJobResponse' => [ 'type' => 'structure', 'members' => [ 'dataDeletionJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'datasetArn', 'roleArn', 'jobOutput', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'ingestionMode' => [ 'shape' => 'IngestionMode', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'jobOutput' => [ 'shape' => 'DatasetExportJobOutput', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDatasetExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'domain' => [ 'shape' => 'Domain', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDatasetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'CreateDatasetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'datasetArn', 'dataSource', 'roleArn', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'Tags', ], 'importMode' => [ 'shape' => 'ImportMode', ], 'publishAttributionMetricsToS3' => [ 'shape' => 'Boolean', ], ], ], 'CreateDatasetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetImportJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'schemaArn', 'datasetGroupArn', 'datasetType', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'datasetType' => [ 'shape' => 'DatasetType', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], ], ], 'CreateEventTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateEventTrackerResponse' => [ 'type' => 'structure', 'members' => [ 'eventTrackerArn' => [ 'shape' => 'Arn', ], 'trackingId' => [ 'shape' => 'TrackingId', ], ], ], 'CreateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', 'filterExpression', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'filterExpression' => [ 'shape' => 'FilterExpression', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateFilterResponse' => [ 'type' => 'structure', 'members' => [ 'filterArn' => [ 'shape' => 'Arn', ], ], ], 'CreateMetricAttributionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', 'metrics', 'metricsOutputConfig', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'metrics' => [ 'shape' => 'MetricAttributes', ], 'metricsOutputConfig' => [ 'shape' => 'MetricAttributionOutput', ], ], ], 'CreateMetricAttributionResponse' => [ 'type' => 'structure', 'members' => [ 'metricAttributionArn' => [ 'shape' => 'Arn', ], ], ], 'CreateRecommenderRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', 'recipeArn', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'recommenderConfig' => [ 'shape' => 'RecommenderConfig', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateRecommenderResponse' => [ 'type' => 'structure', 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'schema', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schema' => [ 'shape' => 'AvroSchema', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'CreateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'schemaArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSolutionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'performHPO' => [ 'shape' => 'Boolean', ], 'performAutoML' => [ 'shape' => 'PerformAutoML', ], 'performAutoTraining' => [ 'shape' => 'PerformAutoTraining', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'eventType' => [ 'shape' => 'EventType', ], 'solutionConfig' => [ 'shape' => 'SolutionConfig', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSolutionResponse' => [ 'type' => 'structure', 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSolutionVersionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionArn', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionArn' => [ 'shape' => 'Arn', ], 'trainingMode' => [ 'shape' => 'TrainingMode', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSolutionVersionResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'DataDeletionJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'dataDeletionJobArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'Status', ], 'numDeleted' => [ 'shape' => 'Integer', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DataDeletionJobSummary' => [ 'type' => 'structure', 'members' => [ 'dataDeletionJobArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DataDeletionJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataDeletionJobSummary', ], 'max' => 100, ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'dataLocation' => [ 'shape' => 'S3Location', ], ], ], 'Dataset' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'datasetType' => [ 'shape' => 'DatasetType', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'latestDatasetUpdate' => [ 'shape' => 'DatasetUpdateSummary', ], 'trackingId' => [ 'shape' => 'TrackingId', ], ], ], 'DatasetExportJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetExportJobArn' => [ 'shape' => 'Arn', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'ingestionMode' => [ 'shape' => 'IngestionMode', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'jobOutput' => [ 'shape' => 'DatasetExportJobOutput', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetExportJobOutput' => [ 'type' => 'structure', 'required' => [ 's3DataDestination', ], 'members' => [ 's3DataDestination' => [ 'shape' => 'S3DataConfig', ], ], ], 'DatasetExportJobSummary' => [ 'type' => 'structure', 'members' => [ 'datasetExportJobArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetExportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetExportJobSummary', ], 'max' => 100, ], 'DatasetGroup' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'DatasetGroupSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'DatasetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetGroupSummary', ], 'max' => 100, ], 'DatasetImportJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetImportJobArn' => [ 'shape' => 'Arn', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'importMode' => [ 'shape' => 'ImportMode', ], 'publishAttributionMetricsToS3' => [ 'shape' => 'Boolean', ], ], ], 'DatasetImportJobSummary' => [ 'type' => 'structure', 'members' => [ 'datasetImportJobArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'importMode' => [ 'shape' => 'ImportMode', ], ], ], 'DatasetImportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetImportJobSummary', ], 'max' => 100, ], 'DatasetSchema' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'schema' => [ 'shape' => 'AvroSchema', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'DatasetSchemaSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'DatasetSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'datasetType' => [ 'shape' => 'DatasetType', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'DatasetType' => [ 'type' => 'string', 'max' => 256, ], 'DatasetUpdateSummary' => [ 'type' => 'structure', 'members' => [ 'schemaArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'Datasets' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetSummary', ], 'max' => 100, ], 'Date' => [ 'type' => 'timestamp', ], 'DefaultCategoricalHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'values' => [ 'shape' => 'CategoricalValues', ], 'isTunable' => [ 'shape' => 'Tunable', ], ], ], 'DefaultCategoricalHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultCategoricalHyperParameterRange', ], 'max' => 100, ], 'DefaultContinuousHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'ContinuousMinValue', ], 'maxValue' => [ 'shape' => 'ContinuousMaxValue', ], 'isTunable' => [ 'shape' => 'Tunable', ], ], ], 'DefaultContinuousHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultContinuousHyperParameterRange', ], 'max' => 100, ], 'DefaultHyperParameterRanges' => [ 'type' => 'structure', 'members' => [ 'integerHyperParameterRanges' => [ 'shape' => 'DefaultIntegerHyperParameterRanges', ], 'continuousHyperParameterRanges' => [ 'shape' => 'DefaultContinuousHyperParameterRanges', ], 'categoricalHyperParameterRanges' => [ 'shape' => 'DefaultCategoricalHyperParameterRanges', ], ], ], 'DefaultIntegerHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'IntegerMinValue', ], 'maxValue' => [ 'shape' => 'IntegerMaxValue', ], 'isTunable' => [ 'shape' => 'Tunable', ], ], ], 'DefaultIntegerHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultIntegerHyperParameterRange', ], 'max' => 100, ], 'DeleteCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'campaignArn', ], 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'datasetGroupArn', ], 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetArn', ], 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteEventTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'eventTrackerArn', ], 'members' => [ 'eventTrackerArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteFilterRequest' => [ 'type' => 'structure', 'required' => [ 'filterArn', ], 'members' => [ 'filterArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteMetricAttributionRequest' => [ 'type' => 'structure', 'required' => [ 'metricAttributionArn', ], 'members' => [ 'metricAttributionArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteRecommenderRequest' => [ 'type' => 'structure', 'required' => [ 'recommenderArn', ], 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'schemaArn', ], 'members' => [ 'schemaArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteSolutionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionArn', ], 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAlgorithmRequest' => [ 'type' => 'structure', 'required' => [ 'algorithmArn', ], 'members' => [ 'algorithmArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAlgorithmResponse' => [ 'type' => 'structure', 'members' => [ 'algorithm' => [ 'shape' => 'Algorithm', ], ], ], 'DescribeBatchInferenceJobRequest' => [ 'type' => 'structure', 'required' => [ 'batchInferenceJobArn', ], 'members' => [ 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeBatchInferenceJobResponse' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJob' => [ 'shape' => 'BatchInferenceJob', ], ], ], 'DescribeBatchSegmentJobRequest' => [ 'type' => 'structure', 'required' => [ 'batchSegmentJobArn', ], 'members' => [ 'batchSegmentJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeBatchSegmentJobResponse' => [ 'type' => 'structure', 'members' => [ 'batchSegmentJob' => [ 'shape' => 'BatchSegmentJob', ], ], ], 'DescribeCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'campaignArn', ], 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'campaign' => [ 'shape' => 'Campaign', ], ], ], 'DescribeDataDeletionJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataDeletionJobArn', ], 'members' => [ 'dataDeletionJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDataDeletionJobResponse' => [ 'type' => 'structure', 'members' => [ 'dataDeletionJob' => [ 'shape' => 'DataDeletionJob', ], ], ], 'DescribeDatasetExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'datasetExportJobArn', ], 'members' => [ 'datasetExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetExportJob' => [ 'shape' => 'DatasetExportJob', ], ], ], 'DescribeDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'datasetGroupArn', ], 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'datasetGroup' => [ 'shape' => 'DatasetGroup', ], ], ], 'DescribeDatasetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'datasetImportJobArn', ], 'members' => [ 'datasetImportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetImportJob' => [ 'shape' => 'DatasetImportJob', ], ], ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetArn', ], 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'dataset' => [ 'shape' => 'Dataset', ], ], ], 'DescribeEventTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'eventTrackerArn', ], 'members' => [ 'eventTrackerArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeEventTrackerResponse' => [ 'type' => 'structure', 'members' => [ 'eventTracker' => [ 'shape' => 'EventTracker', ], ], ], 'DescribeFeatureTransformationRequest' => [ 'type' => 'structure', 'required' => [ 'featureTransformationArn', ], 'members' => [ 'featureTransformationArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeFeatureTransformationResponse' => [ 'type' => 'structure', 'members' => [ 'featureTransformation' => [ 'shape' => 'FeatureTransformation', ], ], ], 'DescribeFilterRequest' => [ 'type' => 'structure', 'required' => [ 'filterArn', ], 'members' => [ 'filterArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeFilterResponse' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'Filter', ], ], ], 'DescribeMetricAttributionRequest' => [ 'type' => 'structure', 'required' => [ 'metricAttributionArn', ], 'members' => [ 'metricAttributionArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeMetricAttributionResponse' => [ 'type' => 'structure', 'members' => [ 'metricAttribution' => [ 'shape' => 'MetricAttribution', ], ], ], 'DescribeRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'recipeArn', ], 'members' => [ 'recipeArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'recipe' => [ 'shape' => 'Recipe', ], ], ], 'DescribeRecommenderRequest' => [ 'type' => 'structure', 'required' => [ 'recommenderArn', ], 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeRecommenderResponse' => [ 'type' => 'structure', 'members' => [ 'recommender' => [ 'shape' => 'Recommender', ], ], ], 'DescribeSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'schemaArn', ], 'members' => [ 'schemaArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'schema' => [ 'shape' => 'DatasetSchema', ], ], ], 'DescribeSolutionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionArn', ], 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeSolutionResponse' => [ 'type' => 'structure', 'members' => [ 'solution' => [ 'shape' => 'Solution', ], ], ], 'DescribeSolutionVersionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionVersionArn', ], 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeSolutionVersionResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersion' => [ 'shape' => 'SolutionVersion', ], ], ], 'Description' => [ 'type' => 'string', ], 'DockerURI' => [ 'type' => 'string', 'max' => 256, ], 'Domain' => [ 'type' => 'string', 'enum' => [ 'ECOMMERCE', 'VIDEO_ON_DEMAND', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventTracker' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'eventTrackerArn' => [ 'shape' => 'Arn', ], 'accountId' => [ 'shape' => 'AccountId', ], 'trackingId' => [ 'shape' => 'TrackingId', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'EventTrackerSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'eventTrackerArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'EventTrackers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventTrackerSummary', ], 'max' => 100, ], 'EventType' => [ 'type' => 'string', 'max' => 256, ], 'EventValueThreshold' => [ 'type' => 'string', 'max' => 256, ], 'ExcludedDatasetColumns' => [ 'type' => 'map', 'key' => [ 'shape' => 'DatasetType', ], 'value' => [ 'shape' => 'ColumnNamesList', ], 'max' => 3, ], 'FailureReason' => [ 'type' => 'string', ], 'FeatureTransformation' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'featureTransformationArn' => [ 'shape' => 'Arn', ], 'defaultParameters' => [ 'shape' => 'FeaturizationParameters', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'status' => [ 'shape' => 'Status', ], ], ], 'FeatureTransformationParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'FeaturizationParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'FieldsForThemeGeneration' => [ 'type' => 'structure', 'required' => [ 'itemName', ], 'members' => [ 'itemName' => [ 'shape' => 'ColumnName', ], ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'filterArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'filterExpression' => [ 'shape' => 'FilterExpression', ], 'status' => [ 'shape' => 'Status', ], ], ], 'FilterExpression' => [ 'type' => 'string', 'max' => 2500, 'min' => 1, 'sensitive' => true, ], 'FilterSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'filterArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'status' => [ 'shape' => 'Status', ], ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterSummary', ], 'max' => 100, ], 'GetSolutionMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'solutionVersionArn', ], 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'GetSolutionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'metrics' => [ 'shape' => 'Metrics', ], ], ], 'HPOConfig' => [ 'type' => 'structure', 'members' => [ 'hpoObjective' => [ 'shape' => 'HPOObjective', ], 'hpoResourceConfig' => [ 'shape' => 'HPOResourceConfig', ], 'algorithmHyperParameterRanges' => [ 'shape' => 'HyperParameterRanges', ], ], ], 'HPOObjective' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'HPOObjectiveType', ], 'metricName' => [ 'shape' => 'MetricName', ], 'metricRegex' => [ 'shape' => 'MetricRegex', ], ], ], 'HPOObjectiveType' => [ 'type' => 'string', 'max' => 256, ], 'HPOResource' => [ 'type' => 'string', 'max' => 256, ], 'HPOResourceConfig' => [ 'type' => 'structure', 'members' => [ 'maxNumberOfTrainingJobs' => [ 'shape' => 'HPOResource', ], 'maxParallelTrainingJobs' => [ 'shape' => 'HPOResource', ], ], ], 'HyperParameterRanges' => [ 'type' => 'structure', 'members' => [ 'integerHyperParameterRanges' => [ 'shape' => 'IntegerHyperParameterRanges', ], 'continuousHyperParameterRanges' => [ 'shape' => 'ContinuousHyperParameterRanges', ], 'categoricalHyperParameterRanges' => [ 'shape' => 'CategoricalHyperParameterRanges', ], ], ], 'HyperParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'ImportMode' => [ 'type' => 'string', 'enum' => [ 'FULL', 'INCREMENTAL', ], ], 'IngestionMode' => [ 'type' => 'string', 'enum' => [ 'BULK', 'PUT', 'ALL', ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'IntegerMinValue', ], 'maxValue' => [ 'shape' => 'IntegerMaxValue', ], ], ], 'IntegerHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegerHyperParameterRange', ], 'max' => 100, ], 'IntegerMaxValue' => [ 'type' => 'integer', 'max' => 1000000, ], 'IntegerMinValue' => [ 'type' => 'integer', 'min' => -1000000, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ItemAttribute' => [ 'type' => 'string', 'max' => 150, 'min' => 1, ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => 'arn:aws.*:kms:.*:[0-9]{12}:key/.*', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListBatchInferenceJobsRequest' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBatchInferenceJobsResponse' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJobs' => [ 'shape' => 'BatchInferenceJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBatchSegmentJobsRequest' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBatchSegmentJobsResponse' => [ 'type' => 'structure', 'members' => [ 'batchSegmentJobs' => [ 'shape' => 'BatchSegmentJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCampaignsRequest' => [ 'type' => 'structure', 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListCampaignsResponse' => [ 'type' => 'structure', 'members' => [ 'campaigns' => [ 'shape' => 'Campaigns', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataDeletionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDataDeletionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'dataDeletionJobs' => [ 'shape' => 'DataDeletionJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'datasetExportJobs' => [ 'shape' => 'DatasetExportJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'datasetGroups' => [ 'shape' => 'DatasetGroups', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'datasetImportJobs' => [ 'shape' => 'DatasetImportJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'members' => [ 'datasets' => [ 'shape' => 'Datasets', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventTrackersRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListEventTrackersResponse' => [ 'type' => 'structure', 'members' => [ 'eventTrackers' => [ 'shape' => 'EventTrackers', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFiltersRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListFiltersResponse' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'Filters', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMetricAttributionMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'metricAttributionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMetricAttributionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'metrics' => [ 'shape' => 'MetricAttributes', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMetricAttributionsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMetricAttributionsResponse' => [ 'type' => 'structure', 'members' => [ 'metricAttributions' => [ 'shape' => 'MetricAttributions', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRecipesRequest' => [ 'type' => 'structure', 'members' => [ 'recipeProvider' => [ 'shape' => 'RecipeProvider', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'ListRecipesResponse' => [ 'type' => 'structure', 'members' => [ 'recipes' => [ 'shape' => 'Recipes', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRecommendersRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRecommendersResponse' => [ 'type' => 'structure', 'members' => [ 'recommenders' => [ 'shape' => 'Recommenders', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSchemasRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'schemas' => [ 'shape' => 'Schemas', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSolutionVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListSolutionVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersions' => [ 'shape' => 'SolutionVersions', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSolutionsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListSolutionsResponse' => [ 'type' => 'structure', 'members' => [ 'solutions' => [ 'shape' => 'Solutions', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MetricAttribute' => [ 'type' => 'structure', 'required' => [ 'eventType', 'metricName', 'expression', ], 'members' => [ 'eventType' => [ 'shape' => 'EventType', ], 'metricName' => [ 'shape' => 'MetricName', ], 'expression' => [ 'shape' => 'MetricExpression', ], ], ], 'MetricAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricAttribute', ], 'max' => 10, ], 'MetricAttributesNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricName', ], 'max' => 10, ], 'MetricAttribution' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'metricAttributionArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'metricsOutputConfig' => [ 'shape' => 'MetricAttributionOutput', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'MetricAttributionOutput' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 's3DataDestination' => [ 'shape' => 'S3DataConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'MetricAttributionSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'metricAttributionArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'MetricAttributions' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricAttributionSummary', ], 'max' => 100, ], 'MetricExpression' => [ 'type' => 'string', 'max' => 256, ], 'MetricName' => [ 'type' => 'string', 'max' => 256, ], 'MetricRegex' => [ 'type' => 'string', 'max' => 256, ], 'MetricValue' => [ 'type' => 'double', ], 'Metrics' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetricName', ], 'value' => [ 'shape' => 'MetricValue', ], 'max' => 100, ], 'Name' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'NextToken' => [ 'type' => 'string', 'max' => 1500, 'pattern' => '\\p{ASCII}{0,1500}', ], 'NumBatchResults' => [ 'type' => 'integer', ], 'ObjectiveSensitivity' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'OFF', ], ], 'OptimizationObjective' => [ 'type' => 'structure', 'members' => [ 'itemAttribute' => [ 'shape' => 'ItemAttribute', ], 'objectiveSensitivity' => [ 'shape' => 'ObjectiveSensitivity', ], ], ], 'ParameterName' => [ 'type' => 'string', 'max' => 256, ], 'ParameterValue' => [ 'type' => 'string', 'max' => 1000, ], 'PerformAutoML' => [ 'type' => 'boolean', ], 'PerformAutoTraining' => [ 'type' => 'boolean', 'box' => true, ], 'PerformHPO' => [ 'type' => 'boolean', ], 'Recipe' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'algorithmArn' => [ 'shape' => 'Arn', ], 'featureTransformationArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'description' => [ 'shape' => 'Description', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'recipeType' => [ 'shape' => 'RecipeType', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'RecipeProvider' => [ 'type' => 'string', 'enum' => [ 'SERVICE', ], ], 'RecipeSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'domain' => [ 'shape' => 'Domain', ], ], ], 'RecipeType' => [ 'type' => 'string', 'max' => 256, ], 'Recipes' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecipeSummary', ], 'max' => 100, ], 'Recommender' => [ 'type' => 'structure', 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'recommenderConfig' => [ 'shape' => 'RecommenderConfig', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'latestRecommenderUpdate' => [ 'shape' => 'RecommenderUpdateSummary', ], 'modelMetrics' => [ 'shape' => 'Metrics', ], ], ], 'RecommenderConfig' => [ 'type' => 'structure', 'members' => [ 'itemExplorationConfig' => [ 'shape' => 'HyperParameters', ], 'minRecommendationRequestsPerSecond' => [ 'shape' => 'TransactionsPerSecond', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'enableMetadataWithRecommendations' => [ 'shape' => 'Boolean', ], ], ], 'RecommenderSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'recommenderArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'recommenderConfig' => [ 'shape' => 'RecommenderConfig', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'RecommenderUpdateSummary' => [ 'type' => 'structure', 'members' => [ 'recommenderConfig' => [ 'shape' => 'RecommenderConfig', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'Recommenders' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommenderSummary', ], 'max' => 100, ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceConfig' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:([a-z\\d-]+):iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+', ], 'S3DataConfig' => [ 'type' => 'structure', 'required' => [ 'path', ], 'members' => [ 'path' => [ 'shape' => 'S3Location', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'S3Location' => [ 'type' => 'string', 'max' => 256, 'pattern' => '(s3|http|https)://.+', ], 'SchedulingExpression' => [ 'type' => 'string', 'max' => 16, 'min' => 1, 'pattern' => 'rate\\(\\d+ days?\\)', ], 'Schemas' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetSchemaSummary', ], 'max' => 100, ], 'Solution' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionArn' => [ 'shape' => 'Arn', ], 'performHPO' => [ 'shape' => 'PerformHPO', ], 'performAutoML' => [ 'shape' => 'PerformAutoML', ], 'performAutoTraining' => [ 'shape' => 'PerformAutoTraining', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'eventType' => [ 'shape' => 'EventType', ], 'solutionConfig' => [ 'shape' => 'SolutionConfig', ], 'autoMLResult' => [ 'shape' => 'AutoMLResult', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'latestSolutionVersion' => [ 'shape' => 'SolutionVersionSummary', ], ], ], 'SolutionConfig' => [ 'type' => 'structure', 'members' => [ 'eventValueThreshold' => [ 'shape' => 'EventValueThreshold', ], 'hpoConfig' => [ 'shape' => 'HPOConfig', ], 'algorithmHyperParameters' => [ 'shape' => 'HyperParameters', ], 'featureTransformationParameters' => [ 'shape' => 'FeatureTransformationParameters', ], 'autoMLConfig' => [ 'shape' => 'AutoMLConfig', ], 'optimizationObjective' => [ 'shape' => 'OptimizationObjective', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'autoTrainingConfig' => [ 'shape' => 'AutoTrainingConfig', ], ], ], 'SolutionSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'recipeArn' => [ 'shape' => 'Arn', ], ], ], 'SolutionVersion' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'solutionArn' => [ 'shape' => 'Arn', ], 'performHPO' => [ 'shape' => 'PerformHPO', ], 'performAutoML' => [ 'shape' => 'PerformAutoML', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'eventType' => [ 'shape' => 'EventType', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'solutionConfig' => [ 'shape' => 'SolutionConfig', ], 'trainingHours' => [ 'shape' => 'TrainingHours', ], 'trainingMode' => [ 'shape' => 'TrainingMode', ], 'tunedHPOParams' => [ 'shape' => 'TunedHPOParams', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'trainingType' => [ 'shape' => 'TrainingType', ], ], ], 'SolutionVersionSummary' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'trainingMode' => [ 'shape' => 'TrainingMode', ], 'trainingType' => [ 'shape' => 'TrainingType', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'SolutionVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionVersionSummary', ], 'max' => 100, ], 'Solutions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionSummary', ], 'max' => 100, ], 'StartRecommenderRequest' => [ 'type' => 'structure', 'required' => [ 'recommenderArn', ], 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], 'StartRecommenderResponse' => [ 'type' => 'structure', 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], 'Status' => [ 'type' => 'string', 'max' => 256, ], 'StopRecommenderRequest' => [ 'type' => 'structure', 'required' => [ 'recommenderArn', ], 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], 'StopRecommenderResponse' => [ 'type' => 'structure', 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], 'StopSolutionVersionCreationRequest' => [ 'type' => 'structure', 'required' => [ 'solutionVersionArn', ], 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'tagKey', 'tagValue', ], 'members' => [ 'tagKey' => [ 'shape' => 'TagKey', ], 'tagValue' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'ThemeGenerationConfig' => [ 'type' => 'structure', 'required' => [ 'fieldsForThemeGeneration', ], 'members' => [ 'fieldsForThemeGeneration' => [ 'shape' => 'FieldsForThemeGeneration', ], ], ], 'TooManyTagKeysException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TrackingId' => [ 'type' => 'string', 'max' => 256, ], 'TrainingDataConfig' => [ 'type' => 'structure', 'members' => [ 'excludedDatasetColumns' => [ 'shape' => 'ExcludedDatasetColumns', ], ], ], 'TrainingHours' => [ 'type' => 'double', 'min' => 0, ], 'TrainingInputMode' => [ 'type' => 'string', 'max' => 256, ], 'TrainingMode' => [ 'type' => 'string', 'enum' => [ 'FULL', 'UPDATE', 'AUTOTRAIN', ], ], 'TrainingType' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'MANUAL', ], ], 'TransactionsPerSecond' => [ 'type' => 'integer', 'min' => 1, ], 'Tunable' => [ 'type' => 'boolean', ], 'TunedHPOParams' => [ 'type' => 'structure', 'members' => [ 'algorithmHyperParameters' => [ 'shape' => 'HyperParameters', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'campaignArn', ], 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], ], ], 'UpdateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetArn', 'schemaArn', ], 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], 'schemaArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateMetricAttributionRequest' => [ 'type' => 'structure', 'members' => [ 'addMetrics' => [ 'shape' => 'MetricAttributes', ], 'removeMetrics' => [ 'shape' => 'MetricAttributesNamesList', ], 'metricsOutputConfig' => [ 'shape' => 'MetricAttributionOutput', ], 'metricAttributionArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateMetricAttributionResponse' => [ 'type' => 'structure', 'members' => [ 'metricAttributionArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateRecommenderRequest' => [ 'type' => 'structure', 'required' => [ 'recommenderArn', 'recommenderConfig', ], 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], 'recommenderConfig' => [ 'shape' => 'RecommenderConfig', ], ], ], 'UpdateRecommenderResponse' => [ 'type' => 'structure', 'members' => [ 'recommenderArn' => [ 'shape' => 'Arn', ], ], ], ],];
