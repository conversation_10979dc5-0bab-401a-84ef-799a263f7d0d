<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xml:lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
<title>Spell Check...</title>
<link id="theme" type="text/css" rel="stylesheet" href="themes/classic/dialog-window.css" />
<script language="javascript1.4" type="text/javascript" src="core/dialog.js"></script>
<body  id ="LivespellDialogBody" onload="spellWin.init()" onunload="spellWin.actions.registerclose();" spellcheck="false" >
<div id="MainForm"  >
  <form id="SpellForm" name="SpellForm" method="post" action="">
    <textarea rows="8" cols="30" id="fldTextInput" name="fldTextInput"></textarea>
    <div id="TextShow" onclick="spellWin.ui.showEdit(true);" onmouseup="spellWin.ui.showEdit(true);" ></div>
    <select size="8" id="fldSuggestions" name="fldSuggestions" ondblclick="spellWin.actions.changeCurrent()">
    </select>
    <label accesskey="Y" for="fldTextInput" id="fldTextInputLab"></label>
    <label accesskey="N" for="fldSuggestions" id="SuggestionsLab">LABEL_SUGGESTIONS</label>
    <div id="lMeaning"><a href="#" id="lMeaningLink" onkeypress="return spellWin.ui.lookupMeaning();"  onclick="return spellWin.ui.lookupMeaning();" >LOOKUP_MEANING</a></div>
    <label accesskey="D" for="fldLanguage" id="fldLanguageLab">LABEL_LANGAUGE</label>
    <input type="button" value="BTN_CHANGE"  accesskey="C" id="btnChange" name="btnChange" onclick="spellWin.actions.changeCurrent()" />
    <input type="button" value="BTN_CLOSE" onclick="spellWin.actions.cancel();" accesskey="X" id="btnCancel" name="btnCancel" />
    <input type="button" value="BTN_IGNORE_ONCE" accesskey="I" id="btnIgnore" name="btnIgnore" onclick="spellWin.actions.ignoreOnce();" />
    <input type="button" value="BTN_IGNORE_ALL" accesskey="G" id="btnIgnoreAll" name="btnIgnoreAll" onclick="spellWin.actions.ignoreAll();"/>
    <input type="button" value="BTN_ADD_TO_DICT" accesskey="A" id="btnAddToDict" name="btnAddToDict" onclick="spellWin.actions.addPersonal();" />
    <input type="button" value="BTN_CHANGE_ALL" accesskey="L" id="btnChangeAll" name="btnChangeAll" onclick="spellWin.actions.changeAll();" />
    <input type="button" value="BTN_AUTO_CORECT" accesskey="R" id="btnAutoCorrect" name="btnAutoCorrect" onclick="spellWin.actions.addAutoCorrect();" />
    <input type="button" value="BTN_OPTIONS" accesskey="O" id="btnShowOptions" name="btnShowOptions" onclick="spellWin.optionsMenu.show(true);" />
    <input type="button" value="BTN_CLEAR_EDIT" accesskey="U" id="btnUndoManualEdit" name="btnUndoManualEdit"  onclick="spellWin.ui.showEdit(false);" />
    <input type="button" value="BTN_UNDO" accesskey="U" id="btnUndo" name="btnUndo" disabled="disabled" onclick="spellWin.undo.get();" />
    <select  id="fldLanguage" name="fldLanguage" onchange="spellWin.actions.changeLanguage();">
    </select>
      <label for="optSentence" id="CheckGrammarLab" accesskey="G">
    <input type="checkbox" value="True" name="optSentence" id="optSentence" onchange="spellWin.ui.setGrammar()"/>
    <span id="optSentenceText">OPT_SENTENCE_AWARE</span>
   </label>
  </form>
</div>
<form name="optForm" action="" id="optForm" method="post" >
  <label id='tOpt' >Options:</label>
  <label for="optCaseSensitive" id="optCaseSensitiveLab" accesskey="C" >
    <input type="checkbox" value="True"   name="optCaseSensitive" id="optCaseSensitive"   >
    <span id="optCaseSensitiveText"> OPT_CASE_SENSITIVE</span></label>
  <label for="optAllCaps" id="optAllCapsLab" accesskey="U" >
    <input type="checkbox" value="True"   name="optAllCaps" id="optAllCaps"    >
    <span id="optAllCapsbText"> OPT_IGNORE_CAPS</span></label>
  <label for="optNumeric" id="optNumericLab" accesskey="B">
    <input type="checkbox" value="True" name="optNumeric" id="optNumeric"   >
    <span id="optNumericText">OPT_IGNORE_NUMERIC</span></label>
 
    
  <label for="btnResetDict" id="btnResetDictLab"><span id="btnResetDictLabText">OPT_PERSONAL_DICT</span> <span id="tDictCount">6 OPT_ENTRIES</span>
    <input name="btnResetDict" type="button" id="btnResetDict"  value="BTN_RESET"   accesskey="D" onclick="spellWin.actions.deletePersonal();  spellWin.optionsMenu.show(true);" />
  </label>
  <label for="btnResetAutoCorrect" id="btnResetAutoCorrectLab"><span id="btnResetAutoCorrectLabText">OPT_PERSONAL_AUTO_CURRECT</span> <span id="tAutoCorrectCount">2 OPT_ENTRIES</span>
    <input name="btnResetAutoCorrect" type="button" id="btnResetAutoCorrect"  value="BTN_RESET" accesskey="A" onclick="spellWin.actions.deleteAutoCorrect();  spellWin.optionsMenu.show(true);" />
  </label>
  <input name="btnOptionsOK" type="button"  id="btnOptionsOK" value="BTN_OK" accesskey="O" onclick="spellWin.optionsMenu.set()" />
  <input name="btnOptionsCancel" type="button" id="btnOptionsCancel" value="BTN_CANCEL" accesskey="X" onclick="spellWin.optionsMenu.show(false); " >
</form>
<form name="multiLangForm" action="" id="multiLangForm" method="" >
  <label accesskey="M"  for="fldLanguageMultiple" >
  <div id="fldLanguageMultipleLabText">LANGUAGE_MULTIPLE_INSTRUCTIONS</div>
  <select multiple="multiple" name="fldLanguageMultiple" id="fldLanguageMultiple" size="11" >
  </select>
  </label>
  <input name="btnLangOK" type="button"  id="btnLangOK" value="BTN_OK" accesskey="O" onclick="spellWin.actions.changeMultiLanguage();"/>
  <input name="btnLangCancel" type="button" id="btnLangCancel" value="BTN_CANCEL" accesskey="X"  onclick="spellWin.optionsMenu.showMultiLang(false)"  >
</form>
<form name="doneForm" action="" id="doneForm" method="" >
  <div id="tSum"><strong>DONESCREEN_MESSAGE</strong></div>
  <div id="sdivider">
    <hr />
  </div>
  <div id="tWrd">DONESCREEN_WORDS</div>
  <div id="tEdi">DONESCREEN_EDITS</div>
  <div id="tDoc">DONESCREEN_FIELDS</div>
  <input name="bOK" type="button" id="btnAllDone"  accesskey="O" value="BTN_OK" onclick="spellWin.actions.done()" >
</form>
</body>
</html>
