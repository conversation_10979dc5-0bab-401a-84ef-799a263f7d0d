!function(e){"object"==typeof module&&"undefined"!=typeof module.exports?module.exports=e:e()}((function(){"use strict";(self.webpackChunkFusionCharts=self.webpackChunkFusionCharts||[]).push([[2],{606:(e,t)=>{t.__esModule=!0,t["default"]=void 0;var a={"initial.canvas.canvas":{"canvas.appearing":[{initialAttr:{opacity:0},finalAttr:{opacity:1},slot:"initial"}]}};t["default"]=a},605:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(522)),i=a(274),l=a(282),s=n(a(606)),c=n(a(607)),u=(0,l.getDep)("redraphael","plugin"),d="canvasBaseColor3D",p="canvasBgAlpha",f="clip-canvas",h="clip-canvas-init",g=",",v=i.preDefStr.ROUND,m=i.preDefStr.miterStr,b=Math.max,C=u,_="none",D="M",y={chart2D:{bgColor:"bgColor",bgAlpha:"bgAlpha",bgAngle:"bgAngle",bgRatio:"bgRatio",canvasBgColor:"canvasBgColor",canvasBaseColor:"canvasBaseColor",divLineColor:"divLineColor",legendBgColor:"legendBgColor",legendBorderColor:"legendBorderColor",toolTipbgColor:"toolTipbgColor",toolTipBorderColor:"toolTipBorderColor",baseFontColor:"baseFontColor",anchorBgColor:"anchorBgColor"},chart3D:{bgColor:"bgColor3D",bgAlpha:"bgAlpha3D",bgAngle:"bgAngle3D",bgRatio:"bgRatio3D",canvasBgColor:"canvasBgColor3D",canvasBaseColor:d,divLineColor:"divLineColor3D",divLineAlpha:"divLineAlpha3D",legendBgColor:"legendBgColor3D",legendBorderColor:"legendBorderColor3D",toolTipbgColor:"toolTipbgColor3D",toolTipBorderColor:"toolTipBorderColor3D",baseFontColor:"baseFontColor3D",anchorBgColor:"anchorBgColor3D"}},S=function(){this.hide()},k=function(){this.hide(),this._.cubeside.hide(),this._.cubetop.hide()},x=function(){this.show(),this._.cubeside.show(),this._.cubetop.show()};(0,l.addDep)({name:"canvas3dAnimation",type:"animationRule",extension:s["default"]}),(0,c["default"])(u);var P=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.getName=function(){return"canvas"},a.getType=function(){return"canvas"},a.drawCanvas=function(){this.getFromEnv("chart").isBar?this.drawCanvas3dBar():this.drawCanvas3dColumn()},a.configureAttributes=function(){e.prototype.configureAttributes.call(this),this.config.xDepth=10,this.config.yDepth=10},a.drawCanvas3dColumn=function(){var e,t,a,n,o,r,l,s,c,g,v,C,P,A,N,w,T=this,F=T.getFromEnv("chart"),M=F.getFromEnv("dataSource"),B=T.config,E=F.config,I=B.canvasLeft,L=B.canvasTop,O=B.canvasWidth,R=B.canvasHeight,G=M.chart,V=T.getFromEnv("animationManager"),z=F.getFromEnv("color-manager"),H=T.getGraphicalElement("canvasBorderElement"),W=T.getGraphicalElement("canvasElement"),Y=T.config,U=Y.clip={},j=T.getContainer("canvasGroup"),X=T.getGraphicalElement("canvasBg"),Z=T.getGraphicalElement("canvas3DBase"),J=F.getChildContainer("plotGroup"),q=F.getChildContainer("datalabelsGroup"),K=T.getGraphicalElement("canvas3dbaseline"),$=Y.canvasBgColor,Q=Y.showCanvasBG=Boolean((0,i.pluckNumber)(G.showcanvasbg,1)),ee=E.canvasBgDepth,te=E.showCanvasBase,ae=E.canvasBaseDepth,ne=Y.canvasBaseColor3D=(0,i.pluck)(G.canvasbasecolor,z.getColor(d)),oe=Y.use3DLighting=(0,i.pluckNumber)(G.use3dlighting,1),re=y.chart3D,ie=Y.canvasBorderRadius=(0,i.pluckNumber)(G.plotborderradius,0),le=Y.canvasBorderColor=(0,i.convertColor)((0,i.pluck)(G.canvasbordercolor,z.getColor(i.canvasBorderColorStr))),se=Y.canBGAlpha=(0,i.pluck)(G.canvasbgalpha,z.getColor(p)),ce=Y.canBGColor=(0,i.pluck)(G.canvasbgcolor,z.getColor(re.canvasBgColor)),ue=E.canvasBasePadding||2;$=Y.canvasBgColor=oe?{FCcolor:{color:(0,i.getDarkColor)(ce,85)+i.COMMASTRING+(0,i.getLightColor)(ce,55),alpha:se+i.COMMASTRING+se,ratio:i.BGRATIOSTRING,angle:(0,i.getAngle)(E.width-(E.marginLeft+E.marginRight),E.height-(E.marginTop+E.marginBottom),1)}}:(0,i.convertColor)(ce,se),ce=ce.split(i.COMMASTRING)[0],se=se.split(i.COMMASTRING)[0],N=Y.xDepth,w=Y.yDepth,r={x:I-0,y:L-0,width:O+0,height:R+0,r:ie,"stroke-width":0,stroke:le,"stroke-linejoin":m},e=V.setAnimation({el:H||"rect",attr:r,component:T,label:"canvas",container:j}),H||T.addGraphicalElement("canvasBorderElement",e),U[f]=[b(0,I-N),b(0,L),b(1,O+N),b(1,R+w)],U[h]=[b(0,I-N),b(0,L-w),1,b(1,R+2*w)],P=U[f].slice(0),V.setAnimation({el:J,attr:{"clip-rect":P},component:T}),V.setAnimation({el:q,attr:{"clip-rect":P},component:T}),r={x:I,y:L,width:O,height:R,r:ie,"stroke-width":0,stroke:_,fill:(0,i.toRaphaelColor)($)},t=V.setAnimation({el:W||"rect",attr:r,component:T,label:"canvas",callback:Q?i.stubFN:S,container:j}),W||T.addGraphicalElement("canvasElement",t),o=[D,I+O,L,"L",I+O+ee,L*****ee,I+O+ee,L+R-ee,I+O,L+R,"Z"],a=V.setAnimation({el:X||"path",attr:{path:o,"stroke-width":0,stroke:_,fill:(0,i.toRaphaelColor)($)},component:T,label:"canvas",callback:Q?i.stubFN:S,container:j}),X||T.addGraphicalElement("canvasBg",a),Q?(a.show(),t.show()):(a.hide(),t.hide()),l=I-N-ue,s=L+R+w+ue,c=O,g=ae,v=N+ue,C=w+ue,n=V.setAnimation({el:Z||"cubepath",component:T,index:0,attr:{cubepath:[l,s,c,g,v,C],stroke:_,"stroke-width":0,visibility:te?"visible":"hidden",fill:ne.replace(i.dropHash,i.HASHSTRING),noGradient:!oe},callback:te?i.stubFN:k,label:"canvas",container:j}),Z||T.addGraphicalElement("canvas3DBase",n),A=V.setAnimation({el:K||"path",attr:{path:[D,I,L+R,"H",O+I],stroke:u.tintshade(ne.replace(i.dropHash,i.HASHSTRING),.05).rgba},component:T,callback:te?i.stubFN:S,label:"canvas",container:j}),K||T.addGraphicalElement("canvas3dbaseline",A),te&&(x.call(n),A.show())},a.drawCanvas3dBar=function(){var e,t,a,n,o,r,l,s,c,u,P,A,N,w,T=this,F=T.getFromEnv("chart"),M=F.getFromEnv("dataSource"),B=F.config,E=B.canvasLeft,I=B.canvasTop,L=B.canvasWidth,O=B.canvasHeight,R=M.chart,G=F.getFromEnv("color-manager"),V=T.getGraphicalElement("canvasBorderElement"),z=T.getGraphicalElement("canvasElement"),H=T.config,W=H.clip={},Y=T.getContainer("canvasGroup"),U=T.getGraphicalElement("canvasBg"),j=T.getGraphicalElement("canvas3DBase"),X=F.getChildContainer("plotGroup"),Z=F.getChildContainer("datalabelsGroup"),J=T.getFromEnv("animationManager"),q=T.getGraphicalElement("canvas3dbaseline"),K=H.canvasBgColor,$=H.showCanvasBG=Boolean((0,i.pluckNumber)(R.showcanvasbg,1)),Q=B.canvasBgDepth,ee=B.showCanvasBase,te=B.canvasBaseDepth,ae=H.canvasBaseColor3D=(0,i.pluck)(R.canvasbasecolor,G.getColor(d)),ne=H.use3DLighting=(0,i.pluckNumber)(R.use3dlighting,1),oe=y.chart3D,re=H.canvasBorderRadius=(0,i.pluckNumber)(R.plotborderradius,0),ie=H.canvasBorderWidth=0,le=.5*ie,se=H.canvasBorderColor=(0,i.convertColor)((0,i.pluck)(R.canvasbordercolor,G.getColor(i.canvasBorderColorStr))),ce=H.canBGAlpha=(0,i.pluck)(R.canvasbgalpha,G.getColor(p)),ue=H.canBGColor=(0,i.pluck)(R.canvasbgcolor,G.getColor(oe.canvasBgColor)),de=H.xDepth,pe=H.yDepth;K=H.canvasBgColor=ne?{FCcolor:{color:(0,i.getDarkColor)(ue,85)+g+(0,i.getLightColor)(ue,55),alpha:ce+g+ce,ratio:i.BGRATIOSTRING,angle:(0,i.getAngle)(B.width-(B.marginLeft+B.marginRight),B.height-(B.marginTop+B.marginBottom),1)}}:(0,i.convertColor)(ue,ce),ue=ue.split(g)[0],ce=ce.split(g)[0],de=H.xDepth=5,pe=H.yDepth=5,o={x:E-le,y:I-le,width:L+ie,height:O+ie,r:re,"stroke-width":ie,stroke:se,"stroke-linejoin":ie>2?v:m},e=J.setAnimation({el:V||"rect",attr:o,container:Y,label:"canvas",component:T}),V||T.addGraphicalElement("canvasBorderElement",e),W[f]=[b(0,E-de),b(0,I),b(1,L+de),b(1,O+pe)],W[h]=[b(0,E-de),b(0,I-pe),1,b(1,O+2*pe)],N=W[f].slice(0),J.setAnimation({el:X,attr:{"clip-rect":N},component:T}),J.setAnimation({el:Z,attr:{"clip-rect":N},component:T}),o={x:E,y:I,width:L,height:O,r:re,"stroke-width":0,stroke:_,fill:(0,i.toRaphaelColor)(K)},t=J.setAnimation({el:z||"rect",attr:o,component:T,label:"canvas",container:Y}),z||T.addGraphicalElement("canvasElement",t),r=[D,E,I,"L",E*****Q,I-Q,E+L-Q,I-Q,E+L,I,"Z"],a=J.setAnimation({el:U||"path",attr:{path:r,"stroke-width":0,stroke:_,fill:(0,i.toRaphaelColor)(K)},component:T,callback:$?i.stubFN:S,label:"canvas",container:Y}),U||T.addGraphicalElement("canvasBg",a),$?(t.show(),a.show()):(t.hide(),a.hide()),l=E-de-te-1,s=I+pe+1,c=te,u=O,P=de+1,A=pe+1,n=J.setAnimation({el:j||"cubepath",attr:{cubepath:[l,s,c,u,P,A],stroke:_,"stroke-width":0,visibility:"hidden",fill:ae.replace(i.dropHash,i.HASHSTRING),noGradient:!ne},component:T,callback:ee?i.stubFN:k,label:"canvas",container:Y}),j||T.addGraphicalElement("canvas3DBase",n),w=J.setAnimation({el:q||"path",attr:{path:[D,E,I,"V",O+I],stroke:C.tintshade(ae.replace(i.dropHash,i.HASHSTRING),.05).rgba},component:T,callback:ee?i.stubFN:S,label:"canvas",container:Y}),q||T.addGraphicalElement("canvas3dbaseline",w),ee&&(w.show(),x.call(n))},t}(r["default"]),A=P;t["default"]=A},1068:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(625)),i=n(a(604)),l=n(a(1069)),s=function(e){function t(){var t;return(t=e.call(this)||this).registerFactory("canvas",i["default"]),t.registerFactory("dataset",l["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"MSBarCartesian3D"};var a=t.prototype;return a.getName=function(){return"MSBarCartesian3D"},a.parseChartAttr=function(t){e.prototype.parseChartAttr.call(this,t),this.config.drawTrendRegion=0},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.showplotborder=0,t.showzeroplaneontop=0},t}(r["default"]);t["default"]=s},1075:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(626)),i=n(a(604)),l=n(a(1069)),s=function(e){function t(){var t;return(t=e.call(this)||this).registerFactory("canvas",i["default"]),t.registerFactory("dataset",l["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"MSCartesian3D"};var a=t.prototype;return a.getName=function(){return"MSCartesian3D"},a.parseChartAttr=function(t){e.prototype.parseChartAttr.call(this,t),this.config.drawTrendRegion=0},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.showplotborder=0,t.drawcrosslineontop=0,t.showzeroplaneontop=0},t}(r["default"]);t["default"]=s},1082:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(993)),i=n(a(604)),l=function(e){function t(){var t;return(t=e.call(this)||this).registerFactory("canvas",i["default"]),t}(0,o["default"])(t,e);var a=t.prototype;return a.parseChartAttr=function(t){e.prototype.parseChartAttr.call(this,t),this.config.drawTrendRegion=0},t.getName=function(){return"MSDybaseCartesian3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this),this.config.is3D=!0,this.config.showzeroplaneontop=0},a.getName=function(){return"MSDybaseCartesian3D"},t}(r["default"]);t["default"]=l},624:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(517)),i=a(625),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.getName=function(){return"SSBarCartesian"},t.getName=function(){return"SSBarCartesian"},a._feedAxesRawData=function(){return i.__feedAxesRawData.call(this)},a._spaceManager=function(){i.__spaceManager.call(this)},a._postSpaceManagement=function(){i.__postSpaceManagement.call(this)},t}(r["default"]),s=l;t["default"]=s},969:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(603)),i=a(625),l=function(e){function t(){var t;return(t=e.call(this)||this).__feedAxesRawData=i.__feedAxesRawData,t.__spaceManager=i.__spaceManager,t.__postSpaceManagement=i.__postSpaceManagement,t}(0,o["default"])(t,e),t.getName=function(){return"SSBarCartesian3D"};var a=t.prototype;return a.getName=function(){return"SSBarCartesian3D"},a._feedAxesRawData=function(){return i.__feedAxesRawData.call(this)},a._spaceManager=function(){i.__spaceManager.call(this)},a._postSpaceManagement=function(){i.__postSpaceManagement.call(this)},t}(r["default"]);t["default"]=l},603:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(517)),i=n(a(604)),l=n(a(608)),s=function(e){function t(){var t;return(t=e.call(this)||this).registerFactory("canvas",i["default"]),t.registerFactory("dataset",l["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"SSCartesian3D"};var a=t.prototype;return a.getName=function(){return"SSCartesian3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.hasLegend=!1,t.showplotborder=0,t.drawcrosslineontop=0,t.showzeroplaneontop=0},t}(r["default"]);t["default"]=s},621:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(517)),l=n(a(617)),s=a(274),c=s.preDefStr.SEVENTYSTRING,u=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=0,t}(0,r["default"])(t,e),t.getName=function(){return"Area2D"};var a=t.prototype;return a.getName=function(){return"Area2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Area Chart",t.singleseries=!0,t.defaultDatasetType="area",t.anchorborderthickness=1,t.anchorimageurl=o,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=o,t.anchorbgalpha=s.HUNDREDSTRING,t.anchorimagealpha=s.HUNDREDSTRING,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=o,t.anchorbordercolor=o,t.anchorradius=3,t.showvalues=1,t.plotfillalpha=c,t.linedashlen=5,t.linedashgap=4,t.linedashed=o,t.linealpha=s.HUNDREDSTRING,t.linethickness=2,t.drawfullareaborder=1,t.inheritplotbordercolor=0,t.connectnulldata=0,t.enablemousetracking=!0,t.defaultcrosslinethickness=1},a.getDSdef=function(){return l["default"]},t}(i["default"]);t["default"]=u},623:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(624)),i=n(a(965)),l=function(e){function t(){var t;return(t=e.call(this)||this).isBar=!0,t}(0,o["default"])(t,e),t.getName=function(){return"Bar2D"};var a=t.prototype;return a.getType=function(){return"chartAPI"},a.getName=function(){return"Bar2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this),this.config.friendlyName="Bar Chart",this.config.singleseries=!0,this.config.defaultDatasetType="bar2d",this.config.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){},t}(r["default"]);t["default"]=l},968:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(969)),i=n(a(970)),l=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t.fireGroupEvent=!0,t.isBar=!0,t.defaultZeroPlaneHighlighted=!1,t}(0,o["default"])(t,e),t.getName=function(){return"Bar3D"};var a=t.prototype;return a.getName=function(){return"Bar3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.singleseries=!0,t.friendlyName="3D Bar Chart",t.defaultDatasetType="bar3d",t.showplotborder=0,t.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},t}(r["default"]);t["default"]=l},1042:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1043)),i=n(a(1051)),l=n(a(1053)),s=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"Bubble"};var a=t.prototype;return a.getName=function(){return"Bubble"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Bubble Chart",t.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},t}(r["default"]),c=s;t["default"]=c},516:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(517)),i=n(a(595)),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"Column2D"};var a=t.prototype;return a.getName=function(){return"Column2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this),this.config.friendlyName="Column Chart",this.config.singleseries=!0,this.config.defaultDatasetType="column",this.config.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},t}(r["default"]),s=l;t["default"]=s},602:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(603)),i=n(a(611)),l=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t.defaultZeroPlaneHighlighted=!1,t}(0,o["default"])(t,e),t.getName=function(){return"Column3D"};var a=t.prototype;return a.getName=function(){return"Column3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.hasLegend=!1,t.singleseries=!0,t.friendlyName="3D Column Chart",t.showplotborder=0,t.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},t}(r["default"]);t["default"]=l},986:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(973)),i=n(a(981)),l=a(274),s=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"Doughnut2D"};var a=t.prototype;return a.getName=function(){return"Doughnut2D"},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t);var a=this.config,n=this.getFromEnv("chart-attrib");a.doughnutradius=(0,l.pluck)(n.doughnutradius,"50%")},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Doughnut Chart",t.defaultDatasetType="Doughnut2D",t.singletonPlaceValue=!1},a.getDSdef=function(){return i["default"]},t}(r["default"]),c=s;t["default"]=c},988:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(979)),i=n(a(989)),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"Doughnut3D"};var a=t.prototype;return a.getName=function(){return"Doughnut3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="3D Doughnut Chart",t.defaultDatasetType="Doughnut3D",t.singletonPlaceValue=!1},a.getDSdef=function(){return i["default"]},t}(r["default"]),s=l;t["default"]=s},615:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(517)),l=n(a(616)),s=a(274),c=s.preDefStr.SEVENTYSTRING,u=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t.axisPaddingLeft=0,t.axisPaddingRight=0,t}(0,r["default"])(t,e),t.getName=function(){return"Line"};var a=t.prototype;return a.getName=function(){return"Line"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Line Chart",t.singleseries=!0,t.defaultDatasetType="line",t.anchorborderthickness=1,t.anchorimageurl=o,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=o,t.anchorbgalpha=s.HUNDREDSTRING,t.anchorimagealpha=s.HUNDREDSTRING,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=o,t.anchorbordercolor=o,t.anchorradius=3,t.showvalues=1,t.plotfillalpha=c,t.linedashlen=5,t.linedashgap=4,t.linedashed=o,t.linealpha=s.HUNDREDSTRING,t.linethickness=2,t.drawfullareaborder=1,t.connectnulldata=0,t.zeroplanethickness=1,t.enablemousetracking=!0,t.zeroplanealpha=40,t.showzeroplaneontop=0,t.defaultcrosslinethickness=1},a.getDSdef=function(){return l["default"]},t}(i["default"]);t["default"]=u},1099:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(626)),i=n(a(1100)),l=n(a(1102)),s=n(a(628)),c=a(274),u=function(e){(0,o["default"])(a,e),a.getName=function(){return"Marimekko"};var t=a.prototype;function a(){var t;return(t=e.call(this)||this).isValueAbs=!0,t.distributedColumns=!0,t.stack100percent=!0,t.isStacked=!0,t.registerFactory("dataset",(function(e){(0,s["default"])(e);var t=e.getChildren().canvas[0].getChildren("vCanvas")[0],a=e.config.defaultDatasetType||"";t.getChildren("datasetGroup_"+a)[0].addToEnv("categories",e.getFromEnv("dataSource").categories)}),["vCanvas"]),t}return t._checkInvalidSpecificData=function(){var e=this.getFromEnv("dataSource"),t=e.dataset,a=e.categories;if(!(t&&a&&0!==a.length&&a[0].category&&a[0].category instanceof Array))return!0},t.getName=function(){return"Marimekko"},t.parseChartAttr=function(t){e.prototype.parseChartAttr.call(this,t),this.config.showXAxisPercentValues=(0,c.pluckNumber)(t.chart&&t.chart.showxaxispercentvalues,1)},t.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Marimekko Chart",t.defaultDatasetType="marimekko",t.isstacked=!0,t.showpercentvalues=0,t.usepercentdistribution=1,t.showSum=1,t.enablemousetracking=!0},t.getDSdef=function(){return i["default"]},t.getDSGroupdef=function(){return l["default"]},a}(r["default"]);t["default"]=u},1058:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(625)),i=n(a(965)),l=n(a(1014)),s=n(a(628)),c=function(e){function t(){var t;return(t=e.call(this)||this).isBar=!0,t.registerFactory("dataset",s["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"MSBar2D"};var a=t.prototype;return a.getName=function(){return"MSBar2D"},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Multi-series Bar Chart",t.hasLegend=!0,t.defaultDatasetType="bar2d"},t}(r["default"]);t["default"]=c},1067:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1068)),i=n(a(1014)),l=n(a(970)),s=n(a(1069)),c="bar3d",u=function(e){function t(){var t;return(t=e.call(this)||this).defaultSeriesType=c,t.defaultPlotShadow=1,t.isBar=!0,t.defaultZeroPlaneHighlighted=!1,t.registerFactory("dataset",s["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"MSBar3D"};var a=t.prototype;return a.getName=function(){return"MSBar3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.friendlyName="Multi-series 3D Bar Chart",t.hasLegend=!0,t.defaultDatasetType=c,t.showplotborder=0,t.enablemousetracking=!0},a.getDSdef=function(){return l["default"]},a.getDSGroupdef=function(){return i["default"]},t}(r["default"]);t["default"]=u},1074:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1075)),i=n(a(611)),l=n(a(1014)),s=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t.defaultZeroPlaneHighlighted=!1,t}(0,o["default"])(t,e),t.getName=function(){return"MSColumn3D"};var a=t.prototype;return a.getName=function(){return"MSColumn3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.friendlyName="Multi-series 3D Column Chart",t.defaultDatasetType="column3d",t.showplotborder=0,t.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},t}(r["default"]);t["default"]=s},1081:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(1082)),l=n(a(611)),s=n(a(616)),c=n(a(1014)),u=n(a(1083)),d="100",p=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t.isDual=!0,t.registerFactory("dataset",u["default"],["vCanvas"]),t}(0,r["default"])(t,e),t.getName=function(){return"MSColumn3DLineDy"};var a=t.prototype;return a.getName=function(){return"MSColumn3DLineDy"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.sDefaultDatasetType="line",t.friendlyName="Multi-series 3D Column and Line Chart",t.defaultDatasetType="column3d",t.use3dlineshift=1,t.isdual=!0,t.showplotborder=0,t.enablemousetracking=!0,t.anchorborderthickness=1,t.anchorimageurl=o,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=o,t.anchorbgalpha=d,t.anchorimagealpha=d,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=o,t.anchorbordercolor=o,t.anchorradius=3,t.showvalues=1,t.plotfillalpha="70",t.linedashlen=5,t.linedashgap=4,t.linedashed=o,t.linealpha=d,t.linethickness=2,t.drawfullareaborder=1,t.connectnulldata=0},a.getDSdef=function(e){return"line"===e?s["default"]:l["default"]},a.getDSGroupdef=function(e){return"column3d"===e?c["default"]:o},a.getDSType=function(e,t){return e&&"line"===e.toLowerCase()||t?"line":"column3d"},t}(i["default"]);t["default"]=p},1095:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1090)),i=n(a(611)),l=n(a(616)),s=n(a(1014)),c=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t}(0,o["default"])(t,e),t.getName=function(){return"MSColumnLine3D"};var a=t.prototype;return a.getName=function(){return"MSColumnLine3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.friendlyName="Multi-series Column and Line Chart",t.use3dlineshift=1,t.showplotborder=0,t.enablemousetracking=!0},a.getDSdef=function(e){return"line"===e?l["default"]:i["default"]},a.getDSGroupdef=function(e){return"column3d"===e?s["default"]:undefined},a.getDSType=function(e){return e&&"line"===e.toLowerCase()?"line":"column3d"},t}(r["default"]);t["default"]=c},1093:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1064)),i=n(a(595)),l=n(a(617)),s=n(a(616)),c=n(a(1008)),u=n(a(1012)),d=n(a(1014)),p=n(a(995)),f=function(e){function t(){var t;return(t=e.call(this)||this).registerFactory("dataset",p["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"MSCombi2D"};var a=t.prototype;return a.getName=function(){return"MSCombi2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Multi-series Combination Chart",t.defaultDatasetType="column",t.enablemousetracking=!0,t.showzeroplaneontop=0},a.getDSdef=function(e){return"splinearea"===e?c["default"]:"spline"===e?u["default"]:"area"===e?l["default"]:"line"===e?s["default"]:i["default"]},a.getDSGroupdef=function(e){return"column"===e?d["default"]:undefined},a.getDSType=function(e){return void 0===e&&(e=""),"splinearea"===e.toLowerCase()?"splinearea":"spline"===e.toLowerCase()?"spline":"area"===e.toLowerCase()?"area":"line"===e.toLowerCase()?"line":"column"},t}(r["default"]);t["default"]=f},1090:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(1075)),l=n(a(611)),s=n(a(1008)),c=n(a(1012)),u=n(a(617)),d=n(a(616)),p=n(a(1014)),f=n(a(1083)),h="100",g=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t.registerFactory("dataset",f["default"],["vCanvas"]),t}(0,r["default"])(t,e),t.getName=function(){return"MSCombi3D"};var a=t.prototype;return a.getName=function(){return"MSCombi3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.friendlyName="Multi-series 3D Combination Chart",t.defaultDatasetType="column3d",t.showplotborder=0,t.enablemousetracking=!0,t.anchorborderthickness=1,t.anchorimageurl=o,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=o,t.anchorbgalpha=h,t.anchorimagealpha=h,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=o,t.anchorbordercolor=o,t.anchorradius=3,t.showvalues=1,t.plotfillalpha="70",t.linedashlen=5,t.linedashgap=4,t.linedashed=o,t.linealpha=h,t.linethickness=2,t.drawfullareaborder=1,t.connectnulldata=0},a.getDSdef=function(e){return"splinearea"===e?s["default"]:"spline"===e?c["default"]:"area"===e?u["default"]:"line"===e?d["default"]:l["default"]},a.getDSGroupdef=function(e){return"column3d"===e?p["default"]:o},a.getDSType=function(e){return void 0===e&&(e=""),"area"===e.toLowerCase()?"area":"line"===e.toLowerCase()?"line":"spline"===e.toLowerCase()?"spline":"splinearea"===e.toLowerCase()?"splinearea":"column3d"},t}(i["default"]);t["default"]=g},1007:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(993)),l=n(a(595)),s=n(a(617)),c=n(a(616)),u=n(a(1008)),d=n(a(1012)),p=n(a(1014)),f=a(274),h=n(a(995)),g=f.preDefStr.SEVENTYSTRING,v=function(e){function t(){var t;return(t=e.call(this)||this).isDual=!0,t.registerFactory("dataset",h["default"],["vCanvas"]),t}(0,r["default"])(t,e),t.getName=function(){return"MSCombidy2D"};var a=t.prototype;return a.getName=function(){return"MSCombidy2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Multi-series Dual Y-Axis Combination Chart",t.sDefaultDatasetType="line",t.defaultDatasetType="column",t.enablemousetracking=!0,t.isdual=1,t.anchorborderthickness=1,t.anchorimageurl=o,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=o,t.anchorbgalpha=f.HUNDREDSTRING,t.anchorimagealpha=f.HUNDREDSTRING,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=o,t.anchorbordercolor=o,t.anchorradius=3,t.showvalues=1,t.plotfillalpha=g,t.linedashlen=5,t.linedashgap=4,t.linedashed=o,t.linealpha=f.HUNDREDSTRING,t.linethickness=2,t.drawfullareaborder=1,t.connectnulldata=0,t.showzeroplaneontop=0},a.getDSdef=function(e){return"splinearea"===e?u["default"]:"spline"===e?d["default"]:"area"===e?s["default"]:"line"===e?c["default"]:l["default"]},a.getDSGroupdef=function(e){return"column"===e?p["default"]:o},a.getDSType=function(e){return void 0===e&&(e=""),"splinearea"===e.toLowerCase()?"splinearea":"spline"===e.toLowerCase()?"spline":"area"===e.toLowerCase()?"area":"line"===e.toLowerCase()?"line":"column"},t}(i["default"]);t["default"]=v},1087:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(1082)),l=n(a(611)),s=n(a(617)),c=n(a(616)),u=n(a(1008)),d=n(a(1012)),p=n(a(1014)),f=a(274),h=n(a(1083)),g=f.preDefStr.HUNDREDSTRING,v=f.preDefStr.SEVENTYSTRING,m=function(e){function t(){var t;return(t=e.call(this)||this).isDual=!0,t.registerFactory("dataset",h["default"],["vCanvas"]),t}(0,r["default"])(t,e),t.getName=function(){return"MSCombiDY3D"};var a=t.prototype;return a.getName=function(){return"MSCombiDY3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.sDefaultDatasetType="line",t.defaultDatasetType="column3d",t.showplotborder=0,t.isdual=1,t.enablemousetracking=!0,t.anchorborderthickness=1,t.anchorimageurl=o,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=o,t.anchorbgalpha=g,t.anchorimagealpha=g,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=o,t.anchorbordercolor=o,t.anchorradius=3,t.showvalues=1,t.plotfillalpha=v,t.linedashlen=5,t.linedashgap=4,t.linedashed=o,t.linealpha=g,t.linethickness=2,t.drawfullareaborder=1,t.connectnulldata=0},a.getDSGroupdef=function(e){return"column3d"===e.toLowerCase()?p["default"]:o},a.getDSdef=function(e){return"splinearea"===e.toLowerCase()?u["default"]:"spline"===e.toLowerCase()?d["default"]:"area"===e.toLowerCase()?s["default"]:"line"===e.toLowerCase()?c["default"]:l["default"]},a.getDSType=function(e){return"splinearea"===e.toLowerCase()?"splinearea":"spline"===e.toLowerCase()?"spline":"area"===e.toLowerCase()?"area":"line"===e.toLowerCase()?"line":"column3d"},t}(i["default"]);t["default"]=m},1057:(e,t,a)=>{var n=a(269);t.__esModule=!0,t._checkInvalidSpecificData=t["default"]=void 0;var o=n(a(288)),r=n(a(1058)),i=n(a(965)),l=n(a(1023)),s=n(a(1059)),c=function(){var e,t=this.getFromEnv("dataSource"),a=t.dataset,n=t.categories;if(!a||!n)return!0;if(e=a.length)for(;e--;)if(!a[e].dataset)return!0};t._checkInvalidSpecificData=c;var u=function(e){function t(){var t;return(t=e.call(this)||this).registerFactory("dataset",s["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"MSStackedBar2D"};var a=t.prototype;return a._checkInvalidSpecificData=function(){return c.call(this)},a.getName=function(){return"MSStackedBar2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Multi-series Stacked Bar Chart",t.defaultDatasetType="bar",t.isstacked=!0,t.showSum=0,t.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},t}(r["default"]);t["default"]=u},992:(e,t,a)=>{var n=a(269);t.__esModule=!0,t._setCategories=p,t["default"]=void 0;var o=n(a(288)),r=n(a(993)),i=a(997),l=a(999),s=n(a(1e3)),c=a(274),u=n(a(1001)),d=c.preDefStr.NINETYSTRING;function p(){var e,t,a=this,n=a.getFromEnv("dataSource"),o=n.dataset,r=a.getFromEnv("number-formatter"),i=a.getChildren("xAxis"),l=n.data||o&&o[0].data||[],s=[],c={};for(t=l.length-1;t>=0;t--)"true"===(e=l[t]).vline||"1"===e.vline||1===e.vline||!0===e.vline?(c[t]=e,l.splice(t,1)):null===r.getCleanValue(e.value,!0)&&l.splice(t,1);for(t in l.sort((function(e,t){return r.getCleanValue(t.value,!0)-r.getCleanValue(e.value,!0)})),s=l.slice(),c)s.splice(t,0,c[t]);i[0].setTickValues(s)}var f=function(e){function t(){var t;return(t=e.call(this)||this).isPercentage=!0,t.registerFactory("axis",s["default"],["canvas"]),t.registerFactory("dataset",u["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"Pareto2D"};var a=t.prototype;return a.getName=function(){return"Pareto2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.singleseries=!0,t.hasLegend=!1,t.defaultDatasetType="column",t.plotfillalpha=d,t.enablemousetracking=!0},a._setCategories=function(){p.call(this)},a._checkInvalidSpecificData=function(){var e=this.getFromEnv("dataSource").data;if(!e||!e.length)return!0},a.getDSdef=function(e){return"column"===e?i.ParetoColumnDataset:l.ParetoLineDataset},a.getDSGroupdef=function(){},t}(r["default"]);t["default"]=f},1003:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(992)),i=a(274),l=a(1004),s=a(999),c=n(a(604)),u=function(e){function t(){var t;return(t=e.call(this)||this).fireGroupEvent=!0,t.defaultPlotShadow=1,t.isPercentage=!0,t.registerFactory("canvas",c["default"]),t}(0,o["default"])(t,e),t.getName=function(){return"Pareto3D"};var a=t.prototype;return a.getName=function(){return"Pareto3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.friendlyName="3D Pareto Chart",t.singleseries=!0,t.hasLegend=!1,t.defaultDatasetType="column3d",t.plotfillalpha=i.preDefStr.NINETYSTRING,t.use3dlineshift=1,t.enablemousetracking=!0,t.showzeroplaneontop=0},a.getDSdef=function(e){return"column"===e?l.ParetoColumn3DDataset:s.ParetoLineDataset},t}(r["default"]);t["default"]=u},973:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(974)),i=n(a(520)),l=a(274),s=n(a(525)),c=n(a(527)),u=n(a(529)),d=n(a(977)),p=n(a(629)),f=a(627),h=a(286),g=Math,v=g.min,m=g.max,b=g.abs,C=g.PI,_=g.round,D=C/180,y=180/C,S=0,k=function(e,t,a){var n,o,r,i,l=!!a,s=t;return e?((i=(n=e.components&&e.components.data||[])[s=e.config.reversePlotOrder?n.length-s-1:s])&&(o=i.config,r=l!==i.config.sliced||void 0===a?e.plotGraphicClick.call(i.graphics.element):o.sliced),r):r},x=function(e){(0,o["default"])(a,e),a.getName=function(){return"Pie2D"};var t=a.prototype;function a(){var t;return(t=e.call(this)||this).defaultSeriesType="pie",t.defaultPlotShadow=1,t.reverseLegend=1,t.defaultPaletteOptions=undefined,t.sliceOnLegendClick=!0,t.dontShowLegendByDefault=!0,t.defaultZeroPlaneHighlighted=!1,t.hasCanvas=!0,t.eiMethods={isPlotItemSliced:function(e){var t,a,n=this.apiInstance,o=n&&n.getDatasets();return o&&(o=o[0])&&(t=o.components.data)&&t[e]&&(a=t[e].config)&&a.sliced},addData:function(){var e=this.apiInstance,t=e&&e.getDatasets();return t&&(t=t[0])&&t.addData.apply(t,arguments)},removeData:function(){var e=this.apiInstance,t=e&&e.getDatasets();return t&&(t=t[0])&&t.removeData.apply(t,arguments)},updateData:function(){var e=this.apiInstance,t=e&&e.getDatasets();return t&&(t=t[0])&&t.updateData.apply(t,arguments)},slicePlotItem:function(e,t,a){var n=this.apiInstance;if(!a)return k(n.getDatasets()[0],e,t);n.addJob("eiMethods-slice-plot"+S++,(function(){var o=k(n.getDatasets()[0],e,t);return"function"==typeof a&&a(o)}),h.priorityList.postRender)},startingAngle:function(e,t,a){var n,o=this.apiInstance;if(!a)return o._startingAngle(e,t);o.addJob("eiMethods-start-angle"+S++,(function(){n=o._startingAngle(e,t),"function"==typeof a&&a(n)}),h.priorityList.postRender)}},t.registerFactory("dataset",d["default"],["vCanvas","legend"]),t.registerFactory("legend",p["default"]),t}return t.getName=function(){return"Pie2D"},t.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.alignCaptionWithCanvas=0,t.formatnumberscale=1,t.isSingleSeries=!0,t.friendlyName="Pie Chart",t.defaultDatasetType="Pie2D",t.plotborderthickness=1,t.decimals=2,t.alphaanimation=0,t.singletonPlaceValue=!0,t.usedataplotcolorforlabels=0,t.enableslicing=l.ONESTRING,t.skipCanvasDrawing=!0},t.parseChartAttr=function(t){e.prototype.parseChartAttr.call(this,t);var a=this,n=a.getFromEnv("chart-attrib");a.config.showLegend=(0,l.pluckNumber)(n.showlegend,0),a.config.showvalues=(0,l.pluckNumber)(n.showvalues,1),a.config.showlabels=(0,l.pluckNumber)(n.showlabels,1)},t.configureAttributes=function(e){var t=this,a=t.config;t.parseChartAttr(e),t.createComponent(e),t.config.skipConfigureIteration.axis=!0,t.configureChildren(),t.getFromEnv("toolTipController").setStyle({backgroundColor:l.hasSVG?(0,l.convertColor)(a.tooltipbgcolor||"FFF",a.tooltipbgalpha||100):(a.tooltipbgcolor||"FFF").replace(/\s+/g,"").replace(/^#?([a-f0-9]+)/gi,"#$1"),color:(a.tooltipcolor||a.basefontcolor||"545454").replace(/^#?([a-f0-9]+)/gi,"#$1"),borderColor:l.hasSVG?(0,l.convertColor)(a.tooltipbordercolor||"666",a.tooltipborderalpha||100):(a.tooltipbordercolor||"666").replace(/\s+/g,"").replace(/^#?([a-f0-9]+)/gi,"#$1"),borderWidth:(0,l.pluckNumber)(a.tooltipborderthickness,1)+"px",showToolTipShadow:(0,l.pluckNumber)(a.showtooltipshadow||0),borderRadius:(0,l.pluckNumber)(a.tooltipborderradius,0)+"px",fontSize:(0,l.pluckNumber)(this.computeFontSize(a.basefontsize),10)+"px",fontFamily:a.basefont||this.getFromEnv("style").inCanfontFamily,padding:(0,l.pluckNumber)(a.tooltippadding||3)+"px"})},t.createComponent=function(){var e,t=this;e=t.config.skipConfigureIteration={},t.createBaseComponent(),t.getFromEnv("animationManager").setAnimationState(t._firstConfigure?"initial":"update"),(0,l.componentFactory)(t,s["default"],"caption"),e.caption=!0,(0,l.componentFactory)(t,c["default"],"subCaption"),e.subCaption=!0,(0,l.componentFactory)(t,u["default"],"background"),e.background=!0,e.canvas=!0,t._createConfigurableComponents&&t._createConfigurableComponents(),t.config.realtimeEnabled&&t._realTimeConfigure&&t._realTimeConfigure()},t._postSpaceManagement=function(){this.config.showLegend&&this.getChildren("legend")&&this.getChildren("legend")[0].postSpaceManager(),this.allocateDimensionOfChartMenuBar()},t._checkInvalidSpecificData=function(){var e,t,a,n=0,o=0,r=this.getFromEnv("dataSource").data;if(!r)return!0;for(t=r.length||0,e=0;e<t;e++)a=Number(r[e].value),n+=isNaN(a)||0!==a?0:1,o+=isNaN(a)?1:0;return n+o>=t},t._spaceManager=function(){var e,t,a,n,o,r,i=this,s=i.config,c=i.getChildren("dataset")[0],u=c.components.data,d=c.config,p=i.getFromEnv("legend"),f=i.getFromEnv("color-manager"),h=i.getFromEnv("smartLabel"),g=i.getFromEnv("chartWidth"),C=i.getFromEnv("chartHeight"),_=[],D=d.dataLabelCounter,y=0,S=i.getFromEnv("dataSource").chart,k=(0,l.pluckNumber)(S.managelabeloverflow,0),x=(0,l.pluckNumber)(S.slicingdistance),P=d.preSliced||s.allPlotSliceEnabled!==l.ZEROSTRING||S.showlegend===l.ONESTRING&&S.interactivelegend!==l.ZEROSTRING?b((0,l.pluckNumber)(x,20)):0,A=/%/g.test(S.pieradius),N=(0,l.pluckNumber)(A?Math.min(g/2,C/2)*(parseFloat(S.pieradius)/100):S.pieradius,0),w=(0,l.pluckNumber)(S.enablesmartlabels,S.enablesmartlabel,1),T=w?(0,l.pluckNumber)(S.skipoverlaplabels,S.skipoverlaplabel,1):0,F=(0,l.pluckNumber)(S.issmartlineslanted,1),M=D?(0,l.pluckNumber)(S.labeldistance,S.smartlabelclearance,5):P,B=s.width,E=s.height,I=(i._manageActionBarSpace(.225*E)||{}).bottom,L=B-(s.marginRight+s.marginLeft),O=E-(s.marginTop+s.marginBottom)-(I?I+s.marginBottom:0),R=v(O,L),G=(0,l.pluck)(S.smartlinecolor,f.getColor("plotFillColor")),V=(0,l.pluckNumber)(S.smartlinealpha,100),z=(0,l.pluckNumber)(S.smartlinethickness,.7),H=d.dataLabelOptions=c._parseDataLabelOptions(),W=H.style,Y=D?(0,l.pluckNumber)(parseInt(W.lineHeight,10),12):0,U=0===N?.15*R:N,j=2*U,X=d.pieYScale,Z=d.pieSliceDepth,J=(0,l.pluck)(S.legendposition,l.POSITION_BOTTOM).toLowerCase().split("-");if(H.connectorWidth=z,H.connectorPadding=(0,l.pluckNumber)(S.connectorpadding,5),H.connectorColor=(0,l.convertColor)(G,V),r=j+2*(Y+(e=(s.showvalues||s.showlabels)&&("inside"!==d.labelPosition||"inside"!==d.valuePosition)?M+P:M)),O-=((a=i._manageChartMenuBar(r<O?O-r:O/2)).top||0)+(a.bottom||0),d.showLegend&&(i.config.hasLegend=!0,J[0]===l.POSITION_RIGHT||J[0]===l.POSITION_LEFT?(t=p._manageLegendPosition(O/2),L-=m(t.left,t.right)):(t=p._manageLegendPosition(O/2),O-=m(t.top,t.bottom)),t&&i._allocateSpace(t)),h.useEllipsesOnOverflow(s.useEllipsesWhenOverflow),1!==D)for(;D--;)h.setStyle(u[D].config.style||s.dataLabelStyle),_[D]=n=h.getOriSize(u[D].config.displayValue),y="inside"!==d.labelPosition||"inside"!==d.valuePosition?m(y,n.width):0;0===N?U=i._stubRadius(L,y,O,e,P,Y,U,M):(d.slicingDistance=P,d.pieMinRadius=U,H.distance=M),o=O-2*(U*X+Y),d.managedPieSliceDepth=Z>o?Z-o:d.pieSliceDepth,H.isSmartLineSlanted=F,H.enableSmartLabels=w,H.skipOverlapLabels=T,H.manageLabelOverflow=k},t._stubRadius=function(e,t,a,n,o,r,i,s){var c,u=i,d=o,p=this.getChildren("dataset")[0],f=p.config,h=this.getFromEnv("dataSource").chart,g=(0,l.pluckNumber)(h.slicingdistance),b=f.dataLabelOptions||(f.dataLabelOptions=p._parseDataLabelOptions());return(c=v(e/2-t-d,a/2-r)-n)>=u?u=c:g||(d=m(v(n-(u-c),d),10)),f.slicingDistance=d,f.pieMinRadius=u,b.distance=s,u},t._startingAngle=function(e,t){var a,n=e,o=this.getChildren("dataset")[0],r=o.config,i=(a=r.startAngle)*-y+(-1*a<0?360:0);return isNaN(n)||r.singletonCase||r.isRotating||(n+=t?i:0,r.startAngle=-n*D,o._rotate(n),i=n),_(100*((i%=360)+(i<0?360:0)))/100},t._manageLegendSpace=function(){f._manageLegendSpace.call(this)},t.getDSdef=function(){return r["default"]},a}(i["default"]),P=x;t["default"]=P},979:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(973)),i=n(a(980)),l=a(274),s=Math,c=s.round,u=s.min,d=s.max,p=s.PI,f=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=0,t}(0,o["default"])(t,e),t.getName=function(){return"Pie3D"};var a=t.prototype;return a.getName=function(){return"Pie3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.friendlyName="3D Pie Chart",t.defaultDatasetType="Pie3D",t.plotborderthickness=.1,t.alphaanimation=1},a.animate=function(){var e,t,a,n,o,r,i,l,s=this.components.dataset[0],c=s.config,u=s.components.data,d=u.length,f=c.alphaAnimation,h=this.get("config","animationObj"),g=h.duration||0,v=h.dummyObj,m=h.animObj,b=h.animType;if(!f)for(e=0;e<d;e++)a=(t=u[e]).graphics,o=t.config.shapeArgs,r=2*p,(n=a.element)&&(n.attr({sAngle:r,eAngle:r}),i=o.sAngle,l=o.eAngle,undefined.animateWith(v,m,{cx:i-r,cy:l-r},g,b))},a._stubRadius=function(e,t,a,n,o,r,i){var s,c=a,p=i,f=o,h=n,g=this.getChildren("dataset")[0],v=g.config,m=g.config,b=(0,l.pluckNumber)(m.slicingdistance),C=v.dataLabelOptions||(v.dataLabelOptions=g._parseDataLabelOptions()),_=v.pieYScale,D=v.pieSliceDepth;return(s=u(e/2-t-f,((c-=D)/2-r)/_)-h)>=p?p=s:b||(f=h=d(u(h-(p-s),f),10)),v.slicingDistance=f,v.pieMinRadius=p,C.distance=h,p},a._startingAngle=function(e,t){var a,n=e,o=this.getChildren("dataset")[0],r=o.config,i=(a=r.startAngle)+(a<0?360:0);return isNaN(n)||r.singletonCase||r.isRotating||(n+=t?i:0,o._rotate(n),i=n),c(100*((i%=360)+(i<0?360:0)))/100},a.getDSdef=function(){return i["default"]},t}(r["default"]);t["default"]=f},1043:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1044)),i=n(a(1048)),l=function(e){function t(){var t;return(t=e.call(this)||this).isXY=!0,t.defaultZeroPlaneHighlighted=!1,t}(0,o["default"])(t,e),t.getName=function(){return"Scatter"};var a=t.prototype;return a.getName=function(){return"Scatter"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scatter Chart",t.hasLegend=!0,t.allowreversexaxis=!0,t.enablemousetracking=!0},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){},t}(r["default"]);t["default"]=l},1018:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(1019)),l=n(a(617)),s=a(274),c=s.preDefStr.SEVENTYSTRING,u=function(e){function t(){var t;return(t=e.call(this)||this).hasScroll=!0,t.defaultPlotShadow=0,t.binSize=0,t}(0,r["default"])(t,e),t.getName=function(){return"ScrollArea2D"};var a=t.prototype;return a.getName=function(){return"ScrollArea2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scrollable Multi-series Area Chart",t.defaultDatasetType="scrollarea2d",t.enablemousetracking=!0,t.anchorborderthickness=1,t.anchorimageurl=o,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=o,t.anchorbgalpha=s.HUNDREDSTRING,t.anchorimagealpha=s.HUNDREDSTRING,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=o,t.anchorbordercolor=o,t.anchorradius=3,t.showvalues=1,t.plotfillalpha=c,t.canvasborderthickness=1,t.linedashlen=5,t.linedashgap=4,t.linedashed=o,t.linealpha=s.HUNDREDSTRING,t.linethickness=2,t.drawfullareaborder=1,t.connectnulldata=0,t.defaultcrosslinethickness=1,t.avgScrollPointWidth=75},a.getDSdef=function(){return l["default"]},a.getDSGroupdef=function(){return o},t}(i["default"]);t["default"]=u},1035:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(625)),i=n(a(965)),l=n(a(1014)),s=n(a(628)),c=a(1015),u="bar2d",d=function(e){function t(){var t;return(t=e.call(this)||this).isBar=!0,t.eiMethods={scrollTo:c.scrollTo},t.hasScroll=!0,t.registerFactory("dataset",s["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"ScrollBar2D"},t.includeInputOptions=function(){return["SwipeGesture"]};var a=t.prototype;return a.getName=function(){return"ScrollBar2D"},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scrollable Multi-series Bar Chart",t.hasLegend=!0,t.defaultDatasetType=u,t.avgScrollPointWidth=40},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t),c.configurer.call(this,t)},a._setAxisScale=function(){c.setAxisScale.call(this,u)},a._resetViewPortConfig=function(){c.resetViewPortConfig.call(this)},t}(r["default"]);t["default"]=d},1019:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1020)),i=n(a(595)),l=n(a(1014)),s=a(1015),c=function(e){function t(){var t;return(t=e.call(this)||this).tooltipConstraint="plot",t.hasScroll=!0,t.defaultPlotShadow=1,t.binSize=0,t.eiMethods.scrollTo=s.scrollTo,t}(0,o["default"])(t,e),t.getName=function(){return"ScrollColumn2D"},t.includeInputOptions=function(){return["SwipeGesture"]};var a=t.prototype;return a.getName=function(){return"ScrollColumn2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.defaultDatasetType="column",t.showzeroplaneontop=1,t.friendlyName="Scrollable Multi-series Column Chart",t.avgScrollPointWidth=40,t.canvasborderthickness=1},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t),s.configurer.call(this,t)},a._setAxisScale=function(){s.setAxisScale.call(this)},a.parseChartAttr=function(t){e.prototype.parseChartAttr.call(this,t)},a._resetViewPortConfig=function(){s.resetViewPortConfig.call(this)},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},t}(r["default"]);t["default"]=c},1017:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1018)),i=n(a(595)),l=n(a(617)),s=n(a(616)),c=n(a(1008)),u=n(a(1012)),d=n(a(1014)),p=n(a(995)),f=function(e){function t(){var t;return(t=e.call(this)||this).hasScroll=!0,t.defaultPlotShadow=1,t.registerFactory("dataset",p["default"],["vCanvas"]),t}(0,o["default"])(t,e),t.getName=function(){return"ScrollCombi2D"};var a=t.prototype;return a.getName=function(){return"ScrollCombi2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scrollable Combination Chart",t.defaultDatasetType="column",t.zeroplanethickness=1,t.zeroplanealpha=80,t.enablemousetracking=!0,t.showzeroplaneontop=0,t.defaultcrosslinethickness=null,t.avgScrollPointWidth=40,t.canvasborderthickness=1},a.getDSdef=function(e){return"splinearea"===e?c["default"]:"spline"===e?u["default"]:"area"===e?l["default"]:"line"===e?s["default"]:i["default"]},a.getDSGroupdef=function(e){return"column"===e?d["default"]:undefined},a.getDSType=function(e){return void 0===e&&(e=""),"splinearea"===e.toLowerCase()?"splinearea":"spline"===e.toLowerCase()?"spline":"area"===e.toLowerCase()?"area":"line"===e.toLowerCase()?"line":"column"},t}(r["default"]);t["default"]=f},1006:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1007)),i=a(1015),l=function(e){function t(){var t;return(t=e.call(this)||this).hasScroll=!0,t.eiMethods={scrollTo:i.scrollTo},t}(0,o["default"])(t,e),t.getName=function(){return"ScrollCombiDy2D"},t.includeInputOptions=function(){return["SwipeGesture"]};var a=t.prototype;return a.getName=function(){return"ScrollCombiDy2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scrollable Dual Y-Axis Combination Chart",t.defaultDatasetType="column",t.showzeroplaneontop=0,t.avgScrollPointWidth=40,t.canvasborderthickness=1},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t),i.configurer.call(this,t)},a._setAxisScale=function(){i.setAxisScale.call(this)},a._resetViewPortConfig=function(){i.resetViewPortConfig.call(this)},t}(r["default"]);t["default"]=l},1038:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1018)),i=n(a(616)),l=function(e){function t(){var t;return(t=e.call(this)||this).defaultPlotShadow=1,t.binSize=0,t}(0,o["default"])(t,e),t.getName=function(){return"ScrollLine2D"};var a=t.prototype;return a.getName=function(){return"ScrollLine2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scrollable Multi-series Line Chart",t.defaultDatasetType="line",t.zeroplanethickness=1,t.zeroplanealpha=40,t.showzeroplaneontop=0,t.enablemousetracking=!0,t.defaultcrosslinethickness=1,t.avgScrollPointWidth=75,t.canvasborderthickness=1},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){},t}(r["default"]);t["default"]=l},1026:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1027)),i=a(1015),l=function(e){function t(){var t;return(t=e.call(this)||this).hasScroll=!0,t.eiMethods={scrollTo:i.scrollTo},t}(0,o["default"])(t,e),t.getName=function(){return"ScrollMSStackedColumn2D"},t.includeInputOptions=function(){return["SwipeGesture"]};var a=t.prototype;return a.getName=function(){return"ScrollMSStackedColumn2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.isstacked=!0,t.friendlyName="Scrollable MultiSeries Stacked Column Chart",t.defaultDatasetType="column",t.showzeroplaneontop=1,t.avgScrollPointWidth=75,t.canvasborderthickness=1},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t),i.configurer.call(this,t)},t}(r["default"]);l.prototype._setAxisScale=i.setAxisScale,l.prototype._resetViewPortConfig=i.resetViewPortConfig;var s=l;t["default"]=s},1030:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1031)),i=a(1015),l=function(e){function t(){var t;return(t=e.call(this)||this).hasScroll=!0,t.eiMethods={scrollTo:i.scrollTo},t}(0,o["default"])(t,e),t.getName=function(){return"ScrollMSStackedColumn2DLineDY"},t.includeInputOptions=function(){return["SwipeGesture"]};var a=t.prototype;return a.getName=function(){return"ScrollMSStackedColumn2DLineDY"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.isstacked=!0,t.friendlyName="Scrollable Multi-series Dual Y-Axis Stacked Column and Line Chart",t.defaultDatasetType="column",t.sDefaultDatasetType="line",t.showzeroplaneontop=1,t.avgScrollPointWidth=75,t.canvasborderthickness=1},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t),i.configurer.call(this,t)},a._setAxisScale=function(){i.setAxisScale.call(this)},a._resetViewPortConfig=function(){i.resetViewPortConfig.call(this)},t}(r["default"]);l.prototype._setAxisScale=i.setAxisScale,l.prototype._resetViewPortConfig=i.resetViewPortConfig;var s=l;t["default"]=s},1034:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1035)),i=n(a(1023)),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"ScrollStackedBar2D"};var a=t.prototype;return a.getName=function(){return"ScrollStackedBar2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scrollable Stacked Bar Chart",t.isstacked=!0,t.avgScrollPointWidth=75,t.canvasborderthickness=1,t.showSum=0},a.getDSGroupdef=function(){return i["default"]},t}(r["default"]),s=l;t["default"]=s},1022:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1019)),i=n(a(1023)),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"ScrollStackedColumn2D"};var a=t.prototype;return a.getName=function(){return"ScrollStackedColumn2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Scrollable Stacked Column Chart",t.isstacked=!0,t.showSum=0,t.canvasborderthickness=1,t.avgScrollPointWidth=75},a.getDSGroupdef=function(){return i["default"]},t}(r["default"]),s=l;t["default"]=s},1062:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1063)),i=n(a(1023)),l=a(274),s=n(a(280)),c=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedArea2D"};var a=t.prototype;return a.getName=function(){return"StackedArea2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Stacked Area Chart",t.plotfillalpha=l.HUNDREDSTRING,t.showSum=0,t.isstacked=1,this.addToEnv("useImprovedLabelPlacement",!0),this.addToEnv("useLinePlotGroupForAnchorPlacement",!0)},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t);var a=this.config,n=this.getFromEnv("chart-attrib");a.showSum=(0,s["default"])(n.showsum,a.showSum),a.showSum&&(a.valueposition=(0,l.parseUnsafeString)((0,l.pluck)(n.valueposition,"below")))},a.getDSGroupdef=function(){return i["default"]},t}(r["default"]),u=c;t["default"]=u},1125:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1023)),i=n(a(616)),l=n(a(617)),s=n(a(1123)),c=a(274),u="line",d="area",p=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedArea2DLineDy"};var a=t.prototype;return a.getName=function(){return"StackedArea2DLineDy"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Stacked 2D Area and Line Chart",t.plotfillalpha=c.HUNDREDSTRING,t.isstacked=1,t.defaultDatasetType=d,t.stack100percent=0,t.defaultcrosslinethickness=1,this.addToEnv("useImprovedLabelPlacement",!0),this.addToEnv("useLinePlotGroupForAnchorPlacement",!0)},a.getDSdef=function(e){return e===u?i["default"]:l["default"]},a.getDSGroupdef=function(e){return e===d?r["default"]:undefined},a.getDSType=function(e){return void 0===e&&(e=""),e.toLowerCase()===u?u:d},t}(s["default"]),f=p;t["default"]=f},1071:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1058)),i=n(a(1023)),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedBar2D"};var a=t.prototype;return a.getName=function(){return"StackedBar2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Stacked Bar Chart",t.enablemousetracking=!0,t.maxbarheight=50,t.isstacked=!0,t.showSum=0},a.getDSGroupdef=function(){return i["default"]},t}(r["default"]),s=l;t["default"]=s},1066:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1067)),i=n(a(1023)),l=n(a(970)),s=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedBar3D"};var a=t.prototype;return a.getName=function(){return"StackedBar3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="3D Stacked Bar Chart",t.enablemousetracking=!0,t.maxbarheight=50,t.isstacked=!0,t.showSum=0},a.getDSdef=function(){return l["default"]},a.getDSGroupdef=function(){return i["default"]},t}(r["default"]),c=s;t["default"]=c},1077:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1020)),i=n(a(595)),l=n(a(1023)),s=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedColumn2D"};var a=t.prototype;return a.getName=function(){return"StackedColumn2D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Stacked Column Chart",t.isstacked=!0,t.showSum=0},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},t}(r["default"]),c=s;t["default"]=c},1092:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1093)),i=n(a(595)),l=n(a(616)),s=n(a(1012)),c=n(a(1023)),u=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedColumn2DLine"};var a=t.prototype;return a.getName=function(){return"StackedColumn2DLine"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="Stacked Column and Line Chart",t.defaultDatasetType="column",t.isstacked=!0,t.stack100percent=0,t.enablemousetracking=!0,t.showSum=0},a.getDSdef=function(e){return"spline"===e?s["default"]:"line"===e?l["default"]:i["default"]},a.getDSGroupdef=function(e){return"column"===e?c["default"]:undefined},a.getDSType=function(e){return void 0===e&&(e=""),"line"===e.toLowerCase()?"line":"column"},t}(r["default"]),d=u;t["default"]=d},1123:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=a(274),i=n(a(1023)),l=n(a(616)),s=n(a(595)),c=n(a(993)),u="Stacked 2D Column and Line Chart",d="line",p="column",f=r.preDefStr.SEVENTYSTRING,h=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedColumn2DLineDy"};var a=t.prototype;return a.getName=function(){return"StackedColumn2DLineDy"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName=u,t.sDefaultDatasetType=d,t.friendlyName=u,t.defaultDatasetType=p,t.isdual=!0,t.isstacked=!0,t.enablemousetracking=!0,t.stack100percent=0,t.showSum=0,t.anchorborderthickness=1,t.anchorimageurl=r.UNDEF,t.anchorimagepadding=1,t.anchorsides=1,t.anchoralpha=r.UNDEF,t.anchorbgalpha=r.HUNDREDSTRING,t.anchorimagealpha=r.HUNDREDSTRING,t.anchorimagescale=100,t.anchorstartangle=90,t.anchorshadow=0,t.anchorbgcolor=r.UNDEF,t.anchorbordercolor=r.UNDEF,t.anchorradius=3,t.showvalues=1,t.plotfillalpha=f,t.linedashlen=5,t.linedashgap=4,t.linedashed=r.UNDEF,t.linealpha=r.HUNDREDSTRING,t.linethickness=2,t.drawfullareaborder=1,t.connectnulldata=0,t.showzeroplaneontop=0},a.getDSdef=function(e){return e===d?l["default"]:s["default"]},a.getDSGroupdef=function(e){return e===p?i["default"]:r.UNDEF},a.getDSType=function(e){return void 0===e&&(e=""),e.toLowerCase()===d?d:p},t}(c["default"]),g=h;t["default"]=g},1073:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1074)),i=n(a(611)),l=n(a(1023)),s=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedColumn3D"};var a=t.prototype;return a.getName=function(){return"StackedColumn3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.friendlyName="3D Stacked Column Chart",t.showSum=0,t.maxbarheight=50,t.enablemousetracking=!0,t.isstacked=!0},a.getDSdef=function(){return i["default"]},a.getDSGroupdef=function(){return l["default"]},t}(r["default"]),c=s;t["default"]=c},1089:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1090)),i=n(a(611)),l=n(a(616)),s=n(a(1023)),c=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedColumn3DLine"};var a=t.prototype;return a.getName=function(){return"StackedColumn3DLine"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.friendlyName="Stacked 3D Column and Line Chart",t.use3dlineshift=1,t.isstacked=!0,t.stack100percent=0,t.showplotborder=0,t.enablemousetracking=!0,t.showSum=0},a.getDSdef=function(e){return"line"===e?l["default"]:i["default"]},a.getDSGroupdef=function(e){return"column"===e?s["default"]:undefined},a.getDSType=function(e){return e&&"line"===e.toLowerCase()?"line":"column"},t}(r["default"]),u=c;t["default"]=u},1080:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1081)),i=n(a(1023)),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e),t.getName=function(){return"StackedColumn3DLineDy"};var a=t.prototype;return a.getName=function(){return"StackedColumn3DLineDy"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.is3D=!0,t.sDefaultDatasetType="line",t.friendlyName="Stacked 3D Column and Line Chart",t.defaultDatasetType="column3d",t.use3dlineshift=1,t.isdual=!0,t.isstacked=!0,t.showplotborder=0,t.enablemousetracking=!0,t.showSum=0},a.getDSGroupdef=function(e){return"column3d"===e?i["default"]:undefined},t}(r["default"]),s=l;t["default"]=s},612:(e,t)=>{t.__esModule=!0,t._checkPointerOverColumn=function(e,t,n){var o,r,i,l,s,c,u,d,p,f=this.getFromEnv("chart").config,h=f.plotborderthickness,g=f.showplotborder,v=this.components.data,m=v[e],b=10;if(!m)return;if(o=m.config.setValue,s=(s=(h=g?h:0)/2)%2==0?s+1:a(s),null!==o&&(c=m._xPos-b,d=m._width+b,u=m._yPos,p=m._height+b,i=n-u+s,l=!!(l=!!(l=(r=t-c+s)>=0&&r<=d+h&&i>=0&&i<=p+h)&&t+n-(c+u)-b>0)&&t+n-(c+u+d+p)+b<0))return{pointIndex:e,hovered:l,pointObj:v[e]}};var a=Math.round},971:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(966)),r={"initial.dataset.bar3D":o["default"]["initial.dataset.bar2D"],"legendInteraction.dataset.bar3D":o["default"]["legendInteraction.dataset.bar2D"]};t["default"]=r},970:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(611)),i=a(965),l=a(282),s=n(a(971));(0,l.addDep)({name:"bar3DAnimation",type:"animationRule",extension:s["default"]});var c=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"dataset"},a.getName=function(){return"bar3D"},a._checkPointerOverColumn=function(e,t,a){var n,o,r,i,l,s,c,u=this.getFromEnv("chart").config,d=u.plotborderthickness,p=u.showplotborder,f=this.components.data,h=f[e];if(h)return d=p?d:0,null!==h.config.setValue&&(l=h._yPos,c=h._height+5,i=h._xPos-5,s=h._width+5,o=a-l,r=!!(r=!!(r=(n=t-i)>=0&&n<=s+d&&o>=0&&o<=c+d)&&t+a-(i+l)-5>0)&&t+a-(i+l+s+c)+5<0)?{pointIndex:e,hovered:r,pointObj:f[e]}:void 0},a._getHoveredPlot=function(e,t){var a,n,o=this,r=o.getFromEnv("chart").isBar;return a=o.getFromEnv("xAxis").getValue(r?t:e),(n=Math.round(a))-a>0?o._checkPointerOverColumn(n,e,t)||o._checkPointerOverColumn(n-1,e,t):o._checkPointerOverColumn(n+1,e,t)||o._checkPointerOverColumn(n,e,t)},a.drawLabel=function(){var e=this.config;i.drawLabel.call(this,e.scrollMinVal,e.scrollMaxVal)},t}(r["default"]),u=c;t["default"]=u},1052:(e,t)=>{t.__esModule=!0,t["default"]=void 0;t["default"]={"initial.dataset.bubble":function(){return{"circle.appearing":function(e){return[{initialAttr:{cx:e.attr.cx,cy:e.attr.cy,r:0},slot:"plot"}]},"group.appearing":function(e){return"label-group"===e.attr.name?[{initialAttr:{opacity:0},finalAttr:{opacity:1},slot:"final"}]:[{initialAttr:{opacity:1},finalAttr:{opacity:1},slot:"final"}]},"*":null}}}},1051:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=n(a(1048)),l=a(595),s=a(274),c=n(a(1049)),u=a(282),d=n(a(1052)),p=s.hasTouch?s.TOUCH_THRESHOLD_PIXELS:s.CLICK_THRESHOLD_PIXELS,f=s.preDefStr.setRolloverAttrStr,h=s.preDefStr.setRolloutAttrStr,g="DataPlotRollOut",v=Math,m=v.round,b=v.min,C=v.max;(0,u.addDep)({name:"bubbleAnimation",type:"animationRule",extension:d["default"]});var _=function(e){function t(){return e.apply(this,arguments)||this}(0,r["default"])(t,e);var a=t.prototype;return a.getType=function(){return"dataset"},a.getName=function(){return"bubble"},a.configureAttributes=function(e){if(!e)return!1;this.trimData(e),this.config.JSONData=e;var t,a,n,r,i,c,u,d,p,f,h,g,v,m,_,D,y,S,k,x,P,A,N,w=this,T=w.getFromEnv("chart"),F=T.getFromEnv("dataSource"),M=T.config,B=F.chart,E=w.config.JSONData,I=w.config,L=E.data||[],O=w.getFromEnv("color-manager"),R=w.index,G=w.getFromEnv("number-formatter"),V=(0,s.pluck)((0,s.parseUnsafeString)(B.tooltipsepchar),", "),z=-Infinity,H=+Infinity,W=z,Y=H,U=z,j=H,X=z,Z=H;for(I.usePattern=(0,s.pluckNumber)(M.usePattern,0),I.patternType=l.PATTERN_TYPES.includes(E.patterntype)?E.patterntype:M.patternType,I.patternAngle=(0,s.pluckNumber)(E.patternangle,B.patternangle,I.patternType===l.PATTERN_TYPES[0]?40:0),I.patternDensity=(0,s.pluckNumber)(E.patterndensity,M.patternDensity),I.patternSize=(0,s.pluckNumber)(E.patternsize,I.patternType===l.PATTERN_TYPES[0]?2:4),I.patternAlpha=(0,s.pluckNumber)(E.patternalpha,M.patternAlpha),I.patternBgColor=(0,s.pluck)(E.patternbgcolor,I.patternBgColor),I.seriesname=(0,s.parseUnsafeString)(E.seriesname),I.includeinlegend=(0,s.pluckNumber)(E.includeinlegend,I.seriesname?1:0),I.anchorBgColor=(0,s.getFirstColor)((0,s.pluck)(E.color,E.plotfillcolor,B.plotfillcolor,O.getPlotColor(R))),I.showPlotBorder=(0,s.pluckNumber)(E.showplotborder,B.showplotborder,1),I.anchorBorderThickness=I.showPlotBorder?(0,s.pluckNumber)(E.plotborderthickness,B.plotborderthickness,1):0,I.anchorBorderColor=(0,s.getFirstColor)((0,s.pluck)(E.plotbordercolor,B.plotbordercolor,I.usePattern?I.anchorBgColor:"666666")),I.plotFillAlpha=(0,s.pluck)(E.plotfillalpha,E.bubblefillalpha,B.plotfillalpha,"100"),I.plotBorderAlpha=(0,s.pluck)(E.plotborderalpha,B.plotborderalpha,"95"),I.negativeColor=(0,s.pluck)(B.negativecolor,"FF0000"),I.is3d=0!==(0,s.pluckNumber)(B.use3dlighting,E.is3d,B.is3d),I.usePattern&&(I.is3d=0),I.bubbleScale=(0,s.pluckNumber)(B.bubblescale,1),I.showTextOutline=(0,s.pluckNumber)(B.textoutline,0),I.minBubbleRadius=(0,s.pluckNumber)(B.minbubbleradius),I.minRadiusForValue=(0,s.pluckNumber)(E.minradiusforvalue,B.minradiusforvalue,0),I.clipBubbles=(0,s.pluckNumber)(B.clipbubbles,1),I.enableAnimation=d=(0,s.pluckNumber)(B.animation,B.defaultanimation,1),I.animation=!!d&&{duration:1e3*(0,s.pluckNumber)(B.animationduration,1)},I.showTooltip=(0,s.pluckNumber)(B.showtooltip,1),I.transposeAnimation=(0,s.pluckNumber)(B.transposeanimation,d),I.transposeAnimDuration=1e3*(0,s.pluckNumber)(B.transposeanimduration,.2),I.seriesNameInTooltip=(0,s.pluckNumber)(B.seriesnameintooltip,1),I.rotateValues=(0,s.pluckNumber)(B.rotatevalues)?270:0,I.showHoverEffect=(0,s.pluckNumber)(B.plothovereffect,B.showhovereffect,o),I.usePattern&&(I.showHoverEffect=0),I.showValues=I.showvalues=(0,s.pluckNumber)(E.showvalues,B.showvalues,0),r=w.components.data=w.components.data||(w.components.data=[]),t=L.length,I.fillColor=I.is3d?(0,s.toRaphaelColor)((0,s.getPointColor)(I.anchorBgColor,I.plotFillAlpha)):(0,s.toRaphaelColor)({color:I.anchorBgColor,alpha:I.plotFillAlpha}),I.strokeColor=(0,s.toRaphaelColor)({color:I.anchorBorderColor,alpha:I.plotFillAlpha}),a=0;a<t;a++)if(p=L[a],!(n=r[a]=r[a]||(r[a]={})).graphics&&(n.graphics={}),(f=n.config={}).x=G.getCleanValue(p.x),f.y=G.getCleanValue(p.y),f.z=G.getCleanValue(p.z,!0),f.setValue={x:f.x,y:f.y,z:f.z},f.patternType=l.PATTERN_TYPES.includes(p.patterntype)?p.patterntype:I.patternType,f.patternAngle=(0,s.pluckNumber)(p.patternangle,E.patternangle,B.patternangle,f.patternType===l.PATTERN_TYPES[0]?40:0),f.patternDensity=(0,s.pluckNumber)(p.patterndensity,I.patternDensity),f.patternSize=(0,s.pluckNumber)(p.patternsize,E.patternsize,f.patternType===l.PATTERN_TYPES[0]?2:4),f.patternAlpha=(0,s.pluckNumber)(p.patternalpha,I.patternAlpha),f.patternBgColor=(0,s.pluck)(p.patternbgcolor,I.patternBgColor),f.dataLabelStyle=w._configureDataLabelStyle(p),f._x=f.x,f._y=f.y,f._z=f.z,f.showValue=(0,s.pluckNumber)(p.showvalue,I.showValues,0),f.plotShowValue=(0,s.pluckNumber)(p.showvalue),f.plotMinRadiusForValue=(0,s.pluckNumber)(p.minradiusforvalue,I.minRadiusForValue),f.anchorProps={},D=f.label=f.x,f.setLink=(0,s.getValidValue)(p.link),I.max=X=C(X,f.z||0),I.min=Z=b(Z,f.z||0),f.is3d=0!==(0,s.pluckNumber)(p.is3d,I.is3d),W=C(W,f.x),Y=b(Y,f.x),U=C(U,f.y),j=b(j,f.y),i=f.color=(0,s.getFirstColor)((0,s.pluck)(p.color,p.z<0?I.negativeColor:I.anchorBgColor)),c=f.alpha=(0,s.pluck)(p.alpha,I.plotFillAlpha),f.colorObj=P=f.is3d?(0,s.getPointColor)(i,c):{color:i,alpha:c},f.setDisplayValue=y=(0,s.parseUnsafeString)((0,s.pluck)(p.displayvalue,p.name,p.label)),u=f.formatedVal=null===f.y?f.y:G.dataLabels(f.y),f.displayValue=(0,s.pluck)(y,f.formatedVal),f.setTooltext=(0,s.getValidValue)((0,s.parseUnsafeString)((0,s.pluck)(p.tooltext,E.plottooltext,B.plottooltext),!1)),I.showTooltip?null===u?h=!1:f.setTooltext!==o?(_=[4,5,6,7,8,9,10,11,12,13,118],m={yDataValue:u,xDataValue:G.xAxis(D),yaxisName:(0,s.parseUnsafeString)(B.yaxisname),xaxisName:(0,s.parseUnsafeString)(B.xaxisname),zDataValue:G.dataLabels(f.z)},h=(0,s.parseTooltext)(f.setTooltext,_,m,p,B,E)):(I.seriesNameInTooltip&&(v=(0,s.getFirstValue)(E&&E.seriesname)),h=v?v+V:s.BLANKSTRING,h+=D?G.xAxis(D)+V:s.BLANKSTRING,h+=u,h+=p.z?V+G.formatValue(p.z):s.BLANKSTRING):h=!1,f.toolText=h,g=f.hoverEffects={},0!==I.showHoverEffect){if(A=g.enabled=(0,s.pluck)(p.hoveralpha,E.hoveralpha,B.bubblehoveralpha,p.hovercolor,E.hovercolor,E.bubblehovercolor,B.bubblehovercolor,p.borderhovercolor,E.borderhovercolor,B.plotborderhovercolor,p.borderhoveralpha,E.borderhoveralpha,B.plotborderhoveralpha,p.hoverscale,E.bubblehoverscale,B.bubblehoverscale,p.borderhovercolor,E.borderhovercolor,B.plotborderhovercolor,p.borderhoverthickness,E.borderhoverthickness,B.plotborderhoverthickness,p.negativehovercolor,E.negativeColor,B.negativecolor,p.is3donhover,B.plotfillhovercolor,E.is3donhover,B.is3donhover,o)!==o,g.negativeColor=(0,s.pluck)(p.negativehovercolor,E.negativehovercolor,B.negativehovercolor,I.negativeColor),g.is3d=(0,s.pluckNumber)(p.is3donhover,E.is3donhover,B.is3donhover,f.is3d),g.color=(0,s.pluck)(p.hovercolor,E.hovercolor,E.bubblehovercolor,B.plotfillhovercolor,B.bubblehovercolor,f.is3d?P.FCcolor.color:i),g.color=g.negativeColor&&p.z<0?g.negativeColor:g.color,g.scale=(0,s.pluck)(p.hoverscale,E.hoverscale,E.bubblehoverscale,B.bubblehoverscale,1),g.color=(0,s.getFirstColor)(g.color),f.hoverColor=g.color,g.alpha=(0,s.pluck)(p.hoveralpha,E.hoveralpha,B.plotfillhoveralpha,B.bubblehoveralpha,c),g.borderColor=(0,s.pluck)(p.borderhovercolor,E.borderhovercolor,B.plotborderhovercolor,I.anchorBorderColor),g.borderAlpha=(0,s.pluck)(p.borderhoveralpha,E.borderhoveralpha,B.plotborderhoveralpha,g.alpha,I.plotBorderAlpha),g.borderThickness=(0,s.pluckNumber)(p.borderhoverthickness,E.borderhoverthickness,B.plotborderhoverthickness,I.anchorBorderThickness),g.color=g.is3d?(0,s.getPointColor)(g.color,g.alpha):{FCcolor:{color:g.color,alpha:g.alpha}},1===(A&&I.showHoverEffect?0:I.showHoverEffect)){for(x=(N=(S="string"==typeof g.color)?g.color.split(/\s{0,},\s{0,}/):g.color.FCcolor.color.split(/\s{0,},\s{0,}/)).length,k=0;k<x;k++)N[k]=(0,s.getLightColor)(N[k],70);S?g.color=N.join(","):g.color.FCcolor.color=N.join(",")}!1===A&&(g.enabled=Boolean(I.showHoverEffect))}else g.enabled=!1;I.xMax=W,I.xMin=Y,I.yMin=j,I.yMax=U,w.setState("dirty",!0),w.setState("visible",1===(0,s.pluckNumber)(E.visible,!Number(E.initiallyhidden),1)),T.config.showLegend&&w._addLegend(),w.setState("dirty",!0)},a._getHoveredPlot=function(e,t){var a=this.config.dataTree.getNeighbour({x:e,y:t},!0,"circle");if(a)return{pointIndex:a.index||a.i,hovered:!0,pointObj:a.data}},a._hoverPlotAnchor=function(e,t,a){var n=this.getFromEnv("animationManager"),o=e.graphics.element,r=t===g?o.data(h):o.data(f);a&&o&&(n.setAnimationState&&n.setAnimationState(t===g?"mouseOut":"mouseOver"),n.setAnimation({el:o,attr:r,component:this}))},a._addLegend=function(){var e,t,a=this,n=a.getFromEnv("chart"),o=a.config,r=a.getFromEnv("chart-attrib"),i=n.getChildren("legend")[0],l=a.config.JSONData||{};o.includeinlegend?(t={enabled:o.includeInLegend,anchorSide:1,type:a.type,label:o.seriesname,usePattern:o.usePattern,legendIconAlpha:(0,s.pluckNumber)(l.legendiconalpha),patternAttr:{patternType:o.patternType,patternAngle:o.patternAngle,patternDensity:o.patternDensity,patternSize:o.patternSize,color:o.anchorBgColor,patternAlpha:o.patternAlpha,patternBgColor:o.patternBgColor,alpha:o.plotFillAlpha}},(e=i.getItem(a.config.legendItemId))?e.configure({style:i.config.itemStyle,hiddenStyle:i.config.itemHiddenStyle,datasetVisible:i.config.datasetVisible,hoverStyle:i.config.itemHoverStyle}):(a.config.legendItemId=i.createItem(a),e=i.getItem(a.config.legendItemId),a.addExtEventListener("fc-click",(function(){e.itemClickFn()}),e)),e.configure(t),e.setStateCosmetics("default",{symbol:{fill:o.fillColor,bgAlpha:(0,s.pluckNumber)(l.legendiconbgalpha,l.legendiconalpha,r.legendiconbgalpha,r.legendiconalpha,o.plotFillAlpha),borderAlpha:(0,s.pluckNumber)(l.legendiconborderalpha,l.legendiconalpha,r.legendiconborderalpha,r.legendiconalpha,"100"),rawFillColor:o.anchorBgColor,rawStrokeColor:o.anchorBorderColor,stroke:o.strokeColor}}),a.getState("visible")?e.removeLegendState("hidden"):e.setLegendState("hidden")):a.config.legendItemId&&i.disposeItem(a.config.legendItemId)},a.getBubbleRadius=function(e){var t,a=v.sqrt,n=this.config,o=n.bubbleScale,r=n.minBubbleRadius,i=this.getFromEnv("chartConfig"),l=b(i.canvasHeight,i.canvasWidth)/8,s=a(this.getLinkedParent().getDataLimitRange().zMax),c=a(e);return t=m(c*l/s)*o||0,r&&(t=C(t,r)),t},a.createCoordinates=function(){var e,t,a,n,r,i,l,s=this,c=s.components,u=c.data,d=s.getFromEnv("yAxis"),p=d.getAxisBase(),f=d.getPixel(p),h=s.getFromEnv("xAxis"),g=h.config.isVertical,v=u.length,m=c.data;for(n=0;n<v;n++)t=(e=m[n])&&e.config,e!==o&&(a=t._b,r=h.getPixel(t._x),i=d.getPixel(t._y),l=a?d.getPixel(a):f,"bubble"===s.getName()&&(t.r=s.getBubbleRadius(t._z),t.showValue=t.plotShowValue!==o?+t.plotShowValue:t.r>=t.plotMinRadiusForValue&&s.config.showValues),g?(t._Px=i,t._Py=r,t._Pby=i,t._Pbx=l):(t._Px=r,t._Py=i,t._Pby=l,t._Pbx=r))},a.parsePlotAttributes=function(e,t){var a,n,r,i,l,c,u,d,f,h,g,v,m,b=this,_=b.config.JSONData,D=b.getFromEnv("chart").config,y=b.config,S=t,k=b.getState("visible"),x=y.anchorBorderThickness;i=e.config,c=(0,s.pluckNumber)(i.x,S),u=i.y,d=i.z,f=i.setLink,h=i.displayValue,l=i.toolText,i.finalTooltext=i.toolText,g=i.hoverEffects,null!==u&&((m=i.eventArgs||(i.eventArgs={})).index=S,m.link=f,m.value=u,m.y=u,m.x=c,m.z=d,m.displayValue=h,m.toolText=l,m.id=b.userID,m.datasetIndex=b.config.index,m.datasetName=_.seriesname,m.visible=k,m.color=i.color,m.alpha=i.alpha,m.is3dOnHover=g.is3d,m.hoverScale=g.scale,m.use3dLighting=i.is3d,m.hoverColor=i.hoverColor,m.hoverAlpha=g.alpha,y.usePattern?(m.pattern={patternType:i.patternType,patternColor:i.color,patternBgColor:i.patternBgColor===s.TRACKER_FILL?o:i.patternBgColor},delete m.color):delete m.pattern,n=i._Py,r=i._Px,a=i.r,[].push({x:r,y:n,r:a}),v=i.setRolloutAttr={fill:(0,s.toRaphaelColor)(i.colorObj),"stroke-width":y.anchorBorderThickness,stroke:(0,s.toRaphaelColor)({color:y.anchorBorderColor,alpha:y.plotBorderAlpha}),r:a},!1!==g.enabled&&(i.setRolloverAttr={fill:(0,s.toRaphaelColor)(g.color),"stroke-width":g.borderThickness,stroke:(0,s.toRaphaelColor)({color:g.borderColor,alpha:g.borderAlpha}),r:a*g.scale}),i.props={element:{attr:{cx:r,cy:k?n:D.canvasBottom+a,r:a||0,fill:(0,s.toRaphaelColor)(i.colorObj),"stroke-width":y.anchorBorderThickness,visibility:k,stroke:v.stroke}}},i.props.element.patternAttr={patternType:i.patternType,patternAngle:i.patternAngle,patternDensity:i.patternDensity,patternSize:i.patternSize,color:i.props.element.attr.fill,patternAlpha:i.patternAlpha,patternBgColor:i.patternBgColor,alpha:i.alpha,x:i.props.element.attr.cx,y:i.props.element.attr.cy},i.trackerConfig||(i.trackerConfig={}),i.trackerConfig.trackerRadius=C(a+(x||0),p),e._xPos=r,e._yPos=n)},a.allocatePosition=function(){var e,t,a,n,o,r,i,l=this,s=l.components.data,u=[];for(l.createCoordinates(),t=0,a=s.length;t<a;t+=1)e=s[t],l.parsePlotAttributes(e,t),l.parseLabelAttributes(e,t),e&&(n=(r=e.config)._Px,o=r._Py,i=r.r||0,u.push({x:n,y:o,index:t,data:e,r:i}));this.config.dataTree=(new c["default"]).buildKdTree(u)},a.getCanvasPadding=function(){var e,t,a,n,o,r,i,l,s,c,u,d,p,f,h,g=this,v=g.config||(g.config={}),m=g.components||{},C=g.getFromEnv("chartConfig"),_=C.rotatevalues,D=g.getFromEnv("xAxis"),y=g.getFromEnv("dataLabelStyle"),S=m.data||[],k=S.length,x=v.leftMostData||S[0],P=v.rightMostData||S[S.length-1],A=b(C.canvasHeight,C.canvasWidth)/8,N=1,w=1,T=C.zMax,F=v.bubbleScale,M=D.config.axisRange,B=M.max,E=M.min,I=D.getPixel(B),L=D.getPixel(E),O={},R={},G=g.getFromEnv("smartLabel"),V={paddingLeft:0,paddingRight:0},z=0;for(e=A/Math.sqrt(T),l=0;l<k;l++)t=S[l].config,a=x.config,n=P.config,r=Math.sqrt(t.z),p=Math.round(r*e)*F||0,f=D.getValue(p)-E,s=t.x-f/2,1===N&&(r=Math.sqrt(a.z),p=Math.round(r*e)*F||0,f=D.getValue(p)-E,c=a.x-f/2),1===w&&(r=Math.sqrt(n.z),p=Math.round(r*e)*F||0,f=D.getValue(p)-E,u=n.x-f/2),N=0,w=0,c>s&&(x=S[l],N=1),u<s&&(P=S[l],w=1);return G.useEllipsesOnOverflow(C.useEllipsesWhenOverflow),G.setStyle(y),x&&x.config.showValue&&(d=(o=x.config).displayValue,R=G.getOriSize(d),z=_?R.height:R.width,L>(i=D.getPixel(o.x)-.5*z)&&(V.paddingLeft=L-i)),P&&P.config.showValue&&(d=(o=P.config).displayValue,O=G.getOriSize(d),z=_?O.height:O.width,I<(h=D.getPixel(o.x)+.5*z)&&(V.paddingRight=h-I)),V},a.drawPlots=function(){var e,t,a,n,o,r,i,l,s,c=this,u=c.getFromEnv("animationManager"),d=c.components.data,p=c.getContainer(),g=c.getState("visible"),v=c.getContainer("labelGroup"),m=function(){!1===g&&(p.plotGroup.hide(),p.commonElemsGroup.hide(),v&&v.hide(),c._containerHidden=!0)},b={};for(o=0,r=d.length;o<r;o+=1)l=(i=(n=d[o]).config).y,e=n.graphics.element,b=i.hoverEffects,a=n.graphics.hotElement,s=n.graphics.label,null!==l?(t=n.graphics.element,(e=u.setAnimation({el:t||"circle",attr:i.props.element.attr,label:"circle",callback:m,component:c,container:p.plotGroup})).addPattern(i.props.element.patternAttr,c.config.usePattern),t||(n.graphics.element=e),e.show(),e.data("hoverEnabled",b.enabled).data(f,i.setRolloverAttr).data(h,i.setRolloutAttr).data("anchorRadius",i.r).data("anchorHoverRadius",i.r),e&&e.data("eventArgs",i&&i.eventArgs)):(e&&e.hide(),a&&a.hide(),s&&s.hide()),e&&(e.isDrawn=!0)},a.getDataLimits=function(){var e=this,t=e.getFromEnv("chart").config,a=e.config,n=a.yMax,o=a.yMin,r=-Infinity,i=+Infinity,l=t.transposeAxis,s=a.xMin,c=a.xMax,u=a.max,d=a.min,p=e.getRegressionPoints();return!1===e.getState("visible")&&l&&(n=r,o=i,s=i,c=r),p&&(n=Math.max(n,p.max),o=Math.min(o,p.min),c=Math.max(c,p.xMax),s=Math.min(s,p.xMin)),{max:n,min:o,xMin:s,xMax:c,zMax:u,zMin:d}},t}(i["default"]),D=_;t["default"]=D},613:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(599)),r={"initial.dataset.column3D":o["default"]["initial.dataset.column"],"legendInteraction.dataset.column3D":o["default"]["legendInteraction.dataset.column"]};t["default"]=r},611:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(595)),i=a(274),l=a(612),s=a(282),c=n(a(613));(0,s.addDep)({name:"column3dAnimation",type:"animationRule",extension:c["default"]});var u=function(e){function t(){var t;return(t=e.call(this)||this).setContainerVisibility=i.stubFN,t}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"dataset"},a.getName=function(){return"column3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this),this.config.use3dlighting=i.UNDEF},a.createContainer=function(){var e=this,t=e.getLinkedParent(),a=e.getFromEnv("paper");!e.getContainer("labelGroup")&&e.addContainer("labelGroup",function(e,t,a){return t.group(e,a)}("label-group",a,t.getChildContainer("vcanvasLabelGroup")).attr("class","fusioncharts-datalabels")).attr("opacity",1)},a._getHoveredPlot=function(e,t){var a,n,o=this,r=o.getFromEnv("chart").isBar;return a=o.getFromEnv("xAxis").getValue(r?t:e),(n=Math.round(a))-a>0?l._checkPointerOverColumn.call(o,n,e,t)||l._checkPointerOverColumn.call(o,n-1,e,t):l._checkPointerOverColumn.call(o,n+1,e,t)||l._checkPointerOverColumn.call(o,n,e,t)},t}(r["default"]);t["default"]=u},982:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o={"initial.dataset.doughnut2D":n(a(975))["default"]["initial.dataset.pie2D"]};t["default"]=o},981:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=t._getInnerSize=void 0;var o=n(a(288)),r=n(a(974)),i=a(282),l=a(274),s=n(a(982)),c=function(){var e,t,a,n,o,r,i,s,c,u=this,d=u.getFromEnv("chart"),p=u.config,f=d.getFromEnv("dataSource").chart,h=p.doughnutradius,g=(0,l.pluckNumber)(f.use3dlighting,1)?(0,l.pluckNumber)(f.radius3d,f["3dradius"],50):100,v=p.pieMinRadius;if(g>100&&(g=100),g<0&&(g=0),e=/%/.test(h)?v*(h=Number(h.split("%")[0])/100):h<=0||h>=v?v/2:(0,l.pluckNumber)(h),p.innerRadius=e,g>0&&l.hasSVG&&(a=(100-(t=parseInt(e/v*100,10)))/2,r=t+","+(n=parseInt(a*g/100,10))+","+2*(a-n)+","+n,u&&(s=u.components.data)))for(i=0,c=s.length;i<c;i+=1)(o=s[i].config).color&&(o.color.ratio=r,o.hoverEffects&&o.hoverEffects.color&&(o.hoverEffects.color.ratio=r));return 2*e};t._getInnerSize=c,(0,i.addDep)({name:"doughnut2dAnimation",type:"animationRule",extension:s["default"]});var u=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"dataset"},a.getName=function(){return"doughnut2D"},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t);var a=this.config,n=this.getFromEnv("chartConfig");a.doughnutradius=(0,l.pluck)(n.doughnutradius,a.doughnutradius,"50%")},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this),this.config.doughnutradius="50%"},a._parsePiePlotOptions=function(){var t=e.prototype._parsePiePlotOptions.call(this);return t.innerSize=this._getInnerSize(),t},a._getInnerSize=function(){return c.call(this)},a.allocatePosition=function(){this.config.innerSize=this._getInnerSize(),e.prototype.allocatePosition.call(this)},t}(r["default"]),d=u;t["default"]=d},990:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o={"initial.dataset.doughnut3D":n(a(983))["default"]["initial.dataset.pie3d"]};t["default"]=o},989:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(980)),i=n(a(990)),l=a(282),s=a(274);(0,l.addDep)({name:"doughnut3dAnimation",type:"animationRule",extension:i["default"]});var c=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"dataset"},a.getName=function(){return"doughnut3D"},a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this),this.config.doughnutradius="50%"},a._configurePie3DManager=function(){var e=this,t=e.config,a=e.components,n=e.getFromEnv("pie3DManager"),o=a.data;n&&n.configure(t.pieSliceDepth,1===o.length,t.use3DLighting,!0)},a.configureAttributes=function(t){e.prototype.configureAttributes.call(this,t);var a=this.config,n=this.getFromEnv("chartConfig");a.doughnutradius=(0,s.pluck)(n.doughnutradius,a.doughnutradius,"50%")},t}(r["default"]),u=c;t["default"]=u},1060:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o,r=n(a(288)),i=a(290),l=a(274),s=Math,c=s.min,u=s.max,d=s.abs,p=function(e){function t(){var t;return(t=e.call(this)||this).setState("visible",!0),t}(0,r["default"])(t,e);var a=t.prototype;return a.getType=function(){return"group"},t.getName=function(){return"barMultiSeriesgroup"},a.getName=function(){return"barMultiSeriesgroup"},a.preConfigure=function(t){if(!t)return!1;this.config.JSONData=t,e.prototype.preConfigure.call(this,t)},a.configure=function(t){if(!t)return!1;e.prototype.configure.call(this,t)},a.setVisibility=function(){var e=this,t=0;e._mapChildren((function(e){e.setVisibility&&e.setVisibility()})),e._mapChildren((function(e){e.getState("visible")&&t++})),e.setState("visible",!!t)},a.createContainer=function(){var e,t,a=this,n=a.getFromEnv("animationManager"),o=a.getLinkedParent().getChildContainer();for(e in o)t=o[e],!a.getChildContainer(e)&&a.addChildContainer(e,n.setAnimation({el:"group",attr:{name:"manager-"+e},component:a,container:t}))},a.allocatePosition=function(){this.setBarPosition()},a.draw=function(){this.createContainer()},a.getCanvasPadding=function(){var e,t,a={paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0};return this.setBarPosition(),this._mapChildren((function(n){for(t in e=!n.getState("removed")&&n.getState("visible")&&n.getCanvasPadding&&n.getCanvasPadding()||{})e.hasOwnProperty(t)&&(a[t]=Math.max(e[t],a[t]))})),a},a.setBarPosition=function(){var e,t,a,n,r,i=this,s=i.getFromEnv("chartConfig"),p=i.getFromEnv("chart-attrib"),f=i.getFromEnv("xAxis"),h=i.getFromEnv("numOfBars"),g=s.plotSpacePercent,v=u((0,l.pluckNumber)(g,20)%100,0),m=v/200,b=f.getPixel(0),C=f.getPixel(1),_=i.getFromEnv("chart").isBar?s.maxBarHeight:s.maxColWidth,D=d(C-b),y=s.plotPaddingPercent,S=!0,k=s.overlapBars,x=0,P=0,A=0;i.addToEnv("groupMaxWidth",D),i._mapChildren((function(e){e.getState("removed")||!1===e.getState("visible")||(P++,e.addToEnv("updatedIndex",A++))})),i.addToEnv("numOfBars",P),i.addToEnv("numColDiff",(0,l.pluckNumber)(h-P,0)),e=(1-.01*g)*D||c(D*(1-2*m),_*(P||1)),p.plotspacepercent===o&&e>=s.canvasWidth/2&&(e=D-_/2),n=c((t=0===P?e:e/P)-1,P>1?k||y!==o?y>0?t*y/100:0:4:0),i.addToEnv("oldPlotWidth",i.getFromEnv("plotWidth")),i.addToEnv("plotWidth",t-n),i.addToEnv("oldPlotPadding",i.getFromEnv("plotPadding")),i.addToEnv("plotPadding",n),a=-(P/2*t-t/2),i.addToEnv("oldGroupNetWidth",i.getFromEnv("groupNetWidth")),i.addToEnv("groupNetWidth",e),i._mapChildren((function(e){!e.getState("removed")&&e.getState("visible")&&(e.addToEnv("shift",a+x*t+n/2),x++)})),C-(r=e/2)-(b-r+e)<4&&(S=!1),0===v&&(S=!0),i.addToEnv("isCrisp",S)},a.childChanged=function(e){void 0===e&&(e={});var t,a,n=this,o=n.config,r=n.getLinkedParent(),i=0,l=n.getState("visible"),s={};n._mapChildren((function(e){e.getState("visible")&&i++})),n.setState("visible",!!i),l!==!!i&&(a=!0),!1!==e.dataLimitChanged&&((t=n.getDataLimits()).min===o.range.min&&t.max===o.range.max||(o.range.min=t.min,o.range.max=t.max,s.dataLimitChanged=!0,a=!0)),a?r.childChanged&&r.childChanged(s):n.asyncDraw()},a.getAxisValuePadding=function(){var e={},t=-Infinity,a=-Infinity;return this._mapChildren((function(n){n.getState("removed")||(e=n.getAxisValuePadding&&n.getAxisValuePadding()||{},t=Math.max(t,e.left||-Infinity),a=Math.max(a,e.right||-Infinity))})),t===-Infinity&&(t=0),a===-Infinity&&(a=0),this.config.padding||(this.config.padding={},this.config.padding.left=t,this.config.padding.right=a),{left:t,right:a}},a.getDataLimits=function(e){var t,a=this,n=+Infinity,o=-Infinity,r=0,i=function(e){o=Math.max(o,e.max),n=Math.min(n,e.min)};return a._mapChildren((function(a){a.getState("removed")||(!1!==a.getState("visible")?(r++,t=a.getDataLimits(e),i(t)):e&&(t=a.getDataLimits(e),i(t)))})),r?a.setState("visible",!0):a.setState("visible",!1),this.config.range||(this.config.range={},this.config.range.min=this.config.dataMin,this.config.range.max=this.config.dataMax),{max:o,min:n}},a.isVisible=function(){return!this.isNotVisible},t}(i.ComponentInterface);t["default"]=p},1053:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=a(290),i=a(274),l=function(e){function t(){var t;return(t=e.call(this)||this).setState("visible",!0),t}(0,o["default"])(t,e);var a=t.prototype;return a.createContainer=function(){var e,t,a=this,n=a.getFromEnv("animationManager"),o=a.getLinkedParent().getChildContainer();for(e in o)t=o[e],!a.getChildContainer(e)&&a.addChildContainer(e,n.setAnimation({el:"group",attr:{name:"manager-"+e},component:a,container:t}))},a.draw=function(){this.createContainer()},a.getDataLimitRange=function(){var e,t,a,n,o,r=this.getChildren(),i=-Infinity,l=+Infinity,s=-Infinity,c=+Infinity;for(t in r)if(r.hasOwnProperty(t)&&(a=r[t])instanceof Array)for(n=a.length,e=0;e<n;e++)a[e].getState("removed")||(o=a[e].getDataLimits(),s=Math.max(s,o.xMax||-Infinity),c=Math.min(c,o.xMin||+Infinity),i=Math.max(i,o.zMax||-Infinity),l=Math.min(l,o.zMin||+Infinity));return{xMax:s,xMin:c,zMax:i=i===-Infinity?0:i,zMin:l=l===+Infinity?0:l}},a.childChanged=function(e){void 0===e&&(e={});var t,a,n,o=this,r=o.config,i=o.getLinkedParent(),l={};!1===e.hide&&!1===e.show||(o._mapChildren((function(e){e.setState("dirty",!0)})),n=!0),!1!==e.dataLimitChanged&&((t=o.getDataLimits()).min===r.range.min&&t.max===r.range.max||(r.range.min=t.min,r.range.max=t.max,l.dataLimitChanged=!0,n=!0)),!1!==e.paddingChanged&&((a=o.getAxisValuePadding()).left===r.padding.left&&a.right===r.padding.right||(r.padding.left=a.left,r.padding.right=a.right,l.paddingChanged=!0,n=!0)),n?i.childChanged&&i.childChanged(l):o.asyncDraw()},a.getAxisValuePadding=function(){var e={},t=-Infinity,a=-Infinity;return this._mapChildren((function(n){n.getState("removed")||!1===n.getState("visible")||(e=n.getAxisValuePadding&&n.getAxisValuePadding()||{},t=Math.max(t,e.left||-Infinity),a=Math.max(a,e.right||-Infinity))})),t===-Infinity&&(t=0),a===-Infinity&&(a=0),this.config.padding||(this.config.padding={},this.config.padding.left=t,this.config.padding.right=a),{left:t,right:a}},a.getCanvasPadding=function(){var e,t,a={paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0};return this._mapChildren((function(n){if(!n.getState("removed"))for(t in e=n.getCanvasPadding&&n.getCanvasPadding()||{})e.hasOwnProperty(t)&&(a[t]=Math.max(e[t],a[t]))})),a},a.getDataLimits=function(){var e,t,a,n=this.getFromEnv("chart"),o=-Infinity,r=+Infinity,l=o,s=r,c=r,u=o;return this._mapChildren((function(n){var d;n.getDataLimits&&!n.getState("removed")&&(e=n.getDataLimits(),d=e,t=(0,i.pluck)(d.xMax,o),a=(0,i.pluck)(d.xMin,r),l=Math.max(l,d.max),s=Math.min(s,d.min),u=Math.max(u,t),c=Math.min(c,a))})),l===-Infinity&&(l=0),s===+Infinity&&(s=0),this.config.range||(this.config.range={},this.config.range.min=s,this.config.range.max=l,this.config.range.xMin=c,this.config.range.xMax=u),n.config.yMax=l,n.config.yMin=s,{min:s,max:l,xMin:c,xMax:u}},a.isVisible=function(){return!this.isNotVisible},a.getType=function(){return"manager"},a.getName=function(){return"BubbleGroupManager"},a.remove=function(){this._mapChildren((function(e){e.getState("removed")||e.remove()})),e.prototype.remove.call(this)},t}(r.ComponentInterface);t["default"]=l},610:(e,t)=>{t.__esModule=!0,t["default"]=void 0;t["default"]={"initial.group.column3d":function(){return{"zeroPlane.appearing":function(){return[{initialAttr:{opacity:0},finalAttr:{opacity:1},slot:"axis"}]}}}}},609:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=a(290),i=a(282),l=n(a(610));(0,i.addDep)({name:"column3dManagerAnimation",type:"animationRule",extension:l["default"]});var s=function(e){function t(){var t;return(t=e.call(this)||this).setState("visible",!0),t}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"group"},a.getName=function(){return"column3d"},a.getCanvasPadding=function(){var e,t,a={paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0};return this._mapChildren((function(n){for(t in e=n.getCanvasPadding&&n.getCanvasPadding()||{})e.hasOwnProperty(t)&&(a[t]=Math.max(e[t],a[t]))})),a},a.createContainer=function(){var e,t,a=this,n=a.getFromEnv("animationManager"),o=a.getLinkedParent().getChildContainer();for(e in o)t=o[e],!a.getChildContainer(e)&&a.addChildContainer(e,n.setAnimation({el:"group",attr:{name:"manager"+e},container:t,component:a,label:"group"}))},a.draw3DContainer=function(){var e,t=this,a=t.getFromEnv("animationManager"),n=t.getChildContainer("plotGroup3d"),o=t.getFromEnv("xAxis").getTicksLen(),r=n&&n.negative,i=n&&n.positive,l=this.getLinkedParent().getChildContainer().columnVcanvasGroup;for(!t.getChildContainer("plotGroup3d")&&t.addChildContainer("plotGroup3d",a.setAnimation({el:"group",attr:{name:"3d-plots"},container:l,component:t,label:"group"})),n=t.getChildContainer("plotGroup3d"),t.addToEnv("plotGroup3d",n),r=n.negative=a.setAnimation({el:r||"group",attr:{name:"negative-values"},container:n,component:t,label:"group"}),i=n.positive=a.setAnimation({el:i||"group",attr:{name:"positive-values"},container:n,component:t,label:"group"}),n.zeroPlane=a.setAnimation({el:n.zeroPlane||"group",attr:{name:"zero-plane"},container:n,component:t,label:"group"}).insertBefore(n.positive),(n.negativeGroupArray=n.negativeGroupArray=r.data("categoryplots"))||(r.data("categoryplots",new Array(o)),n.negativeGroupArray=r.data("categoryplots")),(n.positiveGroupAarray=n.positiveGroupAarray=i.data("categoryplots"))||(i.data("categoryplots",new Array(o)),n.positiveGroupAarray=i.data("categoryplots")),e=0;e<o;e++)n.negativeGroupArray[e]=a.setAnimation({el:n.negativeGroupArray[e]||"group",attr:{name:"negative-group-"+e},container:r,component:t,label:"group"}),n.positiveGroupAarray[e]=a.setAnimation({el:n.positiveGroupAarray[e]||"group",attr:{name:"positive-group-"+e},container:i,component:t,label:"group"})},a.drawZeroPlane=function(){var e,t,a=this,n=a.getFromEnv("chart"),o=n.getFromEnv("animationManager"),r=n.isBar,i=n.config,l=i.use3dlighting,s=a.getChildContainer("plotGroup3d"),c=a.getFromEnv("yAxis"),u=c.getLimit(),d=u.max,p=u.min,f=a.getGraphicalElement("zeroplane"),h={},g=i.xDepth,v=i.yDepth,m=c.getPixel(c.getAxisBase());p<0&&d>=0?(!a.graphics&&(a.graphics={}),t=s.zeroPlane,h.fill=i.zeroPlaneColor,h.noGradient=!l,h.stroke=i.zeroPlaneBorderColor||"none",h["stroke-width"]=i.zeroPlaneShowBorder?1:0,h.x=r?m-g:i.canvasLeft-g,h.y=r?i.canvasTop+v:m+v,h.width=r?1:i.canvasWidth,h.height=r?i.canvasHeight:1,h.xDepth=g,h.yDepth=v,f&&(f.show(),f._.cubetop.show(),f._.cubeside.show()),e={el:f||"cubepath",attr:h,container:t,component:a,label:"zeroPlane"}):f&&(e={el:f,attr:r?{x:m-v}:{y:m+v},component:a,doNotRemove:!0,callback:function(){f.hide(),f._.cubetop.hide(),f._.cubeside.hide()},container:s,label:"zeroPlane"}),e&&a.addGraphicalElement("zeroplane",o.setAnimation(e))},a.draw=function(){this.createContainer(),this.draw3DContainer(),this.drawZeroPlane()},a.childChanged=function(e){void 0===e&&(e={});var t,a,n=this,o=n.config,r=n.getLinkedParent(),i=0,l=n.getState("visible"),s={};n._mapChildren((function(e){e.getState("visible")&&i++})),n.setState("visible",!!i),l!==!!i&&(a=!0),!1!==e.dataLimitChanged&&((t=n.getDataLimits()).min===o.range.min&&t.max===o.range.max||(o.range.min=t.min,o.range.max=t.max,s.dataLimitChanged=!0,a=!0)),a?r.childChanged&&r.childChanged(s):n.asyncDraw()},a.getAxisValuePadding=function(){var e={},t=-Infinity,a=-Infinity;return this._mapChildren((function(n){n.getState("removed")||(e=n.getAxisValuePadding&&n.getAxisValuePadding()||{},t=Math.max(t,e.left||-Infinity),a=Math.max(a,e.right||-Infinity))})),t===-Infinity&&(t=0),a===-Infinity&&(a=0),this.config.padding||(this.config.padding={},this.config.padding.left=t,this.config.padding.right=a),{left:t,right:a}},a.getDataLimits=function(e){var t,a=this,n=+Infinity,o=-Infinity,r=0,i=function(e){o=Math.max(o,e.max),n=Math.min(n,e.min)};return a._mapChildren((function(a){a.getState("removed")||(!1!==a.getState("visible")?(r++,t=a.getDataLimits(e),i(t)):e&&(t=a.getDataLimits(e),i(t)))})),r?a.setState("visible",!0):a.setState("visible",!1),this.config.range||(this.config.range={},this.config.range.min=this.config.dataMin,this.config.range.max=this.config.dataMax),{max:o,min:n}},a.isVisible=function(){return!this.isNotVisible},t}(r.ComponentInterface);t["default"]=s},1102:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(1023)),i=a(274),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"group"},a.getName=function(){return"marimekkoStackgroup"},a.getstackConf=function(){return this.config.stackConf},a._setStackPosition=function(){e.prototype._setStackPosition.call(this);var t,a,n,o,r,i,l=this,s=l.config,c=s.stackConf||(s.stackConf=[]),u=s.stackValues,d=l.getFromEnv("categories")[0].category,p=l.getFromEnv("number-formatter"),f=0,h=0,g=0,v=l.getFromEnv("xAxis"),m=v.getVisibleConfig(),b=m.minValue,C=m.maxValue-b,_=b;for(o=0,r=u.length;o<r;o++)h+=u[o]&&u[o].positive||0;for(s.totalSumValue=h,o=0;o<d.length;o++)(a=d[o]).widthpercent&&(f+=p.getCleanValue(a.widthpercent));for(100==+f.toFixed(8)&&(f=+f.toFixed(8),s.setUserWidth=1),t=l.getStackSumPercent(),o=0,r=u.length;o<r;o++)(n=c[o])||(n=c[o]={}),g+=t[o]/100,i=t[o]/100*C/2+_,_=g*C+b,n.x=i,v.updateTicksValues(o,{x:i})},a.getStackSumPercent=function(){var e,t=this,a=t.config,n=a.stackValues,o=a.totalSumValue,r=t.getFromEnv("number-formatter"),i=t.getFromEnv("categories")[0].category,l=a.setUserWidth,s=[];for(e=0;e<n.length;e++)s[e]=l?r.getCleanValue(i[e].widthpercent):(n[e]&&n[e].positive||0)/o*100;return s},a.draw=function(){e.prototype.draw.call(this),this.drawLabel()},a.createContainer=function(){e.prototype.createContainer.call(this);var t=this,a=t.getLinkedParent();!t.getContainer("commonLabelContainer")&&t.addContainer("commonLabelContainer",function(e,t,a){return a.getFromEnv("animationManager").setAnimation({el:"group",attr:{name:e},container:t,component:a,label:"group"})}("manager-commonLabelContainer",a.getChildContainer("vcanvasLabelGroup"),t))},a.drawLabel=function(){var e,t,a,n,o,r,l,s,c,u,d,p,f,h,g,v=this,m=v.config,b=v.getFromEnv("smartLabel"),C=v.getFromEnv("animationManager"),_=v.getStackSumPercent(),D=v.getFromEnv("chart-attrib"),y=v.getChildren("dataset"),S=m.stackConf,k=v.getContainer("commonLabelContainer"),x=v.getFromEnv("number-formatter"),P=0,A=v.getFromEnv("chartConfig"),N=A.canvasBottom,w=v.getFromEnv("xAxis"),T=v.getFromEnv("style"),F=A.dataLabelStyle,M=_.length,B=m.stackValues,E=A.showXAxisPercentValues,I=v.getGraphicalElement("commonLabels")||[],L=I.length;if(b.setStyle(F),E){for(p=(0,i.pluck)(F.backgroundColor,"#ffffff"),d=(0,i.pluck)(F.borderColor===i.BLANKSTRING?"#"+T.inCancolor:F.borderColor,"#000000"),f=(0,i.pluck)(F.borderThickness,1),r=0;r<M-1;r++)B[r]&&(P+=_[r],l=x.percentValue(P),n=w.getPixel(S[r].x)+S[r].columnWidth/2,o=N,u=I[r],B[r].positive!==B[r].negative?(s={text:l,fill:F.color,"text-bound":[p,d,f,F.borderPadding,F.borderRadius,F.borderDash],"line-height":F.lineHeight,visibility:i.visibleStr},o=o-b.getOriSize(l).height/2-f,s.x=n,s.y=o,u&&u.show(),c=C.setAnimation({el:u||"text",container:k,attr:s,label:"text",component:v}),u||v.addGraphicalElement("commonLabels",c,!0)):(l=i.BLANKSTRING,u&&u.hide()));for(var O=r;O<L;O++)I[O].hide()}for(h=0;h<y.length;h++)for(a=(t=y[h]).components.data,e=t.config.JSONData,g=0;g<a.length;g++)a[g].config.finalTooltext=(0,i.parseTooltext)(a[g].config.finalTooltext,[111],{xAxisPercentValue:_[g]&&_[g].toPrecision(4)+"%"},a,D,e)},a._getXpos=function(e){return this.getFromEnv("xAxis").getPixel(this.config.stackConf[e].x)},a._setStackDimensions=function(){var e,t,a,n,o,r,i=this,l=i.config,s=l.stackValues,c=i.getFromEnv("categories")[0].category,u=0,d=i.getFromEnv("number-formatter"),p=i.getStackSumPercent(),f=i.getFromEnv("canvasConfig").canvasWidth,h=l.stackConf||(l.stackConf=[]);for(e=0;e<c.length;e++)(t=c[e]).widthpercent&&(u+=d.getCleanValue(t.widthpercent));for(100==+u.toFixed(8)&&(a=l.setUserWidth=1,u=+u.toFixed(8)),e=0,n=s.length;e<n;e++)t=c[e],(o=h[e])||(o=h[e]={}),r=a?d.getCleanValue(t.widthpercent)/100:p[e]/100,o.columnWidth=r*f},t}(r["default"]),s=l;t["default"]=s},1101:(e,t,a)=>{t.__esModule=!0,t["default"]=void 0;var n=a(274),o={"initial.dataset.marimekko":function(){var e=this,t=e.getFromEnv("chart"),a=t.config.yDepth||0,o="y",r="height",i=e.getFromEnv("yAxis");return{"rect.appearing":function(l){var s,c,u,d,p=i.getPixel(i.getAxisBase())+(t.isBar?-a:a),f=l.attr;return s=f[o],c=f[r],d=Math.sign(s+c/2-p),u=s+c,[{initialAttr:function(){var e={};return e[o]=u,e[r]=0,e},slot:"plot",startEnd:function(){return n.animHelperFN.getTimeByValue({start:0,end:.6},{startPx:p,endPx:1===d?e.config.yAxisMaxPixel:e.config.yAxisMinPixel},{startPx:u,endPx:1===d?f[o]+f[r]:f[o]})},effect:"linear"}]},"group.appearing":null,"group.updating":null,"plotLabel.appearing":[{initialAttr:{opacity:0},slot:"final"}],"*":null}}};t["default"]=o},1100:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(288)),r=n(a(595)),i=a(274),l=n(a(1101)),s=a(282),c=i.regex.dropHypeash,u=Math,d=u.round,p=u.abs;(0,s.addDep)({name:"marimekkoAnimation",type:"animationRule",extension:l["default"]});var f=function(e){function t(){var t;return(t=e.call(this)||this).config.groupName="column",t}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"dataset"},a.getName=function(){return"marimekko"},a.configure=function(t){(0,i.fcEach)(t.data,(function(e){e&&(e.value=+e.value?p(e.value):"string"==typeof e.value&&"-"===e.value.charAt(0)?e.value.substring(1):e.value,e.value&&""!==e.value||(e.value=0))})),e.prototype.configure.call(this,t)},a._addLegend=function(){var e,t,a,n,o=this,r=o.getFromEnv("chart-attrib"),l=o.config,s=o.getFromEnv("legend"),u=(0,i.pluckNumber)(o.getFromEnv("chart-attrib").useplotgradientcolor,1),d=l.legendSymbolColor,p=o.config.JSONData||{};e=(0,i.getLightColor)(d,60).replace(c,i.HASHSTRING),t=u?{FCcolor:{color:d+","+d+","+(0,i.getLightColor)(d,40)+","+d+","+d,ratio:"0,70,30",angle:270,alpha:"100,100,100,100,100"}}:{FCcolor:{color:d,angle:0,ratio:"0",alpha:"100"}},a={enabled:l.includeInLegend,type:o.type,label:(0,i.getFirstValue)(o.config.JSONData.seriesname),usePattern:l.usePattern,legendIconAlpha:(0,i.pluckNumber)(p.legendiconalpha),patternAttr:{patternType:l.patternType,patternAngle:l.patternAngle,patternDensity:l.patternDensity,patternSize:l.patternSize,color:l.plotColor,patternAlpha:l.patternAlpha,patternBgColor:l.patternBgColor,alpha:l.plotfillalpha}},l.includeinlegend?((n=s.getItem(o.config.legendItemId))||(o.config.legendItemId=s.createItem(o),n=s.getItem(o.config.legendItemId),o.addExtEventListener("fc-click",(function(){n.itemClickFn()}),n)),n.configure(a),n.setStateCosmetics("default",{symbol:{fill:(0,i.toRaphaelColor)(t),bgAlpha:(0,i.pluckNumber)(p.legendiconbgalpha,p.legendiconalpha,r.legendiconbgalpha,r.legendiconalpha,l.plotfillalpha),borderAlpha:(0,i.pluckNumber)(p.legendiconborderalpha,p.legendiconalpha,r.legendiconbordergalpha,r.legendiconalpha,"100"),rawFillColor:t.FCcolor.color,stroke:(0,i.toRaphaelColor)(e)}}),o.getState("visible")?n.removeLegendState("hidden"):n.setLegendState("hidden")):o.config.legendItemId&&s.disposeItem(o.config.legendItemId)},a.searchIndex=function(e,t){for(var a,n,o=this.getFromEnv("xAxis"),r=0,i=t.length-1;r<=i;)if(a=Math.round((r+i)/2)||0,(n=o.getPixel(t[a].x)+t[a].columnWidth/2)<e)r=a+1;else{if(!(n>e))return a;i=a-1}return r},a.allocatePosition=function(){this.getLinkedParent()._setStackDimensions(),e.prototype.allocatePosition.call(this)},a._getHoveredPlot=function(e,t){var a,n,o,r,i=this,l=i.getLinkedParent(),s=l.getstackConf(),c=i.getFromEnv("chartConfig"),u=l.config,d=c.plotborderthickness,p=c.showplotborder,f=s.length-1;return n=e+(a=(a=(d=p?d:0)/2)%2==0?a+1:Math.round(a)),r=o&&u.datasetIndex||i.searchIndex(n,s),u.datasetIndex||(u.datasetIndex=r),((o=i._checkPointerOverColumn(r,e,t))||i.index===f)&&delete u.datasetIndex,o},a.setColumnPosition=function(){return this},a.fineTunePlotDimension=function(e,t,a,n,o){var r=a,i=n,l=e,s=t,c=this.getLinkedParent().getstackConf(),u=this.getFromEnv("chart"),p=u.config.plotborderthickness,f=u.getChildren("canvas")[0].config,h=f.canvasBorderWidth>0,g=f.canvasRight,v=f.canvasTop,m=f.canvasLeft;return c.length?(l-=(i=c[o].columnWidth)/2,parseInt(s,10)<=v&&(r-=v-s-+h,s=v-+h),p<=1&&(d(l)<=m&&(i+=l,i-=l=m-p/2+ +!!p-+h),d(l+i)>=g&&(i=g-l+p/2-+!!p+ +h)),{xPos:l,yPos:s,width:i,height:r}):{xPos:l,yPos:s,width:i,height:r}},t}(r["default"]);t["default"]=f},998:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o={"initial.dataset.paretoColumn":n(a(599))["default"]["initial.dataset.column"]};t["default"]=o},997:(e,t,a)=>{var n=a(269);t.__esModule=!0,t.ParetoColumnDataset=void 0;var o=n(a(288)),r=a(274),i=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{"default":e};var a=c(t);if(a&&a.has(e))return a.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in e)if("default"!==r&&Object.prototype.hasOwnProperty.call(e,r)){var i=o?Object.getOwnPropertyDescriptor(e,r):null;i&&(i.get||i.set)?Object.defineProperty(n,r,i):n[r]=e[r]}n["default"]=e,a&&a.set(e,n);return n}(a(595)),l=a(282),s=n(a(998));function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(c=function(e){return e?a:t})(e)}var u,d=Math,p=d.min,f=d.max,h=d.abs;(0,l.addDep)({name:"paretoAnimation",type:"animationRule",extension:s["default"]});var g=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a.getType=function(){return"dataset"},a.getName=function(){return"paretoColumn"},a.configureAttributes=function(e){if(!e)return!1;this.trimData(e),this.config.JSONData=e;var t,a,n,o,l,s,c,d,g,v,m,b,C,_,D,y,S,k,x,P,A,N,w,T,F,M,B,E,I,L,O,R,G,V,z,H,W,Y,U,j,X,Z,J,q,K,$,Q,ee,te=this,ae=te.getFromEnv("chart"),ne=te.config,oe=te.getFromEnv("xAxis"),re=ne.JSONData,ie=re.data,le=ie&&ie.length,se=te.getFromEnv("chart-attrib"),ce=te.getFromEnv("color-manager"),ue=te.index||te.positionIndex,de=ce.getPlotColor(ue),pe=(0,r.pluckNumber)(re.dashed,se.plotborderdashed),fe=ae.config,he=fe.useplotgradientcolor,ge=(0,r.pluckNumber)(se.showtooltip,1),ve=(0,r.parseUnsafeString)(se.yaxisname),me=(0,r.parseUnsafeString)(se.xaxisname),be=te.components.data,Ce=te.getFromEnv("number-formatter"),_e=fe.is3D,De=-Infinity,ye=+Infinity,Se=0,ke=[],xe=0,Pe=ne.tootipSepChar=(0,r.pluck)(se.tooltipsepchar,", ");for(ne.minAbsNonZeroValue=Infinity,ne.minAbsNonZeroData={},ne.defaultPadding={left:.5,right:.5},ne.enableAnimation=j=(0,r.pluckNumber)(se.animation,se.defaultanimation,1),ne.animation=!!j&&{duration:1e3*(0,r.pluckNumber)(se.animationduration,1)},ne.showTooltip=(0,r.pluckNumber)(se.showtooltip,1),ne.showTextOutline=(0,r.pluckNumber)(se.textoutline,0),ne.valuePadding=(0,r.pluckNumber)(se.valuepadding,2),ne.rotateValues=(0,r.pluckNumber)(se.rotatevalues)?270:0,ne.usePattern=(0,r.pluckNumber)(fe.usePattern,0),ne.patternType=i.PATTERN_TYPES.includes(re.patterntype)?re.patterntype:fe.patternType,ne.patternDensity=(0,r.pluckNumber)(re.patterndensity,fe.patternDensity),ne.patternAlpha=(0,r.pluckNumber)(re.patternalpha,fe.patternAlpha),ne.patternBgColor=(0,r.pluck)(re.patternbgcolor,ne.patternBgColor),ne.showHoverEffect=b=(0,r.pluckNumber)(se.plothovereffect,se.showhovereffect,u),ne.usePattern&&(ne.showHoverEffect=b=0),ne.showShadow=m||_e?(0,r.pluckNumber)(se.showshadow,1):(0,r.pluckNumber)(se.showshadow,ce.getColor("showShadow")),ne.useDataPlotColorForLabels=ee=(0,r.pluckNumber)(se.usedataplotcolorforlabels,0),ne.use3dlineshift=(0,r.pluckNumber)(se.use3dlineshift,ae.use3dlineshift),t=ne.showplotborder=(0,r.pluckNumber)(se.showplotborder,_e?0:1),ne.plotDashLen=d=(0,r.pluckNumber)(se.plotborderdashlen,5),ne.plotDashGap=g=(0,r.pluckNumber)(se.plotborderdashgap,4),ne.plotfillangle=C=(0,r.pluckNumber)(360-se.plotfillangle,90),ne.plotfillalpha=_=(0,r.pluck)(se.plotfillalpha,"100"),ne.plotColor=de,ne.isRoundEdges=m=(0,r.pluckNumber)(se.useroundedges,0),ne.plotRadius=(0,r.pluckNumber)(se.useRoundEdges,ne.isRoundEdges?1:0),ne.plotfillratio=D=(0,r.pluck)(se.plotfillratio),ne.plotgradientcolor=y=(0,r.getDefinedColor)(se.plotgradientcolor,ce.getColor("plotGradientColor")),!he&&(y=""),ne.plotborderalpha=S=t&&!_e?(0,r.pluck)(se.plotborderalpha,_,"100"):0,ne.plotbordercolor=k=(0,r.pluck)(se.plotbordercolor,_e?"#ffffff":ce.getColor("plotBorderColor")),ne.plotborderthickness=v=(0,r.pluckNumber)(se.plotborderthickness,1),ne.plotBorderDashStyle=P=pe?(0,r.getDashStyle)(d,g):"none",ne.showValues=(0,r.pluckNumber)(se.showvalues,1),ne.definedGroupPadding=f((0,r.pluckNumber)(se.plotspacepercent),0),ne.plotSpacePercent=f((0,r.pluckNumber)(se.plotspacepercent,20)%100,0),ne.maxcolwidth=(0,r.pluckNumber)(se.maxcolwidth,50),ne.plotpaddingpercent=(0,r.pluckNumber)(se.plotpaddingpercent),ne.placevaluesinside=(0,r.pluckNumber)(se.placevaluesinside,0),ne.use3dlighting=(0,r.pluckNumber)(se.use3dlighting,1),ne.parentYAxis=0,te.setState("visible",1===(0,r.pluckNumber)(re.visible,1)),te.setState("dirty",!0),be||(be=te.components.data=[]),q=0;q<le;q++)Se+=$=h(Ce.getCleanValue(ie[q].value)),ke[q]=(0,r.extend2)({},ie[q]),ke[q].value=$;for(ke.sort((function(e,t){return t.value-e.value})),ne.imageCount=0,q=0;q<le;q++)A=ke[q],(w=be[q])||(w=be[q]={graphics:{}}),w.config||(T=be[q].config={}),null!==(N=h(Ce.getCleanValue(A.value)))&&(T=w&&w.config,Q=oe.getLabel(q),T.label=(0,r.getValidValue)((0,r.parseUnsafeString)((0,r.pluck)(Q.label))),T.showValue=(0,r.pluckNumber)(A.showvalue,ne.showValues),T.setValue=N,T.setLink=(0,r.pluck)(A.link),T.setDisplayValue=M=(0,r.parseUnsafeString)(A.displayvalue),xe+=T.setValue,K=Ce.dataLabels(N),T.id=A.id?(0,r.getValidValue)((0,r.parseUnsafeString)((0,r.pluck)(A.id))):T.label,T.valuePadding=(0,r.pluckNumber)(A.valuepadding,re.valuepadding,fe.valuepadding),T.patternType=i.PATTERN_TYPES.includes(A.patterntype)?A.patterntype:ne.patternType,T.patternAngle=(0,r.pluckNumber)(A.patternangle,re.patternangle,se.patternangle,T.patternType===i.PATTERN_TYPES[0]?40:0),T.patternDensity=(0,r.pluckNumber)(A.patterndensity,ne.patternDensity),T.patternSize=(0,r.pluckNumber)(A.patternsize,re.patternsize,T.patternType===i.PATTERN_TYPES[0]?2:4),T.patternAlpha=(0,r.pluckNumber)(A.patternalpha,ne.patternAlpha),T.patternBgColor=(0,r.pluck)(A.patternbgcolor,ne.patternBgColor),T.dataLabelStyle=te._configureDataLabelStyle(A),T.shadow={opacity:ne.showShadow?_/100:0},X=(0,r.pluckNumber)(A.dashed),Z=(0,r.pluckNumber)(A.dashlen,d),J=g=(0,r.pluckNumber)(A.dashgap,g),T.plotBorderDashStyle=x=1===X?(0,r.getDashStyle)(Z,J):0===X?"none":P,de=ce.getPlotColor(q),de=(0,r.pluck)(A.color,de),D=(0,r.pluck)(A.ratio,ne.plotfillratio),_=(0,r.pluck)(A.alpha,ne.plotfillalpha),S=(0,r.pluck)(A.alpha,ne.plotborderalpha),N<0&&!m&&(s=C,C=360-C),T.colorArr=F=(0,r.getColumnColor)(de+","+y,_,D,C,m,k,S.toString(),0,!!_e),0!==b&&(B=(0,r.pluck)(A.hovercolor,se.plotfillhovercolor,se.columnhovercolor,de),E=(0,r.pluck)(A.hoveralpha,se.plotfillhoveralpha,se.columnhoveralpha,_),!(I=(0,r.pluck)(A.hovergradientcolor,se.plothovergradientcolor,y))&&(I=""),L=(0,r.pluck)(A.hoverratio,se.plothoverratio,D),O=(0,r.pluckNumber)(360-A.hoverangle,360-re.hoverangle,360-se.plothoverangle,C),R=(0,r.pluck)(A.borderhovercolor,se.plotborderhovercolor,k),G=(0,r.pluck)(A.borderhoveralpha,re.borderhoveralpha,se.plotborderhoveralpha,se.plotfillhoveralpha,S,_),V=(0,r.pluckNumber)(A.borderhoverthickness,re.borderhoverthickness,se.plotborderhoverthickness,v),z=(0,r.pluckNumber)(A.borderhoverdashed,se.plotborderhoverdashed),H=(0,r.pluckNumber)(A.borderhoverdashgap,se.plotborderhoverdashgap,d),W=(0,r.pluckNumber)(A.borderhoverdashlen,se.plotborderhoverdashlen,g),Y=z?(0,r.getDashStyle)(W,H):x,1===b&&B===de&&(B=(0,r.getLightColor)(B,70)),U=(0,r.getColumnColor)(B+","+I,E,L,O,m,R,G.toString(),0,!!_e),T.setRolloutAttr={fill:_e?[(0,r.toRaphaelColor)(F[0]),!ne.use3dlighting]:(0,r.toRaphaelColor)(F[0]),stroke:t&&(0,r.toRaphaelColor)(F[1]),"stroke-width":v,"stroke-dasharray":x},T.setRolloverAttr={fill:_e?[(0,r.toRaphaelColor)(U[0]),!ne.use3dlighting]:(0,r.toRaphaelColor)(U[0]),stroke:t&&(0,r.toRaphaelColor)(U[1]),"stroke-width":V,"stroke-dasharray":Y}),ee&&oe.updateTicksValues(q,{labelfontcolor:(0,r.convertColor)(de)}),T.originalPlotColor=(0,r.hashify)(de),T.displayValue=(0,r.pluck)(M,K),o=T.setTooltext=T.origToolText=(0,r.getValidValue)((0,r.parseUnsafeString)((0,r.pluck)(A.tooltext,se.plottooltext),!1)),T.toolTipValue=Ce.dataLabels(N,ne.parentYAxis),T._x=q,T._y=N,a=T.toolTipValue,De=f(De,N),ye=p(ye,N),0!==N&&ne.minAbsNonZeroValue>Math.abs(N)&&(ne.minAbsNonZeroValue=Math.abs(N),ne.minAbsNonZeroData=T),ge?(ne.showTooltip?o!==u?(n={formattedValue:a,label:T.label,yaxisName:ve,xaxisName:me,cumulativeValue:xe,cumulativeDataValue:Ce.dataLabels(xe),cumulativePercentValue:undefined,sum:Ce.dataLabels(Se),unformattedSum:Se},l=[1,2,3,5,6,7,20,21,22,23,24,25],c=(0,r.parseTooltext)(o,l,n,A,se)):c=T.label?T.label+Pe:"":c=!1,T.toolText=c):c=!1,T.toolText=c,T.tooltext=o,T.setTooltext=c,s&&(C=s));ne.maxValue=De,ne.minValue=ye},t}(i["default"]);t.ParetoColumnDataset=g},1004:(e,t,a)=>{var n=a(269);t.__esModule=!0,t.ParetoColumn3DDataset=void 0;var o=n(a(288)),r=a(997),i=a(612),l=function(e){function t(){return e.apply(this,arguments)||this}(0,o["default"])(t,e);var a=t.prototype;return a._getHoveredPlot=function(e,t){var a,n,o=this;return a=o.getFromEnv("xAxis").getValue(e),(n=Math.round(a))-a>0?i._checkPointerOverColumn.call(o,n,e,t)||i._checkPointerOverColumn.call(o,n-1,e,t):i._checkPointerOverColumn.call(o,n+1,e,t)||i._checkPointerOverColumn.call(o,n,e,t)},a.createContainer=function(){var e=this,t=e.getLinkedParent();!e.getContainer("labelGroup")&&e.addContainer("labelGroup",function(e,t,a){return a.getFromEnv("animationManager").setAnimation({el:"group",attr:{name:e},container:t,state:"appearing",component:a,label:"group"})}("label-group",t.getChildContainer("vcanvasLabelGroup"),e).attr("class","fusioncharts-datalabels"))},t}(r.ParetoColumnDataset);t.ParetoColumn3DDataset=l},999:(e,t,a)=>{var n=a(269);t.__esModule=!0,t.ParetoLineDataset=void 0;var o,r=n(a(288)),i=n(a(616)),l=a(274),s=Math,c=s.min,u=s.max,d=s.abs,p=function(e){function t(){return e.apply(this,arguments)||this}return(0,r["default"])(t,e),t.prototype.configureAttributes=function(e){if(!e)return!1;this.trimData(e),this.config.JSONData=e;var t,a,n,r,i,s,p,f,h,g,v,m,b,C=this,_=C.getFromEnv("chart"),D=C.config,y=C.getFromEnv("xAxis"),S=D.JSONData,k=S.data,x=k&&k.length,P=C.getFromEnv("chart-attrib"),A=C.getFromEnv("color-manager"),N=(0,l.pluckNumber)(P.showtooltip,1),w=((0,l.parseUnsafeString)(P.yaxisname),(0,l.parseUnsafeString)(P.xaxisname),C.components.data),T=C.getFromEnv("number-formatter"),F=_.config.is3D,M=-Infinity,B=+Infinity,E=0,I=[],L=0,O=D.tootipSepChar=(0,l.pluck)(P.tooltipsepchar,", ");for(D.defaultPadding={left:.5,right:.5},D.enableAnimation=p=(0,l.pluckNumber)(P.animation,P.defaultanimation,1),D.animation=!!p&&{duration:1e3*(0,l.pluckNumber)(P.animationduration,1)},D.showTooltip=(0,l.pluckNumber)(P.showtooltip,1),D.valuePadding=(0,l.pluckNumber)(P.valuepadding,2),D.showTextOutline=(0,l.pluckNumber)(P.textoutline,0),D.rotateValues=(0,l.pluckNumber)(P.rotatevalues)?270:0,D.showHoverEffect=(0,l.pluckNumber)(P.plothovereffect,P.showhovereffect,o),D.showShadow=F?(0,l.pluckNumber)(P.showshadow,1):(0,l.pluckNumber)(P.showshadow,A.getColor("showShadow")),D.useDataPlotColorForLabels=(0,l.pluckNumber)(P.usedataplotcolorforlabels,0),D.use3dlineshift=(0,l.pluckNumber)(P.use3dlineshift,_.use3dlineshift),D.drawLine=1,D.linecolor=(0,l.getFirstColor)((0,l.pluck)(P.linecolor,A.getColor("plotBorderColor"))),D.linethickness=(0,l.pluckNumber)(P.linethickness,2),D.linealpha=(0,l.pluck)(P.linealpha,"100"),D.linedashed=(0,l.pluckNumber)(P.linedashed,0),D.linedashlen=(0,l.pluckNumber)(S.linedashlen,P.linedashlen,5),D.linedashgap=(0,l.pluckNumber)(S.linedashgap,P.linedashgap,4),f=(0,l.getDashStyle)(D.linedashlen,D.linedashgap),D.lineDashStyle=D.linedashed?f:"none",D.drawanchors=(0,l.pluckNumber)(P.drawanchors,P.showanchors),D.anchorbgcolor=(0,l.pluck)(P.anchorbgcolor,A.getColor("anchorBgColor")),D.anchorbordercolor=(0,l.pluck)(P.anchorbordercolor,D.linecolor),D.anchorradius=(0,l.pluckNumber)(P.anchorradius,3),D.anchoralpha=(0,l.pluck)(P.anchoralpha),D.anchorbgalpha=(0,l.pluck)(P.anchorbgalpha,100),D.anchorborderthickness=(0,l.pluck)(P.anchorborderthickness,1),D.anchorsides=(0,l.pluck)(P.anchorsides,0),D.anchorimageurl=(0,l.pluck)(P.anchorimageurl),D.anchorimagealpha=(0,l.pluckNumber)(P.anchorimagealpha,100),D.anchorimagescale=(0,l.pluckNumber)(P.anchorimagescale,100),D.anchorimagepadding=(0,l.pluckNumber)(P.anchorimagepadding,1),D.anchorstartangle=(0,l.pluckNumber)(P.anchorstartangle,90),D.parentYAxis=1,D.valuePosition=(0,l.pluck)(P.valueposition,"auto"),D.showvalues=D.showValues=(0,l.pluckNumber)(P.showlinevalues,P.showvalues,1),C.setState("visible",1===(0,l.pluckNumber)(S.visible,1)),C.setState("dirty",!0),D.shadow={opacity:D.showShadow?D.linealpha/100:0},D.showCumulativeLine=(0,l.pluckNumber)(P.showcumulativeline,1),D.maxRadius=-Infinity,w||(w=C.components.data=[]),h=0;h<x;h++)E+=v=d(T.getCleanValue(k[h].value)),I[h]=(0,l.extend2)({},k[h]),I[h].value=v;for(I.sort((function(e,t){return t.value-e.value})),D.imageCount=0,h=0;h<x;h++)n=I[h],(i=w[h])||(i=w[h]={graphics:{}}),i.config||(s=w[h].config={}),null!==(r=d(T.getCleanValue(n.value)))&&(s=i&&i.config,m=y.getLabel(h),s.label=(0,l.getValidValue)((0,l.parseUnsafeString)((0,l.pluck)(m.label))),s.showValue=(0,l.pluckNumber)(n.showvalue,D.showValues),s.setValue=r,s.setLink=(0,l.pluck)(n.link),s.setDisplayValue=(0,l.parseUnsafeString)(n.displayvalue),L+=s.setValue,s.dataLabelStyle=C._configureDataLabelStyle(n),s.valuePadding=(0,l.pluckNumber)(n.valuepadding,S.valuepadding,_.config.valuepadding),r=s.setValue=L/E*100,g=T.percentValue(r),s.toolTipValue=g,s.displayValue=g,s.valuePosition=(0,l.pluck)(n.valueposition,D.valuePosition),s.anchorProps=this._parseAnchorProperties(h,I),s.hoverEffects=this._parseHoverEffectOptions(i),b=s.anchorProps,D.maxRadius=Math.max(D.maxRadius,b.radius+b.borderThickness/2),s._x=h,s._y=r,s.toolTipValue,M=u(M,r),B=c(B,r),N?(a=!!D.showTooltip&&(s.label?s.label+O+s.toolTipValue:""),s.toolText=a):a=!1,s.toolText=a,s.tooltext=t,s.setTooltext=a);D.maxValue=M,D.minValue=B},t}(i["default"]);t.ParetoLineDataset=p},983:(e,t)=>{t.__esModule=!0,t["default"]=void 0;var a={"initial.dataset.pie3d":function(){return{"group.appearing":function(e){var t=e.component.getFromEnv("chartConfig");return"plots"===e.attr.name?[{initialAttr:{opacity:"0"},finalAttr:{opacity:"1"},slot:t.alphaanimation?"plot":"initial"}]:[{initialAttr:{opacity:"1"},finalAttr:{opacity:"1"},slot:"final"}]},"slice.appearing":function(e){var t=e.component,a=t.getFromEnv("chart").config,n=t.config,o=e.attr;return a.alphaanimation?[{initialAttr:{opacity:"1"},slot:"plot"}]:n.animateClockWise?[{initialAttr:{sAngle:0,eAngle:0,transform:""},finalAttr:{sAngle:o.sAngle,eAngle:o.eAngle,transform:""},slot:"plot",startEnd:{start:0,end:.75}},{finalAttr:{transform:o.transform},slot:"plot",startEnd:{start:.75,end:1}}]:[{initialAttr:{sAngle:2*Math.PI,eAngle:2*Math.PI,transform:""},finalAttr:{sAngle:o.sAngle,eAngle:o.eAngle,transform:""},slot:"plot",startEnd:{start:0,end:.75}},{finalAttr:{transform:o.transform},slot:"plot",startEnd:{start:.75,end:1}}]},"label.updating":[{initialAttr:{opacity:"1"},finalAttr:{opacity:"1"},slot:"final"}],"label.appearing":[{initialAttr:{opacity:"0"},finalAttr:{opacity:"1"},slot:"final"}],"connector.updating":function(e){return[{initialAttr:{path:e.el.attr("path")||e.attr.path,opacity:e.el.attr("opacity")},finalAttr:{path:e.attr.path},slot:"final"}]},"connector.appearing":function(e){return[{initialAttr:"string"==typeof e.el?{opacity:"0"}:{path:e.attr.path,opacity:"0"},slot:"final"}]},"connector-sliced.updating":function(e){return[{initialAttr:{path:e.el.attr("path")},finalAttr:{path:e.attr.path},slot:"plot"}]},"label-sliced.updating":function(e){return[{initialAttr:{x:e.el.attr("x"),y:e.el.attr("y")},slot:"plot"}]},"*":null}}};t["default"]=a},980:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(292)),r=n(a(288)),i=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{"default":e};var a=f(t);if(a&&a.has(e))return a.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in e)if("default"!==r&&Object.prototype.hasOwnProperty.call(e,r)){var i=o?Object.getOwnPropertyDescriptor(e,r):null;i&&(i.get||i.set)?Object.defineProperty(n,r,i):n[r]=e[r]}n["default"]=e,a&&a.set(e,n);return n}(a(974)),l=a(274),s=a(290),c=a(981),u=n(a(983)),d=a(282),p=n(a(984));function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(f=function(e){return e?a:t})(e)}var h,g=(0,d.getDep)("redraphael","plugin"),v=window,m="hidden",b=8===window.document.documentMode?"visible":"",C=",",_="eventArgs",D="groupId",y="pointer",S=l.preDefStr.elementStr,k="M",x="L",P="v",A="A",N="Z",w=Math,T=w.max,F=w.min,M=w.abs,B=w.ceil,E=w.sin,I=w.atan2,L=w.cos,O=w.floor,R=w.round,G=w.PI,V=2*G,z=G/2,H=G+z,W=function(e,t){for(var a=[],n=0,o=e.length;n<o;n++)a[n]=t.call(e[n],e[n],n,e);return a},Y=function(e){return"string"==typeof e},U=function(e,t){return parseInt(e,t||10)},j={lighting3D:{},lighting2D:{}},X=function(e,t,a,n,o){return I((t-a[1]-n.top)/o,e-a[0]-n.left)},Z=function(e){var t=this.data("plotItem"),a=t.index,n=t.chart,o=n.getFromEnv("animationManager"),r=n.config,i=n.getChildren("dataset")[0],l=i.config,s=i.components.data[a],c=s.graphics,u=s.config,d=c.element,p=u.hoverEffects;l.isRotating||(n.plotEventHandler(d,e,"DataPlotRollOver"),p.enabled&&o.setAnimation({el:d,attr:p,component:i})),r.isHovered=!0},J=function(e){var t,a,n=this.data("plotItem"),o=n.index,r=n.chart,i=r.getFromEnv("animationManager"),l=r.config,s=r.getChildren("dataset")[0],c=s.config,u=s.components.data[o];u&&(t=u.config,a=u.graphics.element,c.isRotating||(r.plotEventHandler(a,e,"DataPlotRollOut"),i.setAnimation({el:a,attr:{color:t.color.color.split(",")[0],alpha:t._3dAlpha,borderWidth:t.borderWidth,borderColor:t.borderColor},component:s})),l.isHovered=!1)},q=function(e){var t,a=this.data("plotItem").chart.getChildren("dataset")[0],n=(0,l.pluckNumber)(e.button,e.originalEvent.button),o=a.config,r=e.data[0],i=e.data[1];o.isRightClicked=!(l.touchEnabled||0===n||1===n),o.enableRotation&&!o.isRightClicked&&(o.isRotating=!1,t=X.call(e,r,i,o.center,o.chartPosition=(0,l.getPosition)(a.getFromEnv("chart-container")),o.pieYScale),o.dragStartAngle=t,o._lastAngle=-o.startAngle,o.startingAngleOnDragStart=o.startAngle)},K=function(){var e=this.data("plotItem"),t=e.index,a=e.chart,n=a.getFromEnv("animationManager"),o=a.config,r=a.getChildren("dataset")[0],i=r.config,s=r.components.data[t],c=s.graphics,u=s.config,d=c.element,p=i.startAngle;i.isRightClicked||i.isRotating&&(setTimeout((function(){i.isRotating=!1}),0),a.fireChartInstanceEvent("rotationEnd",{startingAngle:(0,l.normalizeAngle)(p,!0),changeInAngle:p-i.startingAngleOnDragStart}),!o.isHovered&&n.setAnimation({el:d,attr:{color:u.color.color.split(",")[0],alpha:u._3dAlpha,borderWidth:u.borderWidth,borderColor:u.borderColor},component:r}))},$=function(e){var t,a,n,o=this.data("plotItem").chart,r=e.data,i=r[0],s=r[1],c=r[2],u=r[3],d=o.getChildren("dataset")[0],p=d.config;isNaN(i)||isNaN(s)||!p.enableRotation||p.singletonCase||p.isRightClicked||(t=X.call(e,c,u,p.center,p.chartPosition,p.pieYScale),p.dragStartAngle===t||p.isRotating||(p.isRotating=!0,o.fireChartInstanceEvent("rotationStart",{startingAngle:(0,l.normalizeAngle)(p.startAngle,!0)})),n=t-p.dragStartAngle,p.dragStartAngle=t,p.moveDuration=0,p._lastAngle+=180*n/G,a=(new Date).getTime(),(!p._lastTime||p._lastTime+p.timerThreshold<a)&&(p._lastTime||d._rotate(),p.timerId=setTimeout((function(){o.disposed&&o.disposing||d._rotate()}),p.timerThreshold),p._lastTime=a))},Q=function(e,t){return e._conf.index-t._conf.index||e._conf.cIndex-t._conf.cIndex||e._conf.isStart-t._conf.isStart||e._conf.si-t._conf.si},ee=function(e,t){return e.point.value-t.point.value},te=function(e,t){return e.angle-t.angle},ae=["start","start","end","end"],ne="middle",oe=[-1,1,1,-1],re=[1,1,-1,-1],ie={stroke:!0,strokeWidth:!0,"stroke-width":!0,dashstyle:!0,"stroke-dasharray":!0,translateX:!0,translateY:!0,"stroke-opacity":!0,fill:!0,"fill-opacity":!0,opacity:!0,transform:!0,cursor:!0,sAngle:!0,eAngle:!0,color:!0,alpha:!0,borderColor:!0,borderAlpha:!0,borderWidth:!0,rolloverProps:!0,showBorderEffect:!0,positionIndex:!0,cx:!0,cy:!0,radiusYFactor:!0,r:!0,innerR:!0},le=function(e,t){var a,n,o,r,i,l,s,c=e,u=this,d=u._confObject,p={},f=d.elements,g=d.Pie3DManager;if(Y(c)&&((s=t)!==h&&null!==s)&&(a=c,(c={})[a]=t),!c||Y(c))u=ie[c]?d[c]:u._attr(c);else{for(a in c)n=c[a],ie[a]?(d[a]=n,"cursor"===a||"transform"===a||"opacity"===a||"fill-opacity"===a?(p[a]=n,l=!0):"sAngle"===a||"eAngle"===a||"cx"===a||"cy"===a||"radiusYFactor"===a||"r"===a||"innerR"===a?r=!0:"color"!==a&&"alpha"!==a&&"borderColor"!==a&&"borderAlpha"!==a&&"borderWidth"!==a||(i=!0)):u._attr(a,n);if(r&&(g._setSliceShape(d),g.refreshDrawing()),(i||r)&&g._setSliceCosmetics(d),l){for(o in f)f[o].attr(p);u._attr(p)}}return u},se=function(e,t,a){if(!a){var n,o=this._confObject.elements;for(n in o)o[n].on(e,t);return this._on(e,t)}this._on(e,t,!0)},ce=function(e,t,a){var n,o=this._confObject.elements,r=v.navigator.userAgent.toLowerCase().indexOf("android")>-1;for(n in o)r&&"topBorder"!==n&&"frontOuter"!==n&&"startSlice"!==n&&"endSlice"!==n||o[n].drag(e,t,a);return this._drag(e,t,a)},ue=function(){var e,t=this._confObject.elements;for(e in t)t[e].hide();return this._hide()},de=function(){var e,t=this._confObject.elements;for(e in t)t[e].show();return this._show()},pe=function(){var e,t=this._confObject,a=t.elements;for(e in a)a[e].destroy();return l.hasSVG&&(t.clipTop.destroy(),t.clipOuterFront.destroy(),t.clipOuterBack.destroy(),t.clipOuterFront1&&t.clipOuterFront1.destroy(),t.clipInnerFront&&t.clipInnerFront.destroy(),t.clipInnerBack&&t.clipInnerBack.destroy()),this._destroy()},fe=function(e,t){var a,n=this,o=n._confObject.elements;if(t===h)return n._data(e);for(a in o)o[a].data(e,t);return n._data(e,t)},he=0;(0,d.addDep)({name:"pie3dAnimation",type:"animationRule",extension:u["default"]});var ge=function(e){function t(){return e.apply(this,arguments)||this}(0,r["default"])(t,e);var a=t.prototype;return a.__setDefaultConfig=function(){e.prototype.__setDefaultConfig.call(this);var t=this.config;t.setBorderWidth=h,t.alphaanimation=1,t.showBorderEffect=h},a.placeDataLabels=function(e){var t,a,n,o,r,s,c,u,d,p,f,g,v,C,D,y,S,P,A,N,I,O,W,Y,U,j,X,Q,ie,le,se,ce,ue,de,pe,fe,he,ge=this,ve=ge.getFromEnv("chart"),me=ve.getFromEnv("toolTipController"),be=ve.config,Ce=ge.config,_e=ge.components.data,De=Ce.piePlotOptions,ye=be.canvasLeft,Se=be.canvasTop,ke=be.canvasWidth,xe=ye+.5*be.canvasWidth,Pe=Se+.5*be.canvasHeight,Ae=ve.getFromEnv("smartLabel"),Ne=Ce.dataLabelOptions,we=Ne.style,Te=(0,l.pluckNumber)(B(parseFloat(we.lineHeight)),12),Fe=(0,l.getFirstValue)(Ne.placeInside,!1),Me=Ne.skipOverlapLabels,Be=Ne.manageLabelOverflow,Ee=Ne.connectorPadding,Ie=Ne.distance,Le=Ne.connectorWidth,Oe=[[],[],[],[]],Re=ye,Ge=Se,Ve=ke,ze=parseInt(we.fontSize,10),He=ze,We=He/2,Ye=[Ee,Ee,-Ee,-Ee],Ue=Ne.isSmartLineSlanted,je=Ie>0,Xe=Ce.center||(Ce.center=[xe,Pe,De.size,De.innerSize||0]),Ze=Xe[1],Je=Xe[0],qe=Xe[2],Ke=Xe[4],$e=Ce.labelsRadius,Qe=R(100*Ce.labelsRadiusY)/100,et=Ce.maxLabels,tt=Ce.enableSmartLabels,at=Ce.pieSliceDepth/2,nt=ve.getFromEnv("animationManager"),ot=ge.getContainer("label-group");if(Ae.useEllipsesOnOverflow(be.useEllipsesWhenOverflow),Ce.dataLabelCounter)if(e||Ae.setStyle(we),1===_e.length)I=_e[0],se=I.graphics,le=I.config,he=le._textAttrs,fe=le._textCss,U=se.label,ce=se.connector,le.slicedTranslation=[Re,Ge],null!==le.y&&le.y!==h&&(he.visibility=b,he["text-anchor"]=ne,he.x=Je,he.y=Ze+We-2,he._x=Je),fe.cursor=le.labellink?"pointer":"",U=se.label=nt.setAnimation({el:se.label||"text",attr:he,css:fe,label:"label",container:ot,component:ge}),be.showtooltip&&U.abbrArr&&U.abbrArr.length&&me.enableToolTip(U,h),U.on("fc-dragstart",q).on("fc-dragmove",$).on("fc-dragend",K).on("fc-click",i.labelClickFn.bind(U,ve,I.config.labellink)).on("fc-mouseup",(0,i.plotClickHandler)(ge,U)).on("fc-mouseover",Z).on("fc-mouseout",J),he._x&&(U.x=he._x,delete he.x),U.data("plotItem",he.plotItem).data(_,he.eventArgs),he.visibility===b&&U.show(),ce&&ce.hide();else if(Fe)(0,l.fcEach)(_e,(function(e){var t,a,n;se=e.graphics,le=e.config,he=le._textAttrs,U=se.label,null!==le.y&&le.y!==h&&(O=le.angle,A=Ze+Xe[6]*E(O)+We-2,D=Je+Xe[5]*L(O),he._x=D,he._y=A,le.sliced&&(a=(t=e.slicedTranslation)[0]-Re,n=t[1]-Ge,D+=a,A+=n),he.visibility=b,he.align=ne,he.x=D,he.y=A),fe.cursor=le.labellink?"pointer":"",U=se.label=nt.setAnimation({el:se.label||"text",attr:he,css:fe,label:"label",container:ot,component:ge}),be.showtooltip&&U&&U.abbrArr&&U.abbrArr.length&&me.enableToolTip(U,h),U.data("plotItem",he.plotItem).data(_,he.eventArgs),he.visibility===b&&U.show(),U.x=he._x,U._x=he._x,U._y=he._y}));else{for((0,l.fcEach)(_e,(function(e){if(se=e.graphics,le=e.config,fe=le._textCss,!((he=le._textAttrs).text=le.displayValue))return se.connector&&nt.setAnimation({el:se.connector,component:ge,callback:i.hideFn}),void(se.label&&nt.setAnimation({el:se.label,component:ge,callback:i.hideFn}));se=e.graphics,null!==le.y&&le.y!==h&&(U=se.label,(ce=se.connector)&&ce.show(),U&&U.show()),U=se.label,be.showtooltip&&U&&U.abbrArr&&U.abbrArr.length&&me.enableToolTip(U,h),(O=le.angle)<0&&(O=V+O),Oe[O>=0&&O<z?1:O<G?2:O<H?3:0].push({point:e,angle:O})})),o=4;o--;){if(Me&&(W=Oe[o].length-et)>0)for(Oe[o].sort(ee),s=0,p=(Y=Oe[o].splice(0,W)).length;s<p;s+=1)I=Y[s].point,(se=I.graphics).label&&nt.setAnimation({el:se.label,attr:{visibility:m},component:ge}),se.connector&&nt.setAnimation({el:se.connector,attr:{visibility:m},component:ge});Oe[o].sort(te)}for(pe=T(Oe[0].length,Oe[1].length,Oe[2].length,Oe[3].length),de=T(F(pe,et)*He,Qe+He),Oe[1].reverse(),Oe[3].reverse(),Ae.setStyle(we),f=4;f--;){for(p=(N=Oe[f]).length,Me||(We=(He=p>et?de/p:ze)/2),d=p*He,a=de,o=0;o<p;o+=1,d-=He)a<(c=M(de*E(N[o].angle)))?c=a:c<d&&(c=d),a=(N[o].oriY=c)-He;for(n=ae[f],u=de-(p-1)*He,a=0,o=N.length-1;o>=0;o-=1,u+=He)I=N[o].point,se=I.graphics,le=I.config,he=le._textAttrs,fe=le._textCss,null!==le.y&&he.text&&(O=N[o].angle,g=le.sliced,U=se.label,(c=M(de*E(O)))<a?c=a:c>u&&(c=u),a=c+He,S=(c+N[o].oriY)/2,v=Je+re[f]*$e*L(w.asin(S/de)),S*=oe[f],S+=Ze,P=Ze+Ke*E(O),C=Je+qe*L(O),(f<2&&v<C||f>1&&v>C)&&(v=C),A=S+We-2,y=(D=v+Ye[f])+Ye[f],he._x=y,Be&&(r=f>1?y-ye:ye+Ve-y,Ae.setStyle(le.style),Te=(0,l.pluckNumber)(B(parseFloat(le.style.lineHeight)),12)+(2*B(parseFloat(le.style.border),12)||0),ie=Ae.getSmartText(le.displayValue,r,Te),he.text=ie.text,he.tooltip=ie.tooltext),O<G&&(S+=at,P+=at,A+=at),he._y=A,g&&(X=le.transX,Q=le.transY,D+=X,v+=X,C+=X,S+=Q,P+=Q,y+=X),he.visibility=b,he["text-anchor"]=n,he.x=y,he.y=S,he.opacity=1,fe.cursor=le.labellink?"pointer":"",j=se.label,U=nt.setAnimation({el:j||"text",attr:he,css:fe,container:ot,component:ge,label:"label"}),be.showtooltip&&U&&U.abbrArr&&U.abbrArr.length&&me.enableToolTip(U,h),U.outlineText(Ce.showTextOutline,he.fill),U.data("textPos",{x:y,y:S}).data("plotItem",he.plotItem).data(_,he.eventArgs),j||(se.label=U,U.on("fc-dragstart",q).on("fc-dragmove",$).on("fc-dragend",K).on("fc-click",i.labelClickFn.bind(U,ve,I.config.labellink)).on("fc-click",(0,i.plotClickHandler)(ge,U)).on("fc-mouseover",Z).on("fc-mouseout",J)),U.x=he._x,U._x=he._x,U.y=he._y,he.tooltip&&(me.enableToolTip(U,he.tooltip),delete he.tooltip),je&&Le&&tt&&(ce=se.connector,le.connectorPath=ue=[k,C,P,x,Ue?v:C,S,D,S],t={path:ue,"stroke-width":Le,stroke:Ne.connectorColor||"#606060",opacity:1,visibility:b},ce&&nt.setAnimation({el:ce,attr:t,label:"connector",component:ge})))}}},a._parsePie3DOptions=function(){var e=this,t=e.config;return{size:2*t.pieMinRadius,slicedOffset:t.slicingDistance,allowPointSelect:!0,cursor:y,innerSize:"pie3d"===e.getName()?0:c._getInnerSize.call(e)}},a._parseBorderConfig=function(e,t,a){var n=this.config.pieBorderColor,o=this.getFromEnv("chart-attrib"),r=(0,l.pluck)(a.bordercolor,n),i=(0,l.pluck)(a.borderalpha,o.plotborderalpha,o.pieborderalpha);return{setPlotBorderColor:r=(0,l.pluck)(r,(0,l.getLightColor)(e,90)).split(C)[0],setPlotBorderAlpha:i=o.showplotborder===l.ZEROSTRING?l.ZEROSTRING:(0,l.pluck)(i,t,"80")}},a._initPie3dManager=function(){var e,t,a,n,o,r,i,s,c,u,d,p=this,f=p.getFromEnv("chart"),h=f.config,g=1e3,v=0,m=p.config,b=p.components,C=m.dataLabelOptions,_=m.pie3DOptions=p._parsePie3DOptions(),D=(0,l.pluck)(m.startAngle,0)%V,y=m.managedPieSliceDepth,S=m.slicedOffset=_.slicedOffset,k=h.canvasWidth,x=h.canvasHeight,P=[h.canvasLeft+.5*k,h.canvasTop+.5*x-.5*y],A=b.data,N=F(k,x),T=C.distance,M=m.pieYScale,B=m.slicedOffsetY||(m.slicedOffsetY=S*m.pieYScale),I=p.getFromEnv("pie3DManager");for(P.push(2*m.pieMinRadius,_.innerSize||0),(P=W(P,(function(e,t){return/%$/.test(e)?[k,x-y,N,N][t]*U(e)/100:e})))[2]/=2,P[3]/=2,P.push(P[2]*M),P.push((P[2]+P[3])/2),P.push(P[5]*M),p.getX=function(e,t){return r=w.asin((e-P[1])/(P[2]+T)),P[0]+(t?-1:1)*(L(r)*(P[2]+T))},m.center=P,(0,l.fcEach)(A,(function(e){v+=e.config.y})),m.labelsRadius=P[2]+T,m.labelsRadiusY=m.labelsRadius*M,m.quadrantHeight=(x-y)/2,m.quadrantWidth=k/2,s=(i=R((i=D)*g)/g)+V,t=(0,l.pluckNumber)(parseInt(C.style.fontSize,10),10)+4,m.maxLabels=O(m.quadrantHeight/t),m.labelFontSize=t,m.connectorPadding=(0,l.pluckNumber)(C.connectorPadding,5),m.isSmartLineSlanted=(0,l.pluck)(C.isSmartLineSlanted,!0),m.connectorWidth=(0,l.pluckNumber)(C.connectorWidth,1),m.enableSmartLabels=C.enableSmartLabels,I||(I=new ve(f),p.attachChild(I,"pie3DManager",!1),p.addToEnv("pie3DManager",I)),p._configurePie3DManager(),e=A.length-1;e>=0;e-=1)n=A[e].config,a=i,c=v?n.y/v:0,(i=R((i+c*V)*g)/g)>s&&(i=s),o=i,n.shapeArgs={sAngle:R(a*g)/g,eAngle:R(o*g)/g},n.centerAngle=r=(o+a)/2%V,n.slicedTranslation=[R(L(r)*S),R(E(r)*B)],u=L(r)*P[2],m.radiusY=d=E(r)*P[4],n.tooltipPos=[P[0]+.7*u,P[1]+d],n.percentage=100*c,n.total=v},a._configurePie3DManager=function(){var e=this,t=e.config,a=e.components,n=e.getFromEnv("pie3DManager"),o=a.data;n&&n.configure(t.pieSliceDepth,1===o.length,t.use3DLighting,!1)},a.allocatePosition=function(){var e,t,a,n,o,r,i,s=this,c=s.getFromEnv("chart").config,u=s.config,d=s.components.data,p=1e3,f=(0,l.pluck)(u.startAngle,0)%V,h=u.pie3DOptions=s._parsePie3DOptions(),g=u.pieYScale,v=u.managedPieSliceDepth,m=c.canvasWidth,b=c.canvasHeight,C=F(m,b),_=[c.canvasLeft+.5*m,c.canvasTop+.5*b-.5*v],D=0,y=d.length;for(_.push(2*u.pieMinRadius,h.innerSize||0),(_=W(_,(function(e,t){return/%$/.test(e)?[m,b-v,C,C][t]*U(e)/100:e})))[2]/=2,_[3]/=2,_.push(_[2]*g),_.push((_[2]+_[3])/2),_.push(_[5]*g),u.center=_,(0,l.fcEach)(d,(function(e){D+=e.config.y})),i=(f=R(f*p)/p)+V,e=d.length-1;e>=0;e-=1)o=d[e],a=f,r=D?o.config.y/D:0,(f=R((f+r*V)*p)/p)>i&&(f=i),n=f,o.config.shapeArgs={sAngle:R(a*p)/p,eAngle:R(n*p)/p};for(e=0;e<y;e++)t=d[e],s.parsePlotAttributes(t,e),s.parseLabelAttributes(t,e)},a.parsePlotAttributes=function(e,t){var a,n,o,r,i,s,c,u,d,p,f,g,v,m,b,C,D,S,k,x=this,P=x.components,A=x.config,N=x.getFromEnv("chart"),w=N.config,T=P.data,F=A.dataLabelOptions,M=F.style,B=A.slicingDistance,I=A.slicedOffsetY||(A.slicedOffsetY=B*A.pieYScale),O=A.showBorderEffect,R=T.length,G=A.usePerPointLabelColor,V=w.textDirection,z=t,H=w.dataLabelStyle;a=A.center,A.prevPositions,n=A.pieYScale,(b=(v=e.config)._textAttrs)||(b=v._textAttrs={}),(C=v._textCss)||(C=v._textCss={}),i=v.y,s=v.displayValue,u=v.sliced,f=v.shapeArgs,d=v.centerAngle,g=v.toolText,c=!!v.link,M=v.style,null!==i&&i!==h&&(D={sAngle:f.sAngle,eAngle:f.eAngle,r:a[2],innerR:a[3],cx:a[0],cy:a[1],radiusYFactor:n,opacity:1},s!==h?(M?((C=v._textCss)||(C=v._textCss={}),C.fontFamily=M.fontFamily,C.fontSize=M.fontSize,C.lineHeight=M.lineHeight,C.fontWeight=M.fontWeight,C.fontStyle=M.fontStyle):v._textCss&&(delete v._textCss,C=h),v.style=M||(M=H),b.text=s,b.fill=(G?(0,l.toRaphaelColor)(v.color):M.color)||"#000000",b["text-bound"]=[M.backgroundColor,M.borderColor,M.borderThickness,M.borderPadding,M.borderRadius,M.borderDash],b.direction=V,b.lineHeight=M.lineHeight,F.distance>0&&(p=F.connectorWidth)&&F.enableSmartLabels&&(S={"stroke-width":p,stroke:F.connectorColor||"#606060",cursor:c?y:"",opacity:1})):b.text=l.BLANKSTRING,v.plotItem=r={chart:N,index:z,seriesData:A,value:i,angle:v.angle=d,link:v.link,shapeArgs:f,slicedX:u&&!A.singletonCase?L(d)*B:0,slicedY:u&&!A.singletonCase?E(d)*I:0,sliced:u,labelText:s,name:v.name,percentage:v.percentage,toolText:g,originalIndex:R-z-1,style:v.style,transX:v.transX=L(d)*B,transY:v.transY=E(d)*I,slicedTranslation:v.slicedTranslation="t"+v.transX+","+v.transY,label:m,connector:undefined},v.eventArgs=o={index:A.reversePlotOrder?R-1-z:z,link:v.link,value:v.y,displayValue:v.displayValueArgs,categoryLabel:v.categoryLabel,isSliced:v.sliced,toolText:v.toolText,color:v.setColor,alpha:v.setAlpha,borderColor:v.borderConfig.setPlotBorderColor,borderAlpha:v.borderConfig.setPlotBorderAlpha,dashed:v.setBorderDashed,showLabel:v.showLabel,showValue:v.showValue,labelPosition:v.labelPosition,valuePosition:v.valuePosition,labelFont:v.labelFont,labelFontColor:v.labelFontColor||"#555555",labelLink:v.labelLink,hoverColor:v.hoverEffects.hoverColor,hoverAlpha:v.hoverEffects.alpha,borderHoverColor:v.hoverBorderColor,borderHoverAlpha:v.hoverEffects.borderAlpha,id:v.id},k={color:v.color.color.split(",")[0],alpha:v._3dAlpha,borderWidth:v.borderWidth,borderColor:v.borderColor,borderAlpha:v.borderConfig.setPlotBorderAlpha},(0,l.extend2)(D,k),D.cursor=c?y:"",D.showBorderEffect=O,D.transform="t"+r.slicedX+","+r.slicedY,b.plotItem=r,b[_]=o,v.props={element:{attr:D},connector:{attr:S},label:{attr:b,css:C}})},a.draw=function(){var e,t,a,n,o,r,s,c,u,d,p,f,g,v,m,b,C,S,k,x,P,A,N,w,T,F,M,B,I=this,O=I.components,R=I.config,G=I.getFromEnv("chart"),V=G.config,z=G.getFromEnv("animationManager"),H=O.data,W=R.dataLabelOptions,Y=W.style,U=R.slicingDistance,j=R.slicedOffsetY||(R.slicedOffsetY=U*R.pieYScale),X=R.showBorderEffect,Q=H.length,ee=R.usePerPointLabelColor,te=V.textDirection,ae=R.valueTotal,ne=O.removeDataArr||[],oe=I.getState("visible"),re=I.getContainer("labelGroup"),ie={},le=I.getFromEnv("toolTipController"),se=V.dataLabelStyle;for(I.getContainer("pie-groups")||I._createContainer(),re=I.getContainer("label-group"),x=I.getContainer("plot-group"),z.setAnimation({el:re,attr:{css:se},component:I,label:"labelcontainer",callback:function(){oe&&ae?(re.show(),x.show()):(re.hide(),x.hide())}}),I._initPie3dManager(),a=I.getFromEnv("pie3DManager"),ne.length&&I.remove(),e=R.center,R.prevPositions||e,t=R.pieYScale,H&&Q||(H=[]),k=-1;++k<Q;)if((A=(m=(r=H[k]).config)._textAttrs)||(A=m._textAttrs={}),b=r.graphics,s=m.y,c=m.displayValue,d=m.sliced,g=m.shapeArgs,p=m.centerAngle,v=m.toolText,u=!!m.link,Y=m.style,null!==s&&s!==h){for(M in P=b.element,C=b.label,S=b.connector,w={sAngle:g.sAngle,eAngle:g.eAngle,r:e[2],innerR:e[3],cx:e[0],cy:e[1],radiusYFactor:t,opacity:1},P?F=!1:(F=!0,(P=b.element=a.useSliceFromPool())||(P=b.element=a.createSlice().drag($,q,K).on("fc-mouseover",Z).on("fc-mouseout",J)).on("fc-click",(0,i.plotClickHandler)(I,P))),c!==h&&(Y?((N=m._textCss)||(N=m._textCss={}),N.fontFamily=Y.fontFamily,N.fontSize=Y.fontSize,N.lineHeight=Y.lineHeight,N.fontWeight=Y.fontWeight,N.fontStyle=Y.fontStyle):m._textCss&&(C&&C.removeCSS(),delete m._textCss,N=h),m.style=Y||(Y=se),A.text=c,A.fill=(ee?(0,l.toRaphaelColor)(m.color):Y.color)||"#000000",A["text-bound"]=[Y.backgroundColor,Y.borderColor,Y.borderThickness,Y.borderPadding,Y.borderRadius,Y.borderDash],A.direction=te,A.lineHeight=Y.lineHeight,W.distance>0&&(f=W.connectorWidth)&&W.enableSmartLabels&&(T={"stroke-width":f,stroke:W.connectorColor||"#606060",cursor:u?y:"",opacity:1},re.show(),S=b.connector=z.setAnimation({el:b.connector||"path",attr:T,container:re,label:"connector",component:I}).show().on("fc-dragstart",q).on("fc-dragmove",$).on("fc-dragend",K).on("fc-mouseover",Z).on("fc-mouseout",J))),ae?(C&&C.show(),S&&S.show()):(C&&C.hide(),S&&S.hide()),o={chart:G,index:k,seriesData:R,value:s,angle:m.angle=p,link:m.link,shapeArgs:g,slicedX:d&&!R.singletonCase?L(p)*U:0,slicedY:d&&!R.singletonCase?E(p)*j:0,sliced:d,labelText:c,name:m.name,percentage:m.percentage,toolText:v,originalIndex:Q-k-1,style:m.style,graphic:P,transX:m.transX=L(p)*U,transY:m.transY=E(p)*j,slicedTranslation:m.slicedTranslation="t"+m.transX+","+m.transY,label:C,connector:S},n={index:R.reversePlotOrder?Q-1-k:k,link:m.link,value:m.y,displayValue:m.displayValueArgs,categoryLabel:m.categoryLabel,isSliced:m.sliced,toolText:m.toolText,color:m.setColor,alpha:m.setAlpha,borderColor:m.borderConfig.setPlotBorderColor,borderAlpha:m.borderConfig.setPlotBorderAlpha,dashed:m.setBorderDashed,showLabel:m.showLabel,showValue:m.showValue,labelPosition:m.labelPosition,valuePosition:m.valuePosition,labelFont:m.labelFont,labelFontColor:m.labelFontColor||"#555555",labelLink:m.labellink,hoverColor:m.hoverEffects.hoverColor,hoverAlpha:m.hoverEffects.alpha,borderHoverColor:m.hoverBorderColor,borderHoverAlpha:m.hoverEffects.borderAlpha,id:m.id},ie={color:m.color.color.split(",")[0],alpha:m._3dAlpha,borderWidth:m.borderWidth,borderColor:m.borderColor,borderAlpha:m.borderConfig.setPlotBorderAlpha},F&&(0,l.extend2)(w,ie),P.data(D,k).data("plotItem",o).data(_,n),P.data(D,k).data("plotItem",o).data(_,n),w.cursor=u?y:"",w.showBorderEffect=X,w.color=m.color.color.split(",")[0],w.alpha=m._3dAlpha,w.borderWidth=m.borderWidth,w.borderColor=m.borderColor,le.enableToolTip(P,v),B=P._confObject.elements)le.enableToolTip(B[M],v);w.transform="t"+o.slicedX+","+o.slicedY,A.plotItem=o,A[_]=n,z.setAnimation({el:P,attr:w,component:I,label:"slice",state:F?"appearing":"updating"}),S&&S.data("plotItem",o).data(_,n)}re.show(),I.placeDataLabels(!1),I.drawn=!0,R.prevPositions=e.slice(0)},a.remove=function(){var e,t,a,n,o=this,r=o.config,i=o.components,l=o.getFromEnv("animationManager"),s=i.removeDataArr||[],c=o.pool=o.pool=[],u=s.length,d=this.getFromEnv("pie3DManager"),p=function(e,t){return function(){t===S?(d.removeSlice(e.element),delete e.element):e[t].hide()}},f=r.startAngle,h=r.center;for(a=0;a<u;a++){for(e in t=s[0].graphics)n=t[e],c[e]||(c[e]=[]),e===S?l.setAnimation({el:n,attr:{sAngle:-f,eAngle:.01-f,r:h[2],innerR:h[3],cx:h[0],cy:h[1]},component:o,callback:p(t,e)}):l.setAnimation({el:n,attr:{opacity:0},component:o,callback:p(t,e)});s.splice(0,1)}},a._rotate=function(e){var t,a=this,n=e,o=a.config,r=a.getFromEnv("animationManager"),i=a.components.data,s=o.slicedOffset,c=o.slicedOffsetY,u=o.startAngle,d=a.getFromEnv("pie3DManager");n=isNaN(n)?-o._lastAngle:n,t=(n-u)%360,o.startAngle=(0,l.pluckNumber)(n,o.startAngle)%360,t=-t*G/180,d&&d.rotate(t),(0,l.fcEach)(i,(function(e){var n,o=e.graphics,i=e.config,u=o.element,d=i.shapeArgs,p=d.sAngle+=t,f=d.eAngle+=t,h=i.angle=(0,l.normalizeAngle)((p+f)/2),g=i.sliced,v=L(h),m=E(h);n=i.slicedTranslation=[R(v*s),R(m*c)],i.transX=n[0],i.transY=n[1],i.slicedX=g?L(t)*s:0,i.slicedY=g?E(t)*c:0,u&&g&&r.setAnimation({el:u,attr:{transform:"t"+n[0]+","+n[1]},component:a})})),a.placeDataLabels(!0,i)},a.foldingFn=function(){var e=this.config.startAngle;return{sAngle:-e,eAngle:.01-e}},a.getType=function(){return"dataset"},a.getName=function(){return"pie3d"},t}(i["default"]);g&&g._availableAnimAttrs&&g._availableAnimAttrs.cx&&(g._availableAnimAttrs.innerR=g._availableAnimAttrs.depth=g._availableAnimAttrs.radiusYFactor=g._availableAnimAttrs.sAngle=g._availableAnimAttrs.eAngle=g._availableAnimAttrs.cx),(0,p["default"])(g);var ve=function(e){function t(t){var a;a=e.call(this)||this;var n=(0,o["default"])(a);return n.config={},n.linkedItems={chart:t},a}(0,r["default"])(t,e);var a=t.prototype;return a.getType=function(){return"pie3DManager"},a.getName=function(){return"pie3d"},a.createSlice=function(){var e,t=this,a=t.renderer,n={elements:{},Pie3DManager:t},o=t.slicingWallsArr,r=n.elements,i=l.hasSVG?"litepath":"path";return(e=a[i](t.getContainer("topGroup")))._confObject=n,n.thisElement=e,e._destroy=e.destroy,e.destroy=pe,e._show=e.show,e.show=de,e._hide=e.hide,e.hide=ue,e._on=e.on,e.on=se,e._drag=e.drag,e.drag=ce,e._attr=e.attr,e.attr=le,e._data=e.data,e.data=fe,t.pointElemStore.push(e),r.topBorder=a[i](t.getContainer("topGroup")),r.bottom=a[i](t.getContainer("bottomBorderGroup")).attr({"stroke-width":0}),r.bottomBorder=a[i](t.getContainer("bottomBorderGroup")),r.frontOuter=a[i](t.getContainer("slicingWallsFrontGroup")).attr({"stroke-width":0}),r.backOuter=a[i](t.getContainer("slicingWallsFrontGroup")).attr({"stroke-width":0}),r.startSlice=a[i](t.getContainer("slicingWallsFrontGroup")),r.endSlice=a[i](t.getContainer("slicingWallsFrontGroup")),r.frontOuter1=a[i](t.getContainer("slicingWallsFrontGroup")).attr({"stroke-width":0}),r.frontOuter._conf={si:he,isStart:.5},r.frontOuter1._conf={si:he,isStart:.5},r.startSlice._conf={si:he,isStart:0},r.endSlice._conf={si:he,isStart:1},r.backOuter._conf={si:he,isStart:.4},o.push(r.startSlice,r.frontOuter1,r.frontOuter,r.backOuter,r.endSlice),t.isDoughnut&&(r.frontInner=a[i](t.getContainer("slicingWallsFrontGroup")).attr({"stroke-width":0}),r.backInner=a[i](t.getContainer("slicingWallsFrontGroup")).attr({"stroke-width":0}),r.backInner._conf={si:he,isStart:.5},r.frontInner._conf={si:he,isStart:.4},o.push(r.frontInner,r.backInner)),he+=1,e},a.refreshDrawing=function(){var e,t,a,n,o,r=this.slicingWallsArr,i=0,l=r.length,s=this.getContainer("slicingWallsFrontGroup"),c=this.getContainer("slicingWallsBackGroup");for(r.sort(Q),t=function(e){var t,a,n,o,r=e[0]&&e[0]._conf.index;for(n=r<=G,a=1,t=e.length;a<t;a+=1)if((o=e[a]._conf.index)<=G!==n||o<r)return a;return 0}(r);i<l;i+=1,t+=1)t===l&&(t=0),(o=(e=r[t])._conf.index)<z?s.appendChild(e):o<=G?(a?e.insertBefore(a):s.appendChild(e),a=e):o<=H?(n?e.insertBefore(n):c.appendChild(e),n=e):c.appendChild(e)},a.configure=function(e,t,a,n){var o=this,r=e,i=t,l=a,s=n,c=o.getLinkedParent(),u=o.getFromEnv("paper"),d=c.getContainer("plot-group");"object"==typeof r&&(i=(r=r.depth).hasOnePoint,l=r.use3DLighting,s=r.isDoughnut),o.renderer||(o.renderer=u),o.hasOnePoint=i,o.use3DLighting=l,o.isDoughnut=s,o.depth=r,!o.getContainer("bottomBorderGroup")&&o.addContainer("bottomBorderGroup",u.group("bottom-border",d)),o.getContainer("bottomBorderGroup").attr({transform:"t0,"+r}),!o.getContainer("slicingWallsBackGroup")&&o.addContainer("slicingWallsBackGroup",u.group("slicingWalls-back-Side",d)),!o.getContainer("slicingWallsFrontGroup")&&o.addContainer("slicingWallsFrontGroup",u.group("slicingWalls-front-Side",d)),!o.getContainer("topGroup")&&o.addContainer("topGroup",u.group("top-Side",d)),!o.pointElemStore&&(o.pointElemStore=[]),!o.slicingWallsArr&&(o.slicingWallsArr=[]),o.moveCmdArr=[k],o.lineCmdArr=[x],o.closeCmdArr=[N],o.colorObjs=[]},a._parseSliceColor=function(e,t,a){var n,o,r,i,s,c,u,d,p,f,h,g,v,m,b,_,D,y,S,k,x=e,P=t,A=3,N=this.use3DLighting,w=N?j.lighting3D:j.lighting2D,T=a.radiusYFactor,F=a.cx,M=a.cy,B=a.r,E=B*T,I=a.innerR||0,L=F+B,O=F-B,R=F+I,G=F-I;return~x.indexOf("rgb")&&(x=(0,l.rawRGBtoHEX)(x)),h=(P=P||100)/2,w[x]&&w[x][P]?k=w[x][P]:(w[x]||(w[x]={}),w[x][P]||(w[x][P]={}),k=w[x][P],N?(n=(0,l.getDarkColor)(x,80),o=(0,l.getDarkColor)(x,75),c=(0,l.getLightColor)(x,85),u=(0,l.getLightColor)(x,70),d=(0,l.getLightColor)(x,40),p=(0,l.getLightColor)(x,50),f=(0,l.getLightColor)(x,65),r=(0,l.getDarkColor)(x,69),i=(0,l.getDarkColor)(x,75),s=(0,l.getDarkColor)(x,95)):(A=10,n=(0,l.getDarkColor)(x,90),o=(0,l.getDarkColor)(x,87),c=(0,l.getLightColor)(x,93),u=(0,l.getLightColor)(x,87),d=(0,l.getLightColor)(x,80),f=p=(0,l.getLightColor)(x,85),s=(0,l.getDarkColor)(x,85),r=(0,l.getDarkColor)(x,75),i=(0,l.getDarkColor)(x,80)),g=o+C+c+C+u+C+c+C+o,m=P+C+P+C+P+C+P+C+P,v=o+C+x+C+c+C+x+C+o,b=h+C+h+C+h+C+h+C+h,D=o+C+x+C+d+C+x+C+o,y=i+C+c+C+p+C+c+C+r,S="FFFFFF,FFFFFF,FFFFFF,FFFFFF,FFFFFF",_=0+C+h/A+C+P/A+C+h/A+C+0,l.hasSVG?k.top={FCcolor:{gradientUnits:"userSpaceOnUse",radialGradient:!0,color:f+C+s,alpha:P+C+P,ratio:"0,100"}}:k.top={FCcolor:{gradientUnits:"objectBoundingBox",color:u+C+u+C+c+C+o,alpha:P+C+P+C+P+C+P,angle:-72,ratio:"0,8,15,77"}},k.frontOuter={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:y,alpha:m,angle:0,ratio:"0,20,15,15,50"}},k.backOuter={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:D,alpha:b,angle:0,ratio:"0,62,8,8,22"}},k.frontInner={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:v,alpha:b,angle:0,ratio:"0,25,5,5,65"}},k.backInner={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:g,alpha:m,angle:0,ratio:"0,62,8,8,22"}},k.topBorder={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:S,alpha:_,angle:0,ratio:"0,20,15,15,50"}},k.topInnerBorder={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:S,alpha:_,angle:0,ratio:"0,50,15,15,20"}},k.bottom=(0,l.toRaphaelColor)((0,l.convertColor)(x,h)),k.startSlice=(0,l.toRaphaelColor)((0,l.convertColor)(n,P)),k.endSlice=(0,l.toRaphaelColor)((0,l.convertColor)(n,P))),k.cx===F&&k.cy===M&&k.rx===B&&k.radiusYFactor===T&&k.innerRx===I||(l.hasSVG&&(k.top.FCcolor.cx=F,k.top.FCcolor.cy=M,k.top.FCcolor.r=B,k.top.FCcolor.fx=F-.3*B,k.top.FCcolor.fy=M*****E),k.topBorder.FCcolor.x1=k.backOuter.FCcolor.x1=k.frontOuter.FCcolor.x1=O,k.topBorder.FCcolor.x2=k.backOuter.FCcolor.x2=k.frontOuter.FCcolor.x2=L,k.topInnerBorder.FCcolor.x1=k.backInner.FCcolor.x1=k.frontInner.FCcolor.x1=G,k.topInnerBorder.FCcolor.x2=k.backInner.FCcolor.x2=k.frontInner.FCcolor.x2=R,k.cx=F,k.cy=M,k.rx=B,k.radiusYFactor=T,k.innerRx=I),k},a.allocatePosition=function(){},a.rotate=function(e){var t,a=this,n=a.pointElemStore,o=0,r=n.length;if(!a.hasOnePoint){for(;o<r;o+=1)(t=n[o]._confObject).sAngle+=e,t.eAngle+=e,a._setSliceShape(t);a.refreshDrawing()}},a.removeSlice=function(e){var t,a,n=this,o=n.pointElemStore,r=e._confObject.elements,i=n.slicingWallsArr,l=o.length;for(t=l-1;t>=0;t-=1)o[t]===e&&o.splice(t,1);for(t=(l=i.length)-1;t>=0;t-=1)(a=i[t])!==r.startSlice&&a!==r.frontOuter1&&a!==r.frontOuter&&a!==r.backInner&&a!==r.endSlice||i.splice(t,1);e.hide&&e.hide(),n._slicePool||(n._slicePool=[]),n._slicePool.push(e),n.refreshDrawing()},a.useSliceFromPool=function(){var e,t=this,a=t._slicePool||(t._slicePool=[]),n=t.slicingWallsArr,o=!1;return a.length&&(o=a.shift(),t.pointElemStore.push(o),o.show(),e=o._confObject.elements,n.push(e.startSlice,e.frontOuter1,e.frontOuter),e.backInner&&n.push(e.backInner),n.push(e.endSlice)),o},a._setSliceShape=function(e,t){var a,n,o,r,i,s,c,u,d,p,f,h,g,v,m,b,C,_,D,y,S,w,T,F,M,B,I,O,R,W,Y,U,j,X,Z,J,q,K,$,Q,ee,te,ae,ne=this,oe=function(e,t,a,n,o,r,i,l){return e===a&&t===n?[]:[A,o,r,0,l,i,a,n]},re=e.sAngle,ie=e.eAngle,le=(0,l.normalizeAngle)(re),se=(0,l.normalizeAngle)(ie),ce=ne.isDoughnut,ue=e.radiusYFactor,de=e.cx,pe=e.cy,fe=e.r,he=fe*ue,ge=fe+(l.hasSVG?-1:2),ve=he+(l.hasSVG?-1:2),me=e.innerR||0,be=me*ue,Ce=ne.depth,_e=Ce+pe,De=de+fe,ye=de-fe,Se=de+me,ke=de-me,xe=pe-he,Pe=[k,ke,xe,x,ke,_e+he,N],Ae=e.elements,Ne="path",we=(le+se)/2,Te=le>se;s=de+fe*(n=L(le)),u=de+ge*n,d=pe+ve*(o=E(le)),_=(c=pe+he*o)+Ce,D=de+fe*(r=L(se)),p=de+ge*r,f=pe+ve*(i=E(se)),S=(y=pe+he*i)+Ce,ce?(h=de+me*n,b=(g=pe+be*o)+Ce,v=de+me*r,C=(m=pe+be*i)+Ce,e.startSlice=[k,s,c,x,s,_,h,b,h,g,N],e.endSlice=[k,D,y,x,D,S,v,C,v,m,N]):(e.startSlice=[k,s,c,x,s,_,de,_e,de,pe,N],e.endSlice=[k,D,y,x,D,S,de,_e,de,pe,N]),l.hasSVG?(a=function(e,t){return(e>t?V:0)+t-e}(le,se),e.clipTopPath=ce?[[k,s,c,A,fe,he,0,a>G?1:0,1,D,y,x,v,m,A,me,be,0,a>G?1:0,0,h,g,N]]:[[k,s,c,A,fe,he,0,a>G?1:0,1,D,y,x,de,pe,N]],e.clipOuterFrontPath1=[Pe],e.clipTopBorderPath=[[k,u,d,A,ge,ve,0,a>G?1:0,1,p,f,x,D,y,D,y+1,A,fe,he,0,a>G?1:0,0,s,c+1,x,s,c,N]],re!==ie?le>se?le<G?(e.clipOuterFrontPath=[[k,De,pe,A,fe,he,0,0,1,D,y,P,Ce,A,fe,he,0,0,0,De,pe+Ce,N]],e.clipOuterFrontPath1=[[k,ye,pe,A,fe,he,0,0,0,s,c,P,Ce,A,fe,he,0,0,1,ye,pe+Ce,N]],e.clipOuterBackPath=[[k,De,pe,A,fe,he,0,1,0,ye,pe,P,Ce,A,fe,he,0,1,1,De,pe+Ce,N]],ce&&(e.clipInnerBackPath=[[k,Se,pe,A,me,be,0,1,0,ke,pe,P,Ce,A,me,be,0,1,1,Se,pe+Ce,N]],e.clipInnerFrontPath=[[k,Se,pe,A,me,be,0,0,1,v,m,P,Ce,A,me,be,0,0,0,Se,pe+Ce,N,k,ke,pe,A,me,be,0,0,0,h,g,P,Ce,A,me,be,0,0,1,ke,pe+Ce,N]])):se>G?(e.clipOuterFrontPath=[[k,De,pe,A,fe,he,0,1,1,ye,pe,P,Ce,A,fe,he,0,1,0,De,pe+Ce,N]],e.clipOuterBackPath=[[k,ye,pe,A,fe,he,0,0,1,D,y,P,Ce,A,fe,he,0,0,0,ye,pe+Ce,N,k,De,pe,A,fe,he,0,0,0,s,c,P,Ce,A,fe,he,0,0,1,De,pe+Ce,N]],ce&&(e.clipInnerFrontPath=[[k,Se,pe,A,me,be,0,1,1,ke,pe,P,Ce,A,me,be,0,1,0,Se,pe+Ce,N]],e.clipInnerBackPath=[[k,ke,pe,A,me,be,0,0,1,v,m,P,Ce,A,me,be,0,0,0,ke,pe+Ce,N,k,Se,pe,A,me,be,0,0,0,h,g,P,Ce,A,me,be,0,0,1,Se,pe+Ce,N]])):(e.clipOuterFrontPath=[[k,De,pe,A,fe,he,0,0,1,D,y,P,Ce,A,fe,he,0,0,0,De,pe+Ce,N]],e.clipOuterBackPath=[[k,s,c,A,fe,he,0,0,1,De,pe,P,Ce,A,fe,he,0,0,0,s,_,N]],ce&&(e.clipInnerFrontPath=[[k,Se,pe,A,me,be,0,0,1,v,m,P,Ce,A,me,be,0,0,0,Se,pe+Ce,N]],e.clipInnerBackPath=[[k,h,g,A,me,be,0,0,1,Se,pe,P,Ce,A,me,be,0,0,0,h,b,N]])):le<G?se>G?(e.clipOuterFrontPath=[[k,s,c,A,fe,he,0,0,1,ye,pe,P,Ce,A,fe,he,0,0,0,s,_,N]],e.clipOuterBackPath=[[k,ye,pe,A,fe,he,0,0,1,D,y,P,Ce,A,fe,he,0,0,0,ye,pe+Ce,N]],ce&&(e.clipInnerFrontPath=[[k,h,g,A,me,be,0,0,1,ke,pe,P,Ce,A,me,be,0,0,0,h,b,N]],e.clipInnerBackPath=[[k,ke,pe,A,me,be,0,0,1,v,m,P,Ce,A,me,be,0,0,0,ke,pe+Ce,N]])):(e.clipOuterFrontPath=[[k,s,c,A,fe,he,0,0,1,D,y,P,Ce,A,fe,he,0,0,0,s,_,N]],e.clipOuterBackPath=[Pe],ce&&(e.clipInnerFrontPath=[[k,h,g,A,me,be,0,0,1,v,m,P,Ce,A,me,be,0,0,0,h,b,N]],e.clipInnerBackPath=[Pe])):(e.clipOuterFrontPath=[Pe],e.clipOuterBackPath=[[k,s,c,A,fe,he,0,0,1,D,y,P,Ce,A,fe,he,0,0,0,s,_,N]],ce&&(e.clipInnerFrontPath=[Pe],e.clipInnerBackPath=[[k,h,g,A,me,be,0,0,1,v,m,P,Ce,A,me,be,0,0,0,h,b,N]])):e.clipOuterFrontPath=e.clipOuterBackPath=e.clipInnerBackPath=e.clipInnerFrontPath=[Pe],Ne="litepath",e.clipBottomBorderPath=e.clipTopPath,e.startSlice=[e.startSlice],e.endSlice=[e.endSlice]):(O=ne.moveCmdArr,R=ne.lineCmdArr,W=ne.closeCmdArr,Y=[de,pe],U=[ye,pe],j=[de,xe],X=[De,pe],Z=[de,pe+he],J=[ye,_e],q=[De,_e],K=[ke,pe],$=[Se,pe],Q=[ke,_e],ee=[Se,_e],e.clipOuterFrontPath1=[],re!==ie?(le>se?le<G?(w=oe(s,c,ye,pe,fe,he,1,0),F=oe(ye,pe,De,pe,fe,he,1,0),B=oe(De,pe,D,y,fe,he,1,0),e.clipOuterBackPath=O.concat(U,F,R,q,oe(De,_e,ye,_e,fe,he,0,0),W),e.clipOuterFrontPath1=O.concat([s,c],w,R,J,oe(ye,_e,s,_,fe,he,0,0),W),e.clipOuterFrontPath=O.concat(X,B,R,[D,S],oe(D,S,De,_e,fe,he,0,0),W),e.clipTopBorderPath=O.concat([s,c],w,F,B),ce?(T=oe(v,m,Se,pe,me,be,0,0),M=oe(Se,pe,ke,pe,me,be,0,0),I=oe(ke,pe,h,g,me,be,0,0),e.clipInnerBackPath=O.concat($,M,R,Q,oe(ke,_e,Se,_e,me,be,1,0),W),e.clipInnerFrontPath=O.concat(K,I,R,[h,b],oe(h,b,ke,_e,me,be,1,0),W,O,[v,m],T,R,ee,oe(Se,_e,v,C,me,be,1,0),W),e.clipTopPath=e.clipTopBorderPath.concat(R,[v,m],T,M,I,W),e.clipTopBorderPath=e.clipTopBorderPath.concat(O,[v,m],T,M,I)):e.clipTopPath=e.clipTopBorderPath.concat(R,Y,W)):se>G?(w=oe(s,c,De,pe,fe,he,1,0),F=oe(De,pe,ye,pe,fe,he,1,0),B=oe(ye,pe,D,y,fe,he,1,0),e.clipOuterFrontPath=O.concat(X,F,R,J,oe(ye,_e,De,_e,fe,he,0,0),W),e.clipOuterBackPath=O.concat([s,c],w,R,q,oe(De,_e,s,_,fe,he,0,0),W,O,U,B,R,[D,S],oe(D,S,ye,_e,fe,he,0,0),W),e.clipTopBorderPath=O.concat([s,c],w,F,B),ce?(T=oe(v,m,ke,pe,me,be,0,0),M=oe(ke,pe,Se,pe,me,be,0,0),I=oe(Se,pe,h,g,me,be,0,0),e.clipInnerFrontPath=O.concat(K,M,R,ee,oe(Se,_e,ke,_e,me,be,1,0),W),e.clipInnerBackPath=O.concat($,I,R,[h,b],oe(h,b,Se,_e,me,be,1,0),W,O,[v,m],T,R,Q,oe(ke,_e,v,C,me,be,1,0),W),e.clipTopPath=e.clipTopBorderPath.concat(R,[v,m],T,M,I,W),e.clipTopBorderPath=e.clipTopBorderPath.concat(O,[v,m],T,M,I)):e.clipTopPath=e.clipTopBorderPath.concat(R,Y,W)):(w=oe(s,c,De,pe,fe,he,1,0),F=oe(De,pe,D,y,fe,he,1,0),e.clipOuterFrontPath=O.concat(X,F,R,[D,S],oe(D,S,De,_e,fe,he,0,0),W),e.clipOuterBackPath=O.concat([s,c],w,R,q,oe(De,_e,s,_,fe,he,0,0),W),e.clipTopBorderPath=O.concat([s,c],w,F),ce?(T=oe(v,m,Se,pe,me,be,0,0),M=oe(Se,pe,h,g,me,be,0,0),e.clipInnerFrontPath=O.concat([v,m],T,R,ee,oe(Se,_e,v,C,me,be,1,0),W),e.clipInnerBackPath=O.concat($,M,R,[h,b],oe(h,b,Se,_e,me,be,1,0),W),e.clipTopPath=e.clipTopBorderPath.concat(R,[v,m],T,M,W),e.clipTopBorderPath=e.clipTopBorderPath.concat(O,[v,m],T,M)):e.clipTopPath=e.clipTopBorderPath.concat(R,Y,W)):le<G?se>G?(w=oe(s,c,ye,pe,fe,he,1,0),F=oe(ye,pe,D,y,fe,he,1,0),e.clipOuterBackPath=O.concat(U,F,R,[D,S],oe(D,S,ye,_e,fe,he,0,0),W),e.clipOuterFrontPath=O.concat([s,c],w,R,J,oe(ye,_e,s,_,fe,he,0,0),W),e.clipTopBorderPath=O.concat([s,c],w,F),ce?(T=oe(v,m,ke,pe,me,be,0,0),M=oe(ke,pe,h,g,me,be,0,0),e.clipInnerBackPath=O.concat([v,m],T,R,Q,oe(ke,_e,v,C,me,be,1,0),W),e.clipInnerFrontPath=O.concat(K,M,R,[h,b],oe(h,b,ke,_e,me,be,1,0),W),e.clipTopPath=e.clipTopBorderPath.concat(R,[v,m],T,M,W),e.clipTopBorderPath=e.clipTopBorderPath.concat(O,[v,m],T,M)):e.clipTopPath=e.clipTopBorderPath.concat(R,Y,W)):(w=oe(s,c,D,y,fe,he,1,0),e.clipOuterBackPath=O.concat([s,c]),e.clipTopBorderPath=e.clipOuterBackPath.concat(w),e.clipOuterFrontPath=e.clipTopBorderPath.concat(R,[D,S],oe(D,S,s,_,fe,he,0,0),W),ce?(T=oe(v,m,h,g,me,be,0,0),e.clipInnerBackPath=O.concat([v,m]),e.clipTopPath=e.clipTopBorderPath.concat(R,[v,m],T,W),e.clipTopBorderPath=e.clipTopBorderPath.concat(O,[v,m],T),e.clipInnerFrontPath=e.clipInnerBackPath.concat(T,R,[h,b],oe(h,b,v,C,me,be,1,0),W)):e.clipTopPath=e.clipTopBorderPath.concat(R,Y,W)):(w=oe(s,c,D,y,fe,he,1,0),e.clipOuterFrontPath=O.concat([s,c]),e.clipTopBorderPath=e.clipOuterFrontPath.concat(w),e.clipOuterBackPath=e.clipTopBorderPath.concat(R,[D,S],oe(D,S,s,_,fe,he,0,0),W),ce?(T=oe(v,m,h,g,me,be,0,0),e.clipInnerFrontPath=O.concat([v,m]),e.clipTopPath=e.clipTopBorderPath.concat(R,[v,m],T,W),e.clipTopBorderPath=e.clipTopBorderPath.concat(e.clipInnerFrontPath,T),e.clipInnerBackPath=e.clipInnerFrontPath.concat(T,R,[h,b],oe(h,b,v,C,me,be,1,0),W)):e.clipTopPath=e.clipTopBorderPath.concat(R,Y,W)),w=O.concat(U,R,X),T=O.concat(j,R,Z),e.clipTopPath=e.clipTopPath.concat(w,T),e.clipOuterFrontPath=e.clipOuterFrontPath.concat(w),e.clipOuterFrontPath1=e.clipOuterFrontPath1.concat(w),e.clipOuterBackPath=e.clipOuterBackPath.concat(w),ce&&(T=O.concat(K,R,$),e.clipInnerFrontPath=e.clipInnerFrontPath.concat(T),e.clipInnerBackPath=e.clipInnerBackPath.concat(T))):(e.clipTopPath=e.clipOuterFrontPath=e.clipOuterBackPath=[],ce&&(e.clipInnerFrontPath=e.clipInnerBackPath=[])),e.clipBottomBorderPath=e.clipTopBorderPath),t||(Ae.startSlice._conf.index=le,Ae.endSlice._conf.index=se,Ae.backOuter._conf.index=ae=Te&&(le<=H||se>H)||le<=H&&se>H?H:le>G?le:se,Ae.frontOuter._conf.index=te=se<=z?se:le>se||le<=z?z:le,Ae.frontOuter1._conf.index=le,Ae.frontOuter1._conf.cIndex=G,le>se?(Ae.backOuter._conf.cIndex=le<H?H:V,Ae.startSlice._conf.cIndex=le<G?(le+G)/2:(le+V)/2,Ae.endSlice._conf.cIndex=Ae.frontOuter._conf.cIndex=0):Ae.backOuter._conf.cIndex=Ae.startSlice._conf.cIndex=Ae.endSlice._conf.cIndex=Ae.frontOuter._conf.cIndex=we,a>G?Ae.frontOuter1.show().attr(Ne,e.clipOuterFrontPath1):Ae.frontOuter1.hide(),e.thisElement._attr(Ne,e.clipTopPath),Ae.bottom.attr(Ne,e.clipTopPath),Ae.bottomBorder.attr(Ne,e.clipBottomBorderPath),Ae.topBorder&&Ae.topBorder.attr(Ne,e.clipTopBorderPath),Ae.frontOuter.attr(Ne,e.clipOuterFrontPath),Ae.backOuter.attr(Ne,e.clipOuterBackPath),ce&&(Ae.backInner.attr(Ne,e.clipInnerBackPath),Ae.frontInner.attr(Ne,e.clipInnerFrontPath),Ae.backInner._conf.index=ae,Ae.frontInner._conf.index=te,le>se?(Ae.backInner._conf.cIndex=V,Ae.frontInner._conf.cIndex=0):Ae.backInner._conf.cIndex=Ae.frontInner._conf.cIndex=we),ne.hasOnePoint?(Ae.startSlice.hide(),Ae.endSlice.hide()):(Ae.startSlice.attr(Ne,e.startSlice).show(),Ae.endSlice.attr(Ne,e.endSlice).show()))},a._setSliceCosmetics=function(e){var t,a,n=e.thisElement,o=e.showBorderEffect,r=e.elements,i=(0,l.convertColor)(e.borderColor,(0,l.pluckNumber)(e.borderAlpha,e.alpha)),s=e.borderWidth;e.color&&(e.color=e.color.color?e.color.color:e.color,t=this._parseSliceColor(e.color,e.alpha,e),l.hasSVG?(a={fill:(0,l.toRaphaelColor)(t.top),"stroke-width":0},o?r.topBorder.show().attr({fill:(0,l.toRaphaelColor)(t.topBorder),"stroke-width":0}):(r.topBorder.hide(),a.stroke=i,a["stroke-width"]=s),n._attr(a)):(n._attr({fill:(0,l.toRaphaelColor)(t.top),"stroke-width":0}),r.topBorder.attr({stroke:i,"stroke-width":s})),r.bottom.attr({fill:(0,l.toRaphaelColor)(t.bottom)}),r.bottomBorder.attr({stroke:i,"stroke-width":s}),r.frontOuter.attr({fill:(0,l.toRaphaelColor)(t.frontOuter)}),r.frontOuter1.attr({fill:(0,l.toRaphaelColor)(t.frontOuter)}),r.backOuter.attr({fill:(0,l.toRaphaelColor)(t.backOuter)}),r.startSlice.attr({fill:(0,l.toRaphaelColor)(t.startSlice),stroke:i,"stroke-width":s}),r.endSlice.attr({fill:(0,l.toRaphaelColor)(t.endSlice),stroke:i,"stroke-width":s}),this.isDoughnut&&(r.frontInner.attr({fill:(0,l.toRaphaelColor)(t.frontInner)}),r.backInner.attr({fill:(0,l.toRaphaelColor)(t.backInner)})))},t}(s.ComponentInterface),me=ge;t["default"]=me},604:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=function(e){var t;if((0,i.componentFactory)(e,o["default"],"canvas",e.config.showVolumeChart?2:1),t=e.getChildren("canvas"))for(var a=0,n=t.length;a<n;a++)t[a].configure(),(0,i.componentFactory)(t[a],r["default"],"axisRefVisualCartesian")};var o=n(a(605)),r=n(a(588)),i=a(274)},1083:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=function(e){var t,a,n,c,u,d,p,f,h,g,v=e.getFromEnv("dataSource").dataset,m=e.getChildren().canvas[0].getChildren("vCanvas"),b=m[0],C=m[1],_=e.getFromEnv("chartConfig").isdual,D={vCanvasDatasetsDef0:{},vCanvasDatasetsDef1:{}},y={vCanvas0:{},vCanvas1:{}};v||e.setChartMessage();for(a=0;a<v.length;a++)"s"===(f=(t=v[a]).parentyaxis||"").toLowerCase()&&_?(p=(0,r.pluck)(t.renderas,e.config.sDefaultDatasetType),h=C,g=y.vCanvas1,c=D.vCanvasDatasetsDef1):(p=(0,r.pluck)(t.renderas,e.config.defaultDatasetType),h=b,g=y.vCanvas0,c=D.vCanvasDatasetsDef0),p=e.getDSType(p,"s"===f.toLowerCase()),(d=e.getDSGroupdef(p,f))&&((0,r.componentFactory)(h,o["default"],s),g[(n=h.getChildren(s)[0]).getName()]=!0,(0,r.componentFactory)(n,d,l),g[d.getName().toLowerCase()]=!0),g[p.toLowerCase()]=!0,c[p]?(c[p].conf.push(t),c[p].indices.push(a)):(c[p]={},c[p].indices=[a],c[p].classDef=e.getDSdef(p),c[p].conf=[t],c[p].pYAxis=f.toLowerCase(),c[p].parent=d?n.getChildren(l)[0]:h);for(var S in D)if(D.hasOwnProperty(S))for(p in c=D[S])c.hasOwnProperty(p)&&("group"===(u=c[p]).parent.getType()&&u.parent.configure(u.conf),(0,r.datasetFactory)(u.parent,u.classDef,"dataset_"+p,u.conf.length,u.conf,u.indices));(0,i.removeComponents)(m[0],Object.keys(y.vCanvas0)),(0,i.removeComponents)(m[1],Object.keys(y.vCanvas1))};var o=n(a(609)),r=a(274),i=a(996),l="multiseriesColumnManager",s="multiseriesColumnManager3D"},1059:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=function(e){var t,a,n,i,l,s,c=e.getFromEnv("dataSource").dataset,u=c&&c.length,d=0,p=e.getChildren("canvas")[0].getChildren("vCanvas")[0],f=[];if(!c)return void e.setChartMessage();for((0,o.componentFactory)(p,r["default"],"multiSeriesGroup_bar"),s=p.getChildren("multiSeriesGroup_bar")[0],t=0;t<u;t++)f.push(c[t].dataset);for((0,o.componentFactory)(s,e.getDSGroupdef(),"stackedGroup_bar",u,f),l=s.getChildren("stackedGroup_bar"),t=0;t<l.length;t++)if(!0!==l[t].getState("removed")){if(!(n=c[t].dataset))return void e.setChartMessage();a=n&&n.length,i=Array(a).fill(d).map((function(e,t){return e+t})),(0,o.datasetFactory)(l[t],e.getDSdef(),"dataset_bar",a,n,i),d+=a}e.config._lastDatasetIndex=i[i.length-1]};var o=a(274),r=n(a(1060))},1069:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=function(e){var t,a,n,i,l,s=e.getFromEnv("dataSource").dataset,c=e.getChildren().canvas[0],u=t=c.getChildren("vCanvas")[0],d=e.config.defaultDatasetType||"";s||e.setChartMessage();(0,o.componentFactory)(t,r["default"],"datasetGroup_"+d),l=t.getChildren("datasetGroup_"+d)[0],a=e.getDSGroupdef(),(0,o.componentFactory)(l,a,"datasetGroup_"+d,1,[{}]),(i=l.getChildren("datasetGroup_"+d))&&(u=i[0]),n=e.getDSdef(),(0,o.datasetFactory)(u,n,"dataset",s.length,s)};var o=a(274),r=n(a(609))},1e3:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=function(e){var t,a,n,l=e.getFromEnv("chart-attrib"),s=(0,i.pluckNumber)(l.showcumulativeline,1),c=e.getChildren("canvas")[0],u=c.getChildren("axisRefVisualCartesian")[0],d={zoomable:!0,pannable:!0},p=e._feedAxesRawData(),f=function(){return u.asyncDraw()};(0,i.componentFactory)(e,r["default"],"xAxis",1,p.xAxisConf),t=e.getChildren(),n=t.xAxis[0],u.setLinkedItem(n.getId(),n),c.attachAxis(n,!1,e.zoomX?d:{}),n.setLinkedItem("canvas",c),(0,i.componentFactory)(e,o["default"],"yAxis",s?2:1,p.yAxisConf),(a=e.getChildren("yAxis"))&&a[1]&&a[1].setAxisConfig({isPercent:!0,drawLabels:!0,drawPlotLines:!0,drawAxisName:!0,drawAxisLine:!0,drawPlotBands:!0,drawTrendLines:!0,drawTrendLabels:!0}),a.forEach((function(t){!0!==t.getState("removed")?(t.setLinkedItem("canvas",c),u.setLinkedItem(t.getId(),t),c.attachAxis(t,!0,e.zoomY?d:{}),u.setLinkedItem(t.getId(),t),u.addExtEventListener("visiblerangeset",f,t)):c.detachAxis(t)})),e._setCategories()};var o=n(a(537)),r=n(a(585)),i=a(274)},1001:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=function(e){var t,a,n,i=e.getChildren(),l=e.getFromEnv("chart-attrib"),s=i.canvas[0].getChildren("vCanvas"),c=e.getFromEnv("dataSource"),u=c.dataset,d=(0,r.pluckNumber)(l.showcumulativeline,1),p=c.data||u&&u[0].data;if(n={data:p},!(p&&0!==p.length))return void e.setChartMessage();t=e.getDSdef("column"),e.config.is3D&&((0,r.componentFactory)(s[0],o["default"],"datasetGroup_column"),a=s[0].getChildren("datasetGroup_column")[0]);(0,r.datasetFactory)(a||s[0],t,"dataset",1,[n]),d&&(t=e.getDSdef("line"),(0,r.datasetFactory)(s[1],t,"dataset",1,[n],[1]))};var o=n(a(609)),r=a(274)},977:(e,t,a)=>{t.__esModule=!0,t["default"]=function(e){var t,a,r=e.getFromEnv("dataSource"),i=r.dataset,l=r.data||i&&i[0].data;if(a=o(l),!(l&&0!==l.length))return void e.setChartMessage();t=e.getDSdef(),(0,n.datasetFactory)(e,t,"dataset",1,[a])};var n=a(274),o=function(e){var t=[];return(0,n.fcEach)(e,(function(e){"true"!==e.vline&&!0!==e.vline&&1!==e.vline&&"1"!==e.vline&&t.push(e)})),{catData:[],data:t}}},608:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=function(e){var t,a,n,l=e.getChildren().canvas[0].getChildren("vCanvas")[0],s=e.getFromEnv("dataSource"),c=s.dataset,u=e.config.defaultDatasetType||"",d=s.data||c&&c[0].data;if(a=i(d),!(d&&0!==d.length))return void e.setChartMessage();(0,o.componentFactory)(l,r["default"],"datasetGroup_"+u),n=l.getChildren("datasetGroup_"+u)[0],t=e.getDSdef(),(0,o.datasetFactory)(n,t,"dataset",1,[a])};var o=a(274),r=n(a(609)),i=function(e){var t=[];return(0,o.fcEach)(e,(function(e){"true"!==e.vline&&!0!==e.vline&&1!==e.vline&&"1"!==e.vline&&t.push(e)})),{data:t}}},607:(e,t,a)=>{t.__esModule=!0,t["default"]=function(e){var t,a=Math.atan2,o="fill",r="path";e.define&&e.define([{name:"cubepath",cubepath:function(){var a,o,i,l,s=this,c={"stroke-linejoin":"round","shape-rendering":"precision",stroke:"none"},u=arguments,d=u.length-1,p=u[d],f=function(e,a,o,i,l,s){var c=this,u=c._.cubetop,d=c._.cubeside,p=e,f=a,h=o,g=i,v=l,m=s;return"object"==typeof p||p===t&&f===t&&h===t&&g===t&&v===t&&m===t||(p=(0,n.pluckNumber)(p,c.attrs.x,0),f=(0,n.pluckNumber)(f,c.attrs.y,0),h=(0,n.pluckNumber)(h,c.attrs.width,0),g=(0,n.pluckNumber)(g,c.attrs.height,0),v=(0,n.pluckNumber)(v,c.attrs.xDepth,0),m=(0,n.pluckNumber)(m,c.attrs.yDepth,0),c.attrs.x=p,c.attrs.y=f,c.attrs.width=h,c.attrs.height=g,c.attrs.xDepth=v,c.attrs.yDepth=m,c._attr(r,["M",p+h,f,"l",0,g,-h,0,0,-g,"z"]),u.attr(r,["M",p,f,"l",1,1,h-1,0,0,-1,v,-m,-h,0,"z"]),d.attr(r,["M",p+h-1,f+1,"l",0,g-1,1,0,v,-m,0,-g,-v,m])),this},h=function(e,t,a,n){var o=this,r=o._.cubetop,i=o._.cubeside;return o.dropshadow&&(r.dropshadow(e,-t,a,n),i.dropshadow(e,-t,a,n)),!1};for(i in p&&p.constructor===e.el.constructor?u[d]=t:p=t,a=s.path(c,p),o=s.path(c,p),(l=s.path(c,p))._.cubetop=a.follow(l,t,"before"),l._.cubeside=o.follow(l,t,"before"),e.fn.cubepath.ca)l.ca[i]=e.fn.cubepath.ca[i];return l._attr=l.attr,l._shadow=l.shadow,l.attr=function(e,a){var n="object"==typeof e,o=a;return n&&(e.cubepath?o=[].concat(e.cubepath):((o=[]).push(e.x),o.push(e.y),o.push(e.width),o.push(e.height),o.push(e.xDepth),o.push(e.yDepth)),e.noGradient!==t&&(l.attrs.noGradient=e.noGradient)),e===t&&o===t?this.attrs:o===t?this.attrs[e]:(n?f.apply(this,o):"drop-shadow"===e&&h.apply(this,[].concat(o)),l._attr(e),this)},l.appendTo=function(e){e.appendChild(l._.cubetop),e.appendChild(l._.cubeside),e.appendChild(l)},"object"==typeof u[0]?l.attr(u[0]):f.apply(l,[u[0],u[1],u[2],u[3],u[4],u[5]])},fn:{_getBBox2:function(){var e=this,t=e._.cubeside.getBBox(),a=e._.cubetop.getBBox(),n=e.getBBox();return{x:n.x+a.height,y:n.y-t.width,width:n.width,height:n.height}},shadow:function(){return this._.cubeside.shadow.apply(this._.cubeside,arguments),this._.cubetop.shadow.apply(this._.cubetop,arguments),this._shadow.apply(this,arguments)}},ca:{"stroke-linejoin":function(){return{"stroke-linejoin":"round"}},fill:function(n,r){var i,l=this,s=l._.cubetop,c=l._.cubeside,u=l._attr("cubepath")||[0,0,0,0,0,0],d=n,p=r,f=u[2],h=u[4],g=u[5];return p===t&&(p=l._attr("noGradient")),"object"==typeof(d=e.color(d))&&(d=i="opacity"in d?"rgba("+[d.r,d.g,d.b,d.opacity]+")":"rgb("+[d.r,d.g,d.b]+")"),p?(l._attr(o,d),s.attr(o,e.tintshade(d,-.78).rgba),c.attr(o,e.tintshade(d,-.65).rgba)):(l._attr(o,[270,e.tintshade(i,.55).rgba,e.tintshade(i,-.65).rgba].join("-")),c.attr(o,[270,e.tintshade(i,-.75).rgba,e.tintshade(i,-.35).rgba].join("-")),s.attr(o,[45+e.deg(a(g,h+f)),e.tintshade(i,-.78).rgba,e.tintshade(i,.22).rgba].join("-"))),!1}}}])};var n=a(274)},514:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(515));t.Column2D=o["default"];var r=n(a(601));t.Column3D=r["default"];var i=n(a(614));t.Line=i["default"];var l=n(a(620));t.Area=l["default"];var s=n(a(622));t.Bar2D=s["default"];var c=n(a(967));t.Bar3D=c["default"];var u=n(a(972));t.Pie2D=u["default"];var d=n(a(978));t.Pie3D=d["default"];var p=n(a(985));t.Doughnut2D=p["default"];var f=n(a(987));t.Doughnut3D=f["default"];var h=n(a(991));t.Pareto2D=h["default"];var g=n(a(1002));t.Pareto3D=g["default"];var v=n(a(1005));t.ScrollCombiDy2D=v["default"];var m=n(a(1016));t.ScrollCombi2D=m["default"];var b=n(a(1021));t.ScrollStackedColumn2D=b["default"];var C=n(a(1025));t.ScrollMSStackedColumn2D=C["default"];var _=n(a(1029));t.ScrollMSStackedColumn2dLineDY=_["default"];var D=n(a(1033));t.ScrollStackedBar2D=D["default"];var y=n(a(1036));t.ScrollArea2D=y["default"];var S=n(a(1037));t.ScrollLine2D=S["default"];var k=n(a(1039));t.ScrollColumn2D=k["default"];var x=n(a(1040));t.ScrollBar2D=x["default"];var P=n(a(1041));t.Bubble=P["default"];var A=n(a(1054));t.Scatter=A["default"];var N=n(a(1055));t.MSStackedColumn2D=N["default"];var w=n(a(1056));t.MSStackedBar2D=w["default"];var T=n(a(1061));t.StackedArea2D=T["default"];var F=n(a(1065));t.StackedBar3D=F["default"];var M=n(a(1070));t.StackedBar2D=M["default"];var B=n(a(1072));t.StackedColumn3D=B["default"];var E=n(a(1076));t.StackedColumn2D=E["default"];var I=n(a(1078));t.MSStackedColumn2DLineDy=I["default"];var L=n(a(1079));t.StackedColumn3DLineDy=L["default"];var O=n(a(1084));t.MSColumn3DLineDy=O["default"];var R=n(a(1085));t.MSCombidy2D=R["default"];var G=n(a(1086));t.MSCombidy3D=G["default"];var V=n(a(1088));t.StackedColumn3DLine=V["default"];var z=n(a(1091));t.StackedColumn2DLine=z["default"];var H=n(a(1094));t.MSColumnLine3D=H["default"];var W=n(a(1096));t.MSCombi3D=W["default"];var Y=n(a(1097));t.MSCombi2D=Y["default"];var U=n(a(1098));t.Marimekko=U["default"];var j=n(a(1103));t.MSArea=j["default"];var X=n(a(1104));t.MSBar3D=X["default"];var Z=n(a(1105));t.MSBar2D=Z["default"];var J=n(a(1106));t.MSLine=J["default"];var q=n(a(1108));t.MSColumn3D=q["default"];var K=n(a(1109));t.MSColumn2D=K["default"];var $=n(a(1110));t.Spline=$["default"];var Q=n(a(1113));t.Splinearea=Q["default"];var ee=n(a(1115));t.Msspline=ee["default"];var te=n(a(1118));t.MSSplineDy=te["default"];var ae=n(a(1120));t.Mssplinearea=ae["default"];var ne=n(a(1122));t.StackedColumn2DLineDy=ne["default"];var oe=n(a(1124));t.StackedArea2DLineDy=oe["default"];var re={name:"charts",type:"package",requiresFusionCharts:!0,extension:function(e){e.addDep(o["default"]),e.addDep(r["default"]),e.addDep(i["default"]),e.addDep(l["default"]),e.addDep(s["default"]),e.addDep(c["default"]),e.addDep(u["default"]),e.addDep(d["default"]),e.addDep(p["default"]),e.addDep(f["default"]),e.addDep(h["default"]),e.addDep(g["default"]),e.addDep(v["default"]),e.addDep(m["default"]),e.addDep(b["default"]),e.addDep(C["default"]),e.addDep(_["default"]),e.addDep(D["default"]),e.addDep(y["default"]),e.addDep(S["default"]),e.addDep(k["default"]),e.addDep(x["default"]),e.addDep(P["default"]),e.addDep(A["default"]),e.addDep(N["default"]),e.addDep(w["default"]),e.addDep(T["default"]),e.addDep(F["default"]),e.addDep(M["default"]),e.addDep(B["default"]),e.addDep(E["default"]),e.addDep(I["default"]),e.addDep(L["default"]),e.addDep(ne["default"]),e.addDep(oe["default"]),e.addDep(O["default"]),e.addDep(R["default"]),e.addDep(G["default"]),e.addDep(V["default"]),e.addDep(z["default"]),e.addDep(H["default"]),e.addDep(W["default"]),e.addDep(Y["default"]),e.addDep(U["default"]),e.addDep(j["default"]),e.addDep(X["default"]),e.addDep(Z["default"]),e.addDep(J["default"]),e.addDep(q["default"]),e.addDep(K["default"]),e.addDep($["default"]),e.addDep(Q["default"]),e.addDep(te["default"]),e.addDep(ee["default"]),e.addDep(ae["default"])}};t["default"]=re},620:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(621))["default"];t["default"]=o},622:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(623))["default"];t["default"]=o},967:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(968))["default"];t["default"]=o},1041:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1042))["default"];t["default"]=o},515:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(516))["default"];t["default"]=o},601:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(602))["default"];t["default"]=o},985:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(986))["default"];t["default"]=o},987:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(988))["default"];t["default"]=o},614:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(615))["default"];t["default"]=o},1098:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1099))["default"];t["default"]=o},1103:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1063))["default"];t["default"]=o},1105:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1058))["default"];t["default"]=o},1104:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1067))["default"];t["default"]=o},1109:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1020))["default"];t["default"]=o},1108:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1074))["default"];t["default"]=o},1084:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1081))["default"];t["default"]=o},1094:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1095))["default"];t["default"]=o},1097:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1093))["default"];t["default"]=o},1096:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1090))["default"];t["default"]=o},1085:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1007))["default"];t["default"]=o},1086:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1087))["default"];t["default"]=o},1106:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1107))["default"];t["default"]=o},1056:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1057))["default"];t["default"]=o},1055:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1027))["default"];t["default"]=o},1078:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1031))["default"];t["default"]=o},991:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(992))["default"];t["default"]=o},1002:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1003))["default"];t["default"]=o},972:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(973))["default"];t["default"]=o},978:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(979))["default"];t["default"]=o},1054:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1043))["default"];t["default"]=o},1036:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1018))["default"];t["default"]=o},1040:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1035))["default"];t["default"]=o},1039:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1019))["default"];t["default"]=o},1016:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1017))["default"];t["default"]=o},1005:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1006))["default"];t["default"]=o},1037:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1038))["default"];t["default"]=o},1025:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1026))["default"];t["default"]=o},1029:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1030))["default"];t["default"]=o},1033:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1034))["default"];t["default"]=o},1021:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1022))["default"];t["default"]=o},1061:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1062))["default"];t["default"]=o},1124:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1125))["default"];t["default"]=o},1070:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1071))["default"];t["default"]=o},1065:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1066))["default"];t["default"]=o},1076:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1077))["default"];t["default"]=o},1091:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1092))["default"];t["default"]=o},1122:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1123))["default"];t["default"]=o},1072:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1073))["default"];t["default"]=o},1088:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1089))["default"];t["default"]=o},1079:(e,t,a)=>{var n=a(269);t.__esModule=!0,t["default"]=void 0;var o=n(a(1080))["default"];t["default"]=o}}])}));
//# sourceMappingURL=http://localhost:3052/4.1.2/map/eval/fusioncharts.charts.js.map