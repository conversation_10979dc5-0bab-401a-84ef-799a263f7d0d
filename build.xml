<?xml version="1.0" encoding="UTF-8"?>

<project name="datixweb" default="lint">

	<target name="all">
		<phingcall target="lint" />
		<phingcall target="doc" />
	</target>

	<!--
		Create client dir
	-->
	<target name="mkClientDir">
		<echo msg="Creating client dir" />
		<mkdir dir="client" />
		<mkdir dir="client/docs" />
	</target>

	<!--
		Report changed files
		Not really being used right now...
	-->
	<target name="changedfiles">
		<echo msg="Reporting changed files" />
		<exec command="php tests/reports/listChangedFiles.php" dir="." checkreturn="true" />
	</target>

	<!--
		Run lint on php files
	-->
	<target name="lint" depends="install">
		<echo msg="Running lint on php files" />
        <exec command="php tests/vendor/jakub-onderka/php-parallel-lint/parallel-lint.php --exclude tests/vendor ." dir="." checkreturn="true" />
	</target>

	<!--
		Generate api documentation
		You need to install apigen (http://apigen.org/)
		TODO: Move this to composer.json
	-->
	<target name="doc">
		<echo msg="Generating documentation for php classes" />
		<apigen source="src" destination="app_docs" title="Datixweb documentation" todo="true" deprecated="true" />
	</target>

	<!--
		Install dependencies for testing
	-->
	<target name="install" depends="mkClientDir">
		<echo msg="Installing stuff required for testing" />
		<exec command="php composer.phar install" dir="tests" checkreturn="true" />
	</target>

</project>
