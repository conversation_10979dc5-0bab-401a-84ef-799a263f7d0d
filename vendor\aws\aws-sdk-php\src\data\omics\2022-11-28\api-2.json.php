<?php
// This file was auto-generated from sdk-root/src/data/omics/2022-11-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-11-28', 'endpointPrefix' => 'omics', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Omics', 'serviceId' => 'Omics', 'signatureVersion' => 'v4', 'signingName' => 'omics', 'uid' => 'omics-2022-11-28', ], 'operations' => [ 'AbortMultipartReadSetUpload' => [ 'name' => 'AbortMultipartReadSetUpload', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sequencestore/{sequenceStoreId}/upload/{uploadId}/abort', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AbortMultipartReadSetUploadRequest', ], 'output' => [ 'shape' => 'AbortMultipartReadSetUploadResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'NotSupportedOperationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'AcceptShare' => [ 'name' => 'AcceptShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/share/{shareId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptShareRequest', ], 'output' => [ 'shape' => 'AcceptShareResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'BatchDeleteReadSet' => [ 'name' => 'BatchDeleteReadSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/readset/batch/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteReadSetRequest', ], 'output' => [ 'shape' => 'BatchDeleteReadSetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], 'idempotent' => true, ], 'CancelAnnotationImportJob' => [ 'name' => 'CancelAnnotationImportJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/import/annotation/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelAnnotationImportRequest', ], 'output' => [ 'shape' => 'CancelAnnotationImportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], 'idempotent' => true, ], 'CancelRun' => [ 'name' => 'CancelRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/run/{id}/cancel', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CancelRunRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'CancelVariantImportJob' => [ 'name' => 'CancelVariantImportJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/import/variant/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelVariantImportRequest', ], 'output' => [ 'shape' => 'CancelVariantImportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], 'idempotent' => true, ], 'CompleteMultipartReadSetUpload' => [ 'name' => 'CompleteMultipartReadSetUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/upload/{uploadId}/complete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CompleteMultipartReadSetUploadRequest', ], 'output' => [ 'shape' => 'CompleteMultipartReadSetUploadResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'NotSupportedOperationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'storage-', ], ], 'CreateAnnotationStore' => [ 'name' => 'CreateAnnotationStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/annotationStore', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAnnotationStoreRequest', ], 'output' => [ 'shape' => 'CreateAnnotationStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'CreateAnnotationStoreVersion' => [ 'name' => 'CreateAnnotationStoreVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/annotationStore/{name}/version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAnnotationStoreVersionRequest', ], 'output' => [ 'shape' => 'CreateAnnotationStoreVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'CreateMultipartReadSetUpload' => [ 'name' => 'CreateMultipartReadSetUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/upload', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMultipartReadSetUploadRequest', ], 'output' => [ 'shape' => 'CreateMultipartReadSetUploadResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'NotSupportedOperationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'CreateReferenceStore' => [ 'name' => 'CreateReferenceStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/referencestore', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateReferenceStoreRequest', ], 'output' => [ 'shape' => 'CreateReferenceStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'CreateRunGroup' => [ 'name' => 'CreateRunGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/runGroup', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRunGroupRequest', ], 'output' => [ 'shape' => 'CreateRunGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'CreateSequenceStore' => [ 'name' => 'CreateSequenceStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSequenceStoreRequest', ], 'output' => [ 'shape' => 'CreateSequenceStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'CreateShare' => [ 'name' => 'CreateShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/share', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateShareRequest', ], 'output' => [ 'shape' => 'CreateShareResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'CreateVariantStore' => [ 'name' => 'CreateVariantStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/variantStore', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVariantStoreRequest', ], 'output' => [ 'shape' => 'CreateVariantStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'CreateWorkflow' => [ 'name' => 'CreateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/workflow', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateWorkflowRequest', ], 'output' => [ 'shape' => 'CreateWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'DeleteAnnotationStore' => [ 'name' => 'DeleteAnnotationStore', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/annotationStore/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAnnotationStoreRequest', ], 'output' => [ 'shape' => 'DeleteAnnotationStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], 'idempotent' => true, ], 'DeleteAnnotationStoreVersions' => [ 'name' => 'DeleteAnnotationStoreVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/annotationStore/{name}/versions/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAnnotationStoreVersionsRequest', ], 'output' => [ 'shape' => 'DeleteAnnotationStoreVersionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], 'idempotent' => true, ], 'DeleteReference' => [ 'name' => 'DeleteReference', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/referencestore/{referenceStoreId}/reference/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteReferenceRequest', ], 'output' => [ 'shape' => 'DeleteReferenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], 'idempotent' => true, ], 'DeleteReferenceStore' => [ 'name' => 'DeleteReferenceStore', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/referencestore/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteReferenceStoreRequest', ], 'output' => [ 'shape' => 'DeleteReferenceStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], 'idempotent' => true, ], 'DeleteRun' => [ 'name' => 'DeleteRun', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/run/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteRunRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], 'idempotent' => true, ], 'DeleteRunGroup' => [ 'name' => 'DeleteRunGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/runGroup/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteRunGroupRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], 'idempotent' => true, ], 'DeleteSequenceStore' => [ 'name' => 'DeleteSequenceStore', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sequencestore/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSequenceStoreRequest', ], 'output' => [ 'shape' => 'DeleteSequenceStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], 'idempotent' => true, ], 'DeleteShare' => [ 'name' => 'DeleteShare', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/share/{shareId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteShareRequest', ], 'output' => [ 'shape' => 'DeleteShareResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], 'idempotent' => true, ], 'DeleteVariantStore' => [ 'name' => 'DeleteVariantStore', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/variantStore/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVariantStoreRequest', ], 'output' => [ 'shape' => 'DeleteVariantStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], 'idempotent' => true, ], 'DeleteWorkflow' => [ 'name' => 'DeleteWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workflow/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteWorkflowRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], 'idempotent' => true, ], 'GetAnnotationImportJob' => [ 'name' => 'GetAnnotationImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/import/annotation/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnnotationImportRequest', ], 'output' => [ 'shape' => 'GetAnnotationImportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'GetAnnotationStore' => [ 'name' => 'GetAnnotationStore', 'http' => [ 'method' => 'GET', 'requestUri' => '/annotationStore/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnnotationStoreRequest', ], 'output' => [ 'shape' => 'GetAnnotationStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'GetAnnotationStoreVersion' => [ 'name' => 'GetAnnotationStoreVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/annotationStore/{name}/version/{versionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnnotationStoreVersionRequest', ], 'output' => [ 'shape' => 'GetAnnotationStoreVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'GetReadSet' => [ 'name' => 'GetReadSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/sequencestore/{sequenceStoreId}/readset/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReadSetRequest', ], 'output' => [ 'shape' => 'GetReadSetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'RangeNotSatisfiableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'storage-', ], ], 'GetReadSetActivationJob' => [ 'name' => 'GetReadSetActivationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/sequencestore/{sequenceStoreId}/activationjob/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReadSetActivationJobRequest', ], 'output' => [ 'shape' => 'GetReadSetActivationJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetReadSetExportJob' => [ 'name' => 'GetReadSetExportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/sequencestore/{sequenceStoreId}/exportjob/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReadSetExportJobRequest', ], 'output' => [ 'shape' => 'GetReadSetExportJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetReadSetImportJob' => [ 'name' => 'GetReadSetImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/sequencestore/{sequenceStoreId}/importjob/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReadSetImportJobRequest', ], 'output' => [ 'shape' => 'GetReadSetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetReadSetMetadata' => [ 'name' => 'GetReadSetMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/sequencestore/{sequenceStoreId}/readset/{id}/metadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReadSetMetadataRequest', ], 'output' => [ 'shape' => 'GetReadSetMetadataResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetReference' => [ 'name' => 'GetReference', 'http' => [ 'method' => 'GET', 'requestUri' => '/referencestore/{referenceStoreId}/reference/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReferenceRequest', ], 'output' => [ 'shape' => 'GetReferenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'RangeNotSatisfiableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'storage-', ], ], 'GetReferenceImportJob' => [ 'name' => 'GetReferenceImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/referencestore/{referenceStoreId}/importjob/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReferenceImportJobRequest', ], 'output' => [ 'shape' => 'GetReferenceImportJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetReferenceMetadata' => [ 'name' => 'GetReferenceMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/referencestore/{referenceStoreId}/reference/{id}/metadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReferenceMetadataRequest', ], 'output' => [ 'shape' => 'GetReferenceMetadataResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetReferenceStore' => [ 'name' => 'GetReferenceStore', 'http' => [ 'method' => 'GET', 'requestUri' => '/referencestore/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReferenceStoreRequest', ], 'output' => [ 'shape' => 'GetReferenceStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetRun' => [ 'name' => 'GetRun', 'http' => [ 'method' => 'GET', 'requestUri' => '/run/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRunRequest', ], 'output' => [ 'shape' => 'GetRunResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'GetRunGroup' => [ 'name' => 'GetRunGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/runGroup/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRunGroupRequest', ], 'output' => [ 'shape' => 'GetRunGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'GetRunTask' => [ 'name' => 'GetRunTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/run/{id}/task/{taskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRunTaskRequest', ], 'output' => [ 'shape' => 'GetRunTaskResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'GetSequenceStore' => [ 'name' => 'GetSequenceStore', 'http' => [ 'method' => 'GET', 'requestUri' => '/sequencestore/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSequenceStoreRequest', ], 'output' => [ 'shape' => 'GetSequenceStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'GetShare' => [ 'name' => 'GetShare', 'http' => [ 'method' => 'GET', 'requestUri' => '/share/{shareId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetShareRequest', ], 'output' => [ 'shape' => 'GetShareResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'GetVariantImportJob' => [ 'name' => 'GetVariantImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/import/variant/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVariantImportRequest', ], 'output' => [ 'shape' => 'GetVariantImportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'GetVariantStore' => [ 'name' => 'GetVariantStore', 'http' => [ 'method' => 'GET', 'requestUri' => '/variantStore/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVariantStoreRequest', ], 'output' => [ 'shape' => 'GetVariantStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'GetWorkflow' => [ 'name' => 'GetWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/workflow/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkflowRequest', ], 'output' => [ 'shape' => 'GetWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'ListAnnotationImportJobs' => [ 'name' => 'ListAnnotationImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/import/annotations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnnotationImportJobsRequest', ], 'output' => [ 'shape' => 'ListAnnotationImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'ListAnnotationStoreVersions' => [ 'name' => 'ListAnnotationStoreVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/annotationStore/{name}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnnotationStoreVersionsRequest', ], 'output' => [ 'shape' => 'ListAnnotationStoreVersionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'ListAnnotationStores' => [ 'name' => 'ListAnnotationStores', 'http' => [ 'method' => 'POST', 'requestUri' => '/annotationStores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnnotationStoresRequest', ], 'output' => [ 'shape' => 'ListAnnotationStoresResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'ListMultipartReadSetUploads' => [ 'name' => 'ListMultipartReadSetUploads', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/uploads', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMultipartReadSetUploadsRequest', ], 'output' => [ 'shape' => 'ListMultipartReadSetUploadsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'NotSupportedOperationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReadSetActivationJobs' => [ 'name' => 'ListReadSetActivationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/activationjobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReadSetActivationJobsRequest', ], 'output' => [ 'shape' => 'ListReadSetActivationJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReadSetExportJobs' => [ 'name' => 'ListReadSetExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/exportjobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReadSetExportJobsRequest', ], 'output' => [ 'shape' => 'ListReadSetExportJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReadSetImportJobs' => [ 'name' => 'ListReadSetImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/importjobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReadSetImportJobsRequest', ], 'output' => [ 'shape' => 'ListReadSetImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReadSetUploadParts' => [ 'name' => 'ListReadSetUploadParts', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/upload/{uploadId}/parts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReadSetUploadPartsRequest', ], 'output' => [ 'shape' => 'ListReadSetUploadPartsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'NotSupportedOperationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReadSets' => [ 'name' => 'ListReadSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/readsets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReadSetsRequest', ], 'output' => [ 'shape' => 'ListReadSetsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReferenceImportJobs' => [ 'name' => 'ListReferenceImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/referencestore/{referenceStoreId}/importjobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReferenceImportJobsRequest', ], 'output' => [ 'shape' => 'ListReferenceImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReferenceStores' => [ 'name' => 'ListReferenceStores', 'http' => [ 'method' => 'POST', 'requestUri' => '/referencestores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReferenceStoresRequest', ], 'output' => [ 'shape' => 'ListReferenceStoresResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListReferences' => [ 'name' => 'ListReferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/referencestore/{referenceStoreId}/references', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReferencesRequest', ], 'output' => [ 'shape' => 'ListReferencesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListRunGroups' => [ 'name' => 'ListRunGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/runGroup', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRunGroupsRequest', ], 'output' => [ 'shape' => 'ListRunGroupsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'ListRunTasks' => [ 'name' => 'ListRunTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/run/{id}/task', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRunTasksRequest', ], 'output' => [ 'shape' => 'ListRunTasksResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'ListRuns' => [ 'name' => 'ListRuns', 'http' => [ 'method' => 'GET', 'requestUri' => '/run', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRunsRequest', ], 'output' => [ 'shape' => 'ListRunsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'ListSequenceStores' => [ 'name' => 'ListSequenceStores', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSequenceStoresRequest', ], 'output' => [ 'shape' => 'ListSequenceStoresResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'ListShares' => [ 'name' => 'ListShares', 'http' => [ 'method' => 'POST', 'requestUri' => '/shares', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSharesRequest', ], 'output' => [ 'shape' => 'ListSharesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'tags-', ], ], 'ListVariantImportJobs' => [ 'name' => 'ListVariantImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/import/variants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVariantImportJobsRequest', ], 'output' => [ 'shape' => 'ListVariantImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'ListVariantStores' => [ 'name' => 'ListVariantStores', 'http' => [ 'method' => 'POST', 'requestUri' => '/variantStores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVariantStoresRequest', ], 'output' => [ 'shape' => 'ListVariantStoresResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/workflow', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkflowsRequest', ], 'output' => [ 'shape' => 'ListWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'StartAnnotationImportJob' => [ 'name' => 'StartAnnotationImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/import/annotation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAnnotationImportRequest', ], 'output' => [ 'shape' => 'StartAnnotationImportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'StartReadSetActivationJob' => [ 'name' => 'StartReadSetActivationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/activationjob', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartReadSetActivationJobRequest', ], 'output' => [ 'shape' => 'StartReadSetActivationJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'StartReadSetExportJob' => [ 'name' => 'StartReadSetExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/exportjob', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartReadSetExportJobRequest', ], 'output' => [ 'shape' => 'StartReadSetExportJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'StartReadSetImportJob' => [ 'name' => 'StartReadSetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/sequencestore/{sequenceStoreId}/importjob', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartReadSetImportJobRequest', ], 'output' => [ 'shape' => 'StartReadSetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'StartReferenceImportJob' => [ 'name' => 'StartReferenceImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/referencestore/{referenceStoreId}/importjob', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartReferenceImportJobRequest', ], 'output' => [ 'shape' => 'StartReferenceImportJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'control-storage-', ], ], 'StartRun' => [ 'name' => 'StartRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/run', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartRunRequest', ], 'output' => [ 'shape' => 'StartRunResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'StartVariantImportJob' => [ 'name' => 'StartVariantImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/import/variant', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartVariantImportRequest', ], 'output' => [ 'shape' => 'StartVariantImportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'tags-', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'tags-', ], 'idempotent' => true, ], 'UpdateAnnotationStore' => [ 'name' => 'UpdateAnnotationStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/annotationStore/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAnnotationStoreRequest', ], 'output' => [ 'shape' => 'UpdateAnnotationStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'UpdateAnnotationStoreVersion' => [ 'name' => 'UpdateAnnotationStoreVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/annotationStore/{name}/version/{versionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAnnotationStoreVersionRequest', ], 'output' => [ 'shape' => 'UpdateAnnotationStoreVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'UpdateRunGroup' => [ 'name' => 'UpdateRunGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/runGroup/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateRunGroupRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'UpdateVariantStore' => [ 'name' => 'UpdateVariantStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/variantStore/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVariantStoreRequest', ], 'output' => [ 'shape' => 'UpdateVariantStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'analytics-', ], ], 'UpdateWorkflow' => [ 'name' => 'UpdateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/workflow/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateWorkflowRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'workflows-', ], ], 'UploadReadSetPart' => [ 'name' => 'UploadReadSetPart', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sequencestore/{sequenceStoreId}/upload/{uploadId}/part', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UploadReadSetPartRequest', ], 'output' => [ 'shape' => 'UploadReadSetPartResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'NotSupportedOperationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RequestTimeoutException', ], ], 'authtype' => 'v4-unsigned-body', 'endpoint' => [ 'hostPrefix' => 'storage-', ], ], ], 'shapes' => [ 'AbortMultipartReadSetUploadRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'uploadId', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'uploadId' => [ 'shape' => 'UploadId', 'location' => 'uri', 'locationName' => 'uploadId', ], ], ], 'AbortMultipartReadSetUploadResponse' => [ 'type' => 'structure', 'members' => [], ], 'Accelerators' => [ 'type' => 'string', 'enum' => [ 'GPU', ], 'max' => 64, 'min' => 1, ], 'AcceptShareRequest' => [ 'type' => 'structure', 'required' => [ 'shareId', ], 'members' => [ 'shareId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'shareId', ], ], ], 'AcceptShareResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ShareStatus', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActivateReadSetFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ReadSetActivationJobStatus', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ActivateReadSetJobItem' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ActivationJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'status' => [ 'shape' => 'ReadSetActivationJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ActivateReadSetJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActivateReadSetJobItem', ], ], 'ActivateReadSetSourceItem' => [ 'type' => 'structure', 'required' => [ 'readSetId', 'status', ], 'members' => [ 'readSetId' => [ 'shape' => 'ReadSetId', ], 'status' => [ 'shape' => 'ReadSetActivationJobItemStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], ], ], 'ActivateReadSetSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActivateReadSetSourceItem', ], ], 'ActivationJobId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'AnnotationFieldMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AnnotationFieldMapKeyString', ], 'value' => [ 'shape' => 'AnnotationFieldMapValueString', ], ], 'AnnotationFieldMapKeyString' => [ 'type' => 'string', 'max' => 21, 'min' => 1, ], 'AnnotationFieldMapValueString' => [ 'type' => 'string', 'max' => 21, 'min' => 1, ], 'AnnotationImportItemDetail' => [ 'type' => 'structure', 'required' => [ 'source', 'jobStatus', ], 'members' => [ 'source' => [ 'shape' => 'S3Uri', ], 'jobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'AnnotationImportItemDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnnotationImportItemDetail', ], 'max' => 1, 'min' => 1, ], 'AnnotationImportItemSource' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'source' => [ 'shape' => 'S3Uri', ], ], ], 'AnnotationImportItemSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnnotationImportItemSource', ], 'min' => 1, ], 'AnnotationImportJobItem' => [ 'type' => 'structure', 'required' => [ 'id', 'destinationName', 'versionName', 'roleArn', 'status', 'creationTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'destinationName' => [ 'shape' => 'String', ], 'versionName' => [ 'shape' => 'VersionName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'JobStatus', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'completionTime' => [ 'shape' => 'CompletionTime', ], 'runLeftNormalization' => [ 'shape' => 'RunLeftNormalization', ], 'annotationFields' => [ 'shape' => 'AnnotationFieldMap', ], ], ], 'AnnotationImportJobItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnnotationImportJobItem', ], ], 'AnnotationStoreItem' => [ 'type' => 'structure', 'required' => [ 'id', 'reference', 'status', 'storeArn', 'name', 'storeFormat', 'description', 'sseConfig', 'creationTime', 'updateTime', 'statusMessage', 'storeSizeBytes', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'status' => [ 'shape' => 'StoreStatus', ], 'storeArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'String', ], 'storeFormat' => [ 'shape' => 'StoreFormat', ], 'description' => [ 'shape' => 'Description', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'storeSizeBytes' => [ 'shape' => 'Long', ], ], ], 'AnnotationStoreItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnnotationStoreItem', ], ], 'AnnotationStoreVersionItem' => [ 'type' => 'structure', 'required' => [ 'storeId', 'id', 'status', 'versionArn', 'name', 'versionName', 'description', 'creationTime', 'updateTime', 'statusMessage', 'versionSizeBytes', ], 'members' => [ 'storeId' => [ 'shape' => 'ResourceId', ], 'id' => [ 'shape' => 'ResourceId', ], 'status' => [ 'shape' => 'VersionStatus', ], 'versionArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'StoreName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'description' => [ 'shape' => 'Description', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'versionSizeBytes' => [ 'shape' => 'Long', ], ], ], 'AnnotationStoreVersionItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnnotationStoreVersionItem', ], ], 'AnnotationType' => [ 'type' => 'string', 'enum' => [ 'GENERIC', 'CHR_POS', 'CHR_POS_REF_ALT', 'CHR_START_END_ONE_BASE', 'CHR_START_END_REF_ALT_ONE_BASE', 'CHR_START_END_ZERO_BASE', 'CHR_START_END_REF_ALT_ZERO_BASE', ], ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:([^: ]*):([^: ]*):([^: ]*):([0-9]{12}):([^: ]*)', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 1, ], 'BatchDeleteReadSetRequest' => [ 'type' => 'structure', 'required' => [ 'ids', 'sequenceStoreId', ], 'members' => [ 'ids' => [ 'shape' => 'ReadSetIdList', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], ], ], 'BatchDeleteReadSetResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'ReadSetBatchErrorList', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelAnnotationImportRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'CancelAnnotationImportResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelRunRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RunId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'CancelVariantImportRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'CancelVariantImportResponse' => [ 'type' => 'structure', 'members' => [], ], 'ClientToken' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'CommentChar' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'CompleteMultipartReadSetUploadRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'uploadId', 'parts', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'uploadId' => [ 'shape' => 'UploadId', 'location' => 'uri', 'locationName' => 'uploadId', ], 'parts' => [ 'shape' => 'CompleteReadSetUploadPartList', ], ], ], 'CompleteMultipartReadSetUploadResponse' => [ 'type' => 'structure', 'required' => [ 'readSetId', ], 'members' => [ 'readSetId' => [ 'shape' => 'ReadSetId', ], ], ], 'CompleteReadSetUploadPartList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompleteReadSetUploadPartListItem', ], ], 'CompleteReadSetUploadPartListItem' => [ 'type' => 'structure', 'required' => [ 'partNumber', 'partSource', 'checksum', ], 'members' => [ 'partNumber' => [ 'shape' => 'CompleteReadSetUploadPartListItemPartNumberInteger', ], 'partSource' => [ 'shape' => 'ReadSetPartSource', ], 'checksum' => [ 'shape' => 'CompleteReadSetUploadPartListItemChecksumString', ], ], ], 'CompleteReadSetUploadPartListItemChecksumString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'CompleteReadSetUploadPartListItemPartNumberInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'CompletionTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAnnotationStoreRequest' => [ 'type' => 'structure', 'required' => [ 'storeFormat', ], 'members' => [ 'reference' => [ 'shape' => 'ReferenceItem', ], 'name' => [ 'shape' => 'StoreName', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagMap', ], 'versionName' => [ 'shape' => 'VersionName', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'storeFormat' => [ 'shape' => 'StoreFormat', ], 'storeOptions' => [ 'shape' => 'StoreOptions', ], ], ], 'CreateAnnotationStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'status', 'name', 'versionName', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'storeFormat' => [ 'shape' => 'StoreFormat', ], 'storeOptions' => [ 'shape' => 'StoreOptions', ], 'status' => [ 'shape' => 'StoreStatus', ], 'name' => [ 'shape' => 'String', ], 'versionName' => [ 'shape' => 'VersionName', ], 'creationTime' => [ 'shape' => 'CreationTime', ], ], ], 'CreateAnnotationStoreVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'versionName', ], 'members' => [ 'name' => [ 'shape' => 'StoreName', 'location' => 'uri', 'locationName' => 'name', ], 'versionName' => [ 'shape' => 'VersionName', ], 'description' => [ 'shape' => 'Description', ], 'versionOptions' => [ 'shape' => 'VersionOptions', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAnnotationStoreVersionResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'versionName', 'storeId', 'name', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'versionName' => [ 'shape' => 'VersionName', ], 'storeId' => [ 'shape' => 'ResourceId', ], 'versionOptions' => [ 'shape' => 'VersionOptions', ], 'name' => [ 'shape' => 'StoreName', ], 'status' => [ 'shape' => 'VersionStatus', ], 'creationTime' => [ 'shape' => 'CreationTime', ], ], ], 'CreateMultipartReadSetUploadRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'sourceFileType', 'subjectId', 'sampleId', 'name', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'sourceFileType' => [ 'shape' => 'FileType', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'generatedFrom' => [ 'shape' => 'GeneratedFrom', ], 'referenceArn' => [ 'shape' => 'ReferenceArn', ], 'name' => [ 'shape' => 'ReadSetName', ], 'description' => [ 'shape' => 'ReadSetDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMultipartReadSetUploadResponse' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'uploadId', 'sourceFileType', 'subjectId', 'sampleId', 'referenceArn', 'creationTime', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'sourceFileType' => [ 'shape' => 'FileType', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'generatedFrom' => [ 'shape' => 'GeneratedFrom', ], 'referenceArn' => [ 'shape' => 'ReferenceArn', ], 'name' => [ 'shape' => 'ReadSetName', ], 'description' => [ 'shape' => 'ReadSetDescription', ], 'tags' => [ 'shape' => 'TagMap', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'CreateReferenceStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ReferenceStoreName', ], 'description' => [ 'shape' => 'ReferenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateReferenceStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceStoreId', ], 'arn' => [ 'shape' => 'ReferenceStoreArn', ], 'name' => [ 'shape' => 'ReferenceStoreName', ], 'description' => [ 'shape' => 'ReferenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'CreateRunGroupRequest' => [ 'type' => 'structure', 'required' => [ 'requestId', ], 'members' => [ 'name' => [ 'shape' => 'RunGroupName', ], 'maxCpus' => [ 'shape' => 'CreateRunGroupRequestMaxCpusInteger', ], 'maxRuns' => [ 'shape' => 'CreateRunGroupRequestMaxRunsInteger', ], 'maxDuration' => [ 'shape' => 'CreateRunGroupRequestMaxDurationInteger', ], 'tags' => [ 'shape' => 'TagMap', ], 'requestId' => [ 'shape' => 'RunGroupRequestId', 'idempotencyToken' => true, ], 'maxGpus' => [ 'shape' => 'CreateRunGroupRequestMaxGpusInteger', ], ], ], 'CreateRunGroupRequestMaxCpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'CreateRunGroupRequestMaxDurationInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'CreateRunGroupRequestMaxGpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'CreateRunGroupRequestMaxRunsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'CreateRunGroupResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RunGroupArn', ], 'id' => [ 'shape' => 'RunGroupId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSequenceStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'SequenceStoreName', ], 'description' => [ 'shape' => 'SequenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'fallbackLocation' => [ 'shape' => 'S3Destination', ], 'eTagAlgorithmFamily' => [ 'shape' => 'ETagAlgorithmFamily', ], ], ], 'CreateSequenceStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'SequenceStoreId', ], 'arn' => [ 'shape' => 'SequenceStoreArn', ], 'name' => [ 'shape' => 'SequenceStoreName', ], 'description' => [ 'shape' => 'SequenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'fallbackLocation' => [ 'shape' => 'S3Destination', ], 'eTagAlgorithmFamily' => [ 'shape' => 'ETagAlgorithmFamily', ], ], ], 'CreateShareRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'principalSubscriber', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', ], 'principalSubscriber' => [ 'shape' => 'String', ], 'shareName' => [ 'shape' => 'ShareName', ], ], ], 'CreateShareResponse' => [ 'type' => 'structure', 'members' => [ 'shareId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'ShareStatus', ], 'shareName' => [ 'shape' => 'ShareName', ], ], ], 'CreateVariantStoreRequest' => [ 'type' => 'structure', 'required' => [ 'reference', ], 'members' => [ 'reference' => [ 'shape' => 'ReferenceItem', ], 'name' => [ 'shape' => 'StoreName', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagMap', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], ], ], 'CreateVariantStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'status', 'name', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'status' => [ 'shape' => 'StoreStatus', ], 'name' => [ 'shape' => 'String', ], 'creationTime' => [ 'shape' => 'CreationTime', ], ], ], 'CreateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'requestId', ], 'members' => [ 'name' => [ 'shape' => 'WorkflowName', ], 'description' => [ 'shape' => 'WorkflowDescription', ], 'engine' => [ 'shape' => 'WorkflowEngine', ], 'definitionZip' => [ 'shape' => 'Blob', ], 'definitionUri' => [ 'shape' => 'WorkflowDefinition', ], 'main' => [ 'shape' => 'WorkflowMain', ], 'parameterTemplate' => [ 'shape' => 'WorkflowParameterTemplate', ], 'storageCapacity' => [ 'shape' => 'CreateWorkflowRequestStorageCapacityInteger', ], 'tags' => [ 'shape' => 'TagMap', ], 'requestId' => [ 'shape' => 'WorkflowRequestId', 'idempotencyToken' => true, ], 'accelerators' => [ 'shape' => 'Accelerators', ], ], ], 'CreateWorkflowRequestStorageCapacityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'CreateWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'WorkflowArn', ], 'id' => [ 'shape' => 'WorkflowId', ], 'status' => [ 'shape' => 'WorkflowStatus', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'CreationType' => [ 'type' => 'string', 'enum' => [ 'IMPORT', 'UPLOAD', ], ], 'DeleteAnnotationStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'force' => [ 'shape' => 'PrimitiveBoolean', 'location' => 'querystring', 'locationName' => 'force', ], ], ], 'DeleteAnnotationStoreResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'StoreStatus', ], ], ], 'DeleteAnnotationStoreVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'versions', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'versions' => [ 'shape' => 'VersionList', ], 'force' => [ 'shape' => 'PrimitiveBoolean', 'location' => 'querystring', 'locationName' => 'force', ], ], ], 'DeleteAnnotationStoreVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'VersionDeleteErrorList', ], ], ], 'DeleteReferenceRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'referenceStoreId', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceId', 'location' => 'uri', 'locationName' => 'id', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'referenceStoreId', ], ], ], 'DeleteReferenceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteReferenceStoreRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteReferenceStoreResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRunGroupRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RunGroupId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteRunRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RunId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteSequenceStoreRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteSequenceStoreResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteShareRequest' => [ 'type' => 'structure', 'required' => [ 'shareId', ], 'members' => [ 'shareId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'shareId', ], ], ], 'DeleteShareResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ShareStatus', ], ], ], 'DeleteVariantStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'force' => [ 'shape' => 'PrimitiveBoolean', 'location' => 'querystring', 'locationName' => 'force', ], ], ], 'DeleteVariantStoreResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'StoreStatus', ], ], ], 'DeleteWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkflowId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'ETag' => [ 'type' => 'structure', 'members' => [ 'algorithm' => [ 'shape' => 'ETagAlgorithm', ], 'source1' => [ 'shape' => 'String', ], 'source2' => [ 'shape' => 'String', ], ], ], 'ETagAlgorithm' => [ 'type' => 'string', 'enum' => [ 'FASTQ_MD5up', 'BAM_MD5up', 'CRAM_MD5up', 'FASTQ_SHA256up', 'BAM_SHA256up', 'CRAM_SHA256up', 'FASTQ_SHA512up', 'BAM_SHA512up', 'CRAM_SHA512up', ], ], 'ETagAlgorithmFamily' => [ 'type' => 'string', 'enum' => [ 'MD5up', 'SHA256up', 'SHA512up', ], ], 'Encoding' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'KMS', ], ], 'EngineLogStream' => [ 'type' => 'string', 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'EscapeChar' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'EscapeQuotes' => [ 'type' => 'boolean', ], 'ExportJobId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'ExportReadSet' => [ 'type' => 'structure', 'required' => [ 'readSetId', ], 'members' => [ 'readSetId' => [ 'shape' => 'ReadSetId', ], ], ], 'ExportReadSetDetail' => [ 'type' => 'structure', 'required' => [ 'id', 'status', ], 'members' => [ 'id' => [ 'shape' => 'ReadSetId', ], 'status' => [ 'shape' => 'ReadSetExportJobItemStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], ], ], 'ExportReadSetDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportReadSetDetail', ], ], 'ExportReadSetFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ReadSetExportJobStatus', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ExportReadSetJobDetail' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'destination', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ExportJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'destination' => [ 'shape' => 'S3Destination', ], 'status' => [ 'shape' => 'ReadSetExportJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ExportReadSetJobDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportReadSetJobDetail', ], ], 'FileInformation' => [ 'type' => 'structure', 'members' => [ 'totalParts' => [ 'shape' => 'FileInformationTotalPartsInteger', ], 'partSize' => [ 'shape' => 'FileInformationPartSizeLong', ], 'contentLength' => [ 'shape' => 'FileInformationContentLengthLong', ], 's3Access' => [ 'shape' => 'ReadSetS3Access', ], ], ], 'FileInformationContentLengthLong' => [ 'type' => 'long', 'box' => true, 'max' => 5497558138880, 'min' => 1, ], 'FileInformationPartSizeLong' => [ 'type' => 'long', 'box' => true, 'max' => 5368709120, 'min' => 1, ], 'FileInformationTotalPartsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'FileType' => [ 'type' => 'string', 'enum' => [ 'FASTQ', 'BAM', 'CRAM', 'UBAM', ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'resourceArns' => [ 'shape' => 'ArnList', ], 'status' => [ 'shape' => 'StatusList', ], 'type' => [ 'shape' => 'TypeList', ], ], ], 'FormatOptions' => [ 'type' => 'structure', 'members' => [ 'tsvOptions' => [ 'shape' => 'TsvOptions', ], 'vcfOptions' => [ 'shape' => 'VcfOptions', ], ], 'union' => true, ], 'FormatToHeader' => [ 'type' => 'map', 'key' => [ 'shape' => 'FormatToHeaderKey', ], 'value' => [ 'shape' => 'FormatToHeaderValueString', ], ], 'FormatToHeaderKey' => [ 'type' => 'string', 'enum' => [ 'CHR', 'START', 'END', 'REF', 'ALT', 'POS', ], ], 'FormatToHeaderValueString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'GeneratedFrom' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'GetAnnotationImportRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetAnnotationImportResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'destinationName', 'versionName', 'roleArn', 'status', 'statusMessage', 'creationTime', 'updateTime', 'completionTime', 'items', 'runLeftNormalization', 'formatOptions', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'destinationName' => [ 'shape' => 'StoreName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'JobStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMsg', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'completionTime' => [ 'shape' => 'CompletionTime', ], 'items' => [ 'shape' => 'AnnotationImportItemDetails', ], 'runLeftNormalization' => [ 'shape' => 'RunLeftNormalization', ], 'formatOptions' => [ 'shape' => 'FormatOptions', ], 'annotationFields' => [ 'shape' => 'AnnotationFieldMap', ], ], ], 'GetAnnotationStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAnnotationStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'reference', 'status', 'storeArn', 'name', 'description', 'sseConfig', 'creationTime', 'updateTime', 'tags', 'statusMessage', 'storeSizeBytes', 'numVersions', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'status' => [ 'shape' => 'StoreStatus', ], 'storeArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'storeOptions' => [ 'shape' => 'StoreOptions', ], 'storeFormat' => [ 'shape' => 'StoreFormat', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'storeSizeBytes' => [ 'shape' => 'Long', ], 'numVersions' => [ 'shape' => 'Integer', ], ], ], 'GetAnnotationStoreVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'versionName', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'versionName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'versionName', ], ], ], 'GetAnnotationStoreVersionResponse' => [ 'type' => 'structure', 'required' => [ 'storeId', 'id', 'status', 'versionArn', 'name', 'versionName', 'description', 'creationTime', 'updateTime', 'tags', 'statusMessage', 'versionSizeBytes', ], 'members' => [ 'storeId' => [ 'shape' => 'ResourceId', ], 'id' => [ 'shape' => 'ResourceId', ], 'status' => [ 'shape' => 'VersionStatus', ], 'versionArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'StoreName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'description' => [ 'shape' => 'Description', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'versionOptions' => [ 'shape' => 'VersionOptions', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'versionSizeBytes' => [ 'shape' => 'Long', ], ], ], 'GetReadSetActivationJobRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', ], 'members' => [ 'id' => [ 'shape' => 'ActivationJobId', 'location' => 'uri', 'locationName' => 'id', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], ], ], 'GetReadSetActivationJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ActivationJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'status' => [ 'shape' => 'ReadSetActivationJobStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'sources' => [ 'shape' => 'ActivateReadSetSourceList', ], ], ], 'GetReadSetExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'id', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'id' => [ 'shape' => 'ExportJobId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetReadSetExportJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'destination', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ExportJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'destination' => [ 'shape' => 'S3Destination', ], 'status' => [ 'shape' => 'ReadSetExportJobStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'readSets' => [ 'shape' => 'ExportReadSetDetailList', ], ], ], 'GetReadSetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', 'location' => 'uri', 'locationName' => 'id', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], ], ], 'GetReadSetImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'roleArn', 'status', 'creationTime', 'sources', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ReadSetImportJobStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'sources' => [ 'shape' => 'ImportReadSetSourceList', ], ], ], 'GetReadSetMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', ], 'members' => [ 'id' => [ 'shape' => 'ReadSetId', 'location' => 'uri', 'locationName' => 'id', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], ], ], 'GetReadSetMetadataResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'sequenceStoreId', 'status', 'fileType', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ReadSetId', ], 'arn' => [ 'shape' => 'ReadSetArn', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'status' => [ 'shape' => 'ReadSetStatus', ], 'name' => [ 'shape' => 'ReadSetName', ], 'description' => [ 'shape' => 'ReadSetDescription', ], 'fileType' => [ 'shape' => 'FileType', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'sequenceInformation' => [ 'shape' => 'SequenceInformation', ], 'referenceArn' => [ 'shape' => 'ReferenceArn', ], 'files' => [ 'shape' => 'ReadSetFiles', ], 'statusMessage' => [ 'shape' => 'ReadSetStatusMessage', ], 'creationType' => [ 'shape' => 'CreationType', ], 'etag' => [ 'shape' => 'ETag', ], ], ], 'GetReadSetRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'partNumber', ], 'members' => [ 'id' => [ 'shape' => 'ReadSetId', 'location' => 'uri', 'locationName' => 'id', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'file' => [ 'shape' => 'ReadSetFile', 'location' => 'querystring', 'locationName' => 'file', ], 'partNumber' => [ 'shape' => 'GetReadSetRequestPartNumberInteger', 'location' => 'querystring', 'locationName' => 'partNumber', ], ], ], 'GetReadSetRequestPartNumberInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'GetReadSetResponse' => [ 'type' => 'structure', 'members' => [ 'payload' => [ 'shape' => 'ReadSetStreamingBlob', ], ], 'payload' => 'payload', ], 'GetReferenceImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'referenceStoreId', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', 'location' => 'uri', 'locationName' => 'id', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'referenceStoreId', ], ], ], 'GetReferenceImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'referenceStoreId', 'roleArn', 'status', 'creationTime', 'sources', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ReferenceImportJobStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'sources' => [ 'shape' => 'ImportReferenceSourceList', ], ], ], 'GetReferenceMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'referenceStoreId', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceId', 'location' => 'uri', 'locationName' => 'id', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'referenceStoreId', ], ], ], 'GetReferenceMetadataResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'referenceStoreId', 'md5', 'creationTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceId', ], 'arn' => [ 'shape' => 'ReferenceArn', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', ], 'md5' => [ 'shape' => 'Md5', ], 'status' => [ 'shape' => 'ReferenceStatus', ], 'name' => [ 'shape' => 'ReferenceName', ], 'description' => [ 'shape' => 'ReferenceDescription', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'files' => [ 'shape' => 'ReferenceFiles', ], ], ], 'GetReferenceRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'referenceStoreId', 'partNumber', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceId', 'location' => 'uri', 'locationName' => 'id', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'referenceStoreId', ], 'range' => [ 'shape' => 'Range', 'location' => 'header', 'locationName' => 'Range', ], 'partNumber' => [ 'shape' => 'GetReferenceRequestPartNumberInteger', 'location' => 'querystring', 'locationName' => 'partNumber', ], 'file' => [ 'shape' => 'ReferenceFile', 'location' => 'querystring', 'locationName' => 'file', ], ], ], 'GetReferenceRequestPartNumberInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'GetReferenceResponse' => [ 'type' => 'structure', 'members' => [ 'payload' => [ 'shape' => 'ReferenceStreamingBlob', ], ], 'payload' => 'payload', ], 'GetReferenceStoreRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetReferenceStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceStoreId', ], 'arn' => [ 'shape' => 'ReferenceStoreArn', ], 'name' => [ 'shape' => 'ReferenceStoreName', ], 'description' => [ 'shape' => 'ReferenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetRunGroupRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RunGroupId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetRunGroupResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RunGroupArn', ], 'id' => [ 'shape' => 'RunGroupId', ], 'name' => [ 'shape' => 'RunGroupName', ], 'maxCpus' => [ 'shape' => 'GetRunGroupResponseMaxCpusInteger', ], 'maxRuns' => [ 'shape' => 'GetRunGroupResponseMaxRunsInteger', ], 'maxDuration' => [ 'shape' => 'GetRunGroupResponseMaxDurationInteger', ], 'creationTime' => [ 'shape' => 'RunGroupTimestamp', ], 'tags' => [ 'shape' => 'TagMap', ], 'maxGpus' => [ 'shape' => 'GetRunGroupResponseMaxGpusInteger', ], ], ], 'GetRunGroupResponseMaxCpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'GetRunGroupResponseMaxDurationInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'GetRunGroupResponseMaxGpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'GetRunGroupResponseMaxRunsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'GetRunRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RunId', 'location' => 'uri', 'locationName' => 'id', ], 'export' => [ 'shape' => 'RunExportList', 'location' => 'querystring', 'locationName' => 'export', ], ], ], 'GetRunResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RunArn', ], 'id' => [ 'shape' => 'RunId', ], 'status' => [ 'shape' => 'RunStatus', ], 'workflowId' => [ 'shape' => 'WorkflowId', ], 'workflowType' => [ 'shape' => 'WorkflowType', ], 'runId' => [ 'shape' => 'RunId', ], 'roleArn' => [ 'shape' => 'RunRoleArn', ], 'name' => [ 'shape' => 'RunName', ], 'runGroupId' => [ 'shape' => 'RunGroupId', ], 'priority' => [ 'shape' => 'GetRunResponsePriorityInteger', ], 'definition' => [ 'shape' => 'WorkflowDefinition', ], 'digest' => [ 'shape' => 'WorkflowDigest', ], 'parameters' => [ 'shape' => 'RunParameters', ], 'storageCapacity' => [ 'shape' => 'GetRunResponseStorageCapacityInteger', ], 'outputUri' => [ 'shape' => 'RunOutputUri', ], 'logLevel' => [ 'shape' => 'RunLogLevel', ], 'resourceDigests' => [ 'shape' => 'RunResourceDigests', ], 'startedBy' => [ 'shape' => 'RunStartedBy', ], 'creationTime' => [ 'shape' => 'RunTimestamp', ], 'startTime' => [ 'shape' => 'RunTimestamp', ], 'stopTime' => [ 'shape' => 'RunTimestamp', ], 'statusMessage' => [ 'shape' => 'RunStatusMessage', ], 'tags' => [ 'shape' => 'TagMap', ], 'accelerators' => [ 'shape' => 'Accelerators', ], 'retentionMode' => [ 'shape' => 'RunRetentionMode', ], 'failureReason' => [ 'shape' => 'RunFailureReason', ], 'logLocation' => [ 'shape' => 'RunLogLocation', ], 'uuid' => [ 'shape' => 'RunUuid', ], 'runOutputUri' => [ 'shape' => 'RunOutputUri', ], 'storageType' => [ 'shape' => 'StorageType', ], 'workflowOwnerId' => [ 'shape' => 'WorkflowOwnerId', ], ], ], 'GetRunResponsePriorityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'GetRunResponseStorageCapacityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'GetRunTaskRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'taskId', ], 'members' => [ 'id' => [ 'shape' => 'RunId', 'location' => 'uri', 'locationName' => 'id', ], 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'GetRunTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'status' => [ 'shape' => 'TaskStatus', ], 'name' => [ 'shape' => 'TaskName', ], 'cpus' => [ 'shape' => 'GetRunTaskResponseCpusInteger', ], 'memory' => [ 'shape' => 'GetRunTaskResponseMemoryInteger', ], 'creationTime' => [ 'shape' => 'TaskTimestamp', ], 'startTime' => [ 'shape' => 'TaskTimestamp', ], 'stopTime' => [ 'shape' => 'TaskTimestamp', ], 'statusMessage' => [ 'shape' => 'TaskStatusMessage', ], 'logStream' => [ 'shape' => 'TaskLogStream', ], 'gpus' => [ 'shape' => 'GetRunTaskResponseGpusInteger', ], 'instanceType' => [ 'shape' => 'TaskInstanceType', ], 'failureReason' => [ 'shape' => 'TaskFailureReason', ], ], ], 'GetRunTaskResponseCpusInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'GetRunTaskResponseGpusInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'GetRunTaskResponseMemoryInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'GetSequenceStoreRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetSequenceStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'SequenceStoreId', ], 'arn' => [ 'shape' => 'SequenceStoreArn', ], 'name' => [ 'shape' => 'SequenceStoreName', ], 'description' => [ 'shape' => 'SequenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'fallbackLocation' => [ 'shape' => 'S3Destination', ], 's3Access' => [ 'shape' => 'SequenceStoreS3Access', ], 'eTagAlgorithmFamily' => [ 'shape' => 'ETagAlgorithmFamily', ], ], ], 'GetShareRequest' => [ 'type' => 'structure', 'required' => [ 'shareId', ], 'members' => [ 'shareId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'shareId', ], ], ], 'GetShareResponse' => [ 'type' => 'structure', 'members' => [ 'share' => [ 'shape' => 'ShareDetails', ], ], ], 'GetVariantImportRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetVariantImportResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'destinationName', 'roleArn', 'status', 'statusMessage', 'creationTime', 'updateTime', 'items', 'runLeftNormalization', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'destinationName' => [ 'shape' => 'StoreName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'JobStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMsg', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'completionTime' => [ 'shape' => 'CompletionTime', ], 'items' => [ 'shape' => 'VariantImportItemDetails', ], 'runLeftNormalization' => [ 'shape' => 'RunLeftNormalization', ], 'annotationFields' => [ 'shape' => 'AnnotationFieldMap', ], ], ], 'GetVariantStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetVariantStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'reference', 'status', 'storeArn', 'name', 'description', 'sseConfig', 'creationTime', 'updateTime', 'tags', 'statusMessage', 'storeSizeBytes', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'status' => [ 'shape' => 'StoreStatus', ], 'storeArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'storeSizeBytes' => [ 'shape' => 'Long', ], ], ], 'GetWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkflowId', 'location' => 'uri', 'locationName' => 'id', ], 'type' => [ 'shape' => 'WorkflowType', 'location' => 'querystring', 'locationName' => 'type', ], 'export' => [ 'shape' => 'WorkflowExportList', 'location' => 'querystring', 'locationName' => 'export', ], 'workflowOwnerId' => [ 'shape' => 'WorkflowOwnerId', 'location' => 'querystring', 'locationName' => 'workflowOwnerId', ], ], ], 'GetWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'WorkflowArn', ], 'id' => [ 'shape' => 'WorkflowId', ], 'status' => [ 'shape' => 'WorkflowStatus', ], 'type' => [ 'shape' => 'WorkflowType', ], 'name' => [ 'shape' => 'WorkflowName', ], 'description' => [ 'shape' => 'WorkflowDescription', ], 'engine' => [ 'shape' => 'WorkflowEngine', ], 'definition' => [ 'shape' => 'WorkflowDefinition', ], 'main' => [ 'shape' => 'WorkflowMain', ], 'digest' => [ 'shape' => 'WorkflowDigest', ], 'parameterTemplate' => [ 'shape' => 'WorkflowParameterTemplate', ], 'storageCapacity' => [ 'shape' => 'GetWorkflowResponseStorageCapacityInteger', ], 'creationTime' => [ 'shape' => 'WorkflowTimestamp', ], 'statusMessage' => [ 'shape' => 'WorkflowStatusMessage', ], 'tags' => [ 'shape' => 'TagMap', ], 'metadata' => [ 'shape' => 'WorkflowMetadata', ], 'accelerators' => [ 'shape' => 'Accelerators', ], ], ], 'GetWorkflowResponseStorageCapacityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'Header' => [ 'type' => 'boolean', ], 'ImportJobId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'ImportReadSetFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ReadSetImportJobStatus', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ImportReadSetJobItem' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'roleArn', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ReadSetImportJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ImportReadSetJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportReadSetJobItem', ], ], 'ImportReadSetSourceItem' => [ 'type' => 'structure', 'required' => [ 'sourceFiles', 'sourceFileType', 'status', 'subjectId', 'sampleId', ], 'members' => [ 'sourceFiles' => [ 'shape' => 'SourceFiles', ], 'sourceFileType' => [ 'shape' => 'FileType', ], 'status' => [ 'shape' => 'ReadSetImportJobItemStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'generatedFrom' => [ 'shape' => 'GeneratedFrom', ], 'referenceArn' => [ 'shape' => 'ReferenceArn', ], 'name' => [ 'shape' => 'ReadSetName', ], 'description' => [ 'shape' => 'ReadSetDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ImportReadSetSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportReadSetSourceItem', ], ], 'ImportReferenceFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ReferenceImportJobStatus', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ImportReferenceJobItem' => [ 'type' => 'structure', 'required' => [ 'id', 'referenceStoreId', 'roleArn', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ReferenceImportJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'completionTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ImportReferenceJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportReferenceJobItem', ], ], 'ImportReferenceSourceItem' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'sourceFile' => [ 'shape' => 'S3Uri', ], 'status' => [ 'shape' => 'ReferenceImportJobItemStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMessage', ], 'name' => [ 'shape' => 'ReferenceName', ], 'description' => [ 'shape' => 'ReferenceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ImportReferenceSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportReferenceSourceItem', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'IN_PROGRESS', 'CANCELLED', 'COMPLETED', 'FAILED', 'COMPLETED_WITH_FAILURES', ], ], 'JobStatusMessage' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'JobStatusMsg' => [ 'type' => 'string', ], 'LineSep' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'ListAnnotationImportJobsFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'JobStatus', ], 'storeName' => [ 'shape' => 'String', ], ], ], 'ListAnnotationImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListAnnotationImportJobsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ids' => [ 'shape' => 'ListAnnotationImportJobsRequestIdsList', ], 'nextToken' => [ 'shape' => 'ListAnnotationImportJobsRequestNextTokenString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ListAnnotationImportJobsFilter', ], ], ], 'ListAnnotationImportJobsRequestIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifier', ], 'max' => 20, 'min' => 1, ], 'ListAnnotationImportJobsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAnnotationImportJobsRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListAnnotationImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'annotationImportJobs' => [ 'shape' => 'AnnotationImportJobItems', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListAnnotationStoreVersionsFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'VersionStatus', ], ], ], 'ListAnnotationStoreVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'ListAnnotationStoreVersionsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'ListAnnotationStoreVersionsRequestNextTokenString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ListAnnotationStoreVersionsFilter', ], ], ], 'ListAnnotationStoreVersionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAnnotationStoreVersionsRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListAnnotationStoreVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'annotationStoreVersions' => [ 'shape' => 'AnnotationStoreVersionItems', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListAnnotationStoresFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'StoreStatus', ], ], ], 'ListAnnotationStoresRequest' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'ListAnnotationStoresRequestIdsList', ], 'maxResults' => [ 'shape' => 'ListAnnotationStoresRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'ListAnnotationStoresRequestNextTokenString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ListAnnotationStoresFilter', ], ], ], 'ListAnnotationStoresRequestIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifier', ], 'max' => 20, 'min' => 1, ], 'ListAnnotationStoresRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAnnotationStoresRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListAnnotationStoresResponse' => [ 'type' => 'structure', 'members' => [ 'annotationStores' => [ 'shape' => 'AnnotationStoreItems', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListMultipartReadSetUploadsRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'maxResults' => [ 'shape' => 'ListMultipartReadSetUploadsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMultipartReadSetUploadsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListMultipartReadSetUploadsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'uploads' => [ 'shape' => 'MultipartReadSetUploadList', ], ], ], 'ListReadSetActivationJobsRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'maxResults' => [ 'shape' => 'ListReadSetActivationJobsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ActivateReadSetFilter', ], ], ], 'ListReadSetActivationJobsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReadSetActivationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'activationJobs' => [ 'shape' => 'ActivateReadSetJobList', ], ], ], 'ListReadSetExportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'maxResults' => [ 'shape' => 'ListReadSetExportJobsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ExportReadSetFilter', ], ], ], 'ListReadSetExportJobsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReadSetExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'exportJobs' => [ 'shape' => 'ExportReadSetJobDetailList', ], ], ], 'ListReadSetImportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', ], 'members' => [ 'maxResults' => [ 'shape' => 'ListReadSetImportJobsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'filter' => [ 'shape' => 'ImportReadSetFilter', ], ], ], 'ListReadSetImportJobsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReadSetImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'importJobs' => [ 'shape' => 'ImportReadSetJobList', ], ], ], 'ListReadSetUploadPartsRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'uploadId', 'partSource', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'uploadId' => [ 'shape' => 'UploadId', 'location' => 'uri', 'locationName' => 'uploadId', ], 'partSource' => [ 'shape' => 'ReadSetPartSource', ], 'maxResults' => [ 'shape' => 'ListReadSetUploadPartsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ReadSetUploadPartListFilter', ], ], ], 'ListReadSetUploadPartsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReadSetUploadPartsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'parts' => [ 'shape' => 'ReadSetUploadPartList', ], ], ], 'ListReadSetsRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'maxResults' => [ 'shape' => 'ListReadSetsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ReadSetFilter', ], ], ], 'ListReadSetsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReadSetsResponse' => [ 'type' => 'structure', 'required' => [ 'readSets', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'readSets' => [ 'shape' => 'ReadSetList', ], ], ], 'ListReferenceImportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'referenceStoreId', ], 'members' => [ 'maxResults' => [ 'shape' => 'ListReferenceImportJobsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'referenceStoreId', ], 'filter' => [ 'shape' => 'ImportReferenceFilter', ], ], ], 'ListReferenceImportJobsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReferenceImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'importJobs' => [ 'shape' => 'ImportReferenceJobList', ], ], ], 'ListReferenceStoresRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListReferenceStoresRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ReferenceStoreFilter', ], ], ], 'ListReferenceStoresRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReferenceStoresResponse' => [ 'type' => 'structure', 'required' => [ 'referenceStores', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'referenceStores' => [ 'shape' => 'ReferenceStoreDetailList', ], ], ], 'ListReferencesRequest' => [ 'type' => 'structure', 'required' => [ 'referenceStoreId', ], 'members' => [ 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'referenceStoreId', ], 'maxResults' => [ 'shape' => 'ListReferencesRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ReferenceFilter', ], ], ], 'ListReferencesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListReferencesResponse' => [ 'type' => 'structure', 'required' => [ 'references', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'references' => [ 'shape' => 'ReferenceList', ], ], ], 'ListRunGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RunGroupName', 'location' => 'querystring', 'locationName' => 'name', ], 'startingToken' => [ 'shape' => 'RunGroupListToken', 'location' => 'querystring', 'locationName' => 'startingToken', ], 'maxResults' => [ 'shape' => 'ListRunGroupsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRunGroupsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRunGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'RunGroupList', ], 'nextToken' => [ 'shape' => 'RunGroupListToken', ], ], ], 'ListRunTasksRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RunId', 'location' => 'uri', 'locationName' => 'id', ], 'status' => [ 'shape' => 'TaskStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'startingToken' => [ 'shape' => 'TaskListToken', 'location' => 'querystring', 'locationName' => 'startingToken', ], 'maxResults' => [ 'shape' => 'ListRunTasksRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRunTasksRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRunTasksResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'TaskList', ], 'nextToken' => [ 'shape' => 'TaskListToken', ], ], ], 'ListRunsRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RunName', 'location' => 'querystring', 'locationName' => 'name', ], 'runGroupId' => [ 'shape' => 'RunGroupId', 'location' => 'querystring', 'locationName' => 'runGroupId', ], 'startingToken' => [ 'shape' => 'RunListToken', 'location' => 'querystring', 'locationName' => 'startingToken', ], 'maxResults' => [ 'shape' => 'ListRunsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'status' => [ 'shape' => 'RunStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListRunsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRunsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'RunList', ], 'nextToken' => [ 'shape' => 'RunListToken', ], ], ], 'ListSequenceStoresRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSequenceStoresRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'SequenceStoreFilter', ], ], ], 'ListSequenceStoresRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSequenceStoresResponse' => [ 'type' => 'structure', 'required' => [ 'sequenceStores', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sequenceStores' => [ 'shape' => 'SequenceStoreDetailList', ], ], ], 'ListSharesRequest' => [ 'type' => 'structure', 'required' => [ 'resourceOwner', ], 'members' => [ 'resourceOwner' => [ 'shape' => 'ResourceOwner', ], 'filter' => [ 'shape' => 'Filter', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSharesResponse' => [ 'type' => 'structure', 'required' => [ 'shares', ], 'members' => [ 'shares' => [ 'shape' => 'ShareDetailsList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListVariantImportJobsFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'JobStatus', ], 'storeName' => [ 'shape' => 'String', ], ], ], 'ListVariantImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListVariantImportJobsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ids' => [ 'shape' => 'ListVariantImportJobsRequestIdsList', ], 'nextToken' => [ 'shape' => 'ListVariantImportJobsRequestNextTokenString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ListVariantImportJobsFilter', ], ], ], 'ListVariantImportJobsRequestIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifier', ], 'max' => 20, 'min' => 1, ], 'ListVariantImportJobsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListVariantImportJobsRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListVariantImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'variantImportJobs' => [ 'shape' => 'VariantImportJobItems', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListVariantStoresFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'StoreStatus', ], ], ], 'ListVariantStoresRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListVariantStoresRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ids' => [ 'shape' => 'ListVariantStoresRequestIdsList', ], 'nextToken' => [ 'shape' => 'ListVariantStoresRequestNextTokenString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'filter' => [ 'shape' => 'ListVariantStoresFilter', ], ], ], 'ListVariantStoresRequestIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifier', ], 'max' => 20, 'min' => 1, ], 'ListVariantStoresRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListVariantStoresRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListVariantStoresResponse' => [ 'type' => 'structure', 'members' => [ 'variantStores' => [ 'shape' => 'VariantStoreItems', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListWorkflowsRequest' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'WorkflowType', 'location' => 'querystring', 'locationName' => 'type', ], 'name' => [ 'shape' => 'WorkflowName', 'location' => 'querystring', 'locationName' => 'name', ], 'startingToken' => [ 'shape' => 'WorkflowListToken', 'location' => 'querystring', 'locationName' => 'startingToken', ], 'maxResults' => [ 'shape' => 'ListWorkflowsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListWorkflowsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'WorkflowList', ], 'nextToken' => [ 'shape' => 'WorkflowListToken', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'Md5' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}||\\p{N}]+', ], 'MultipartReadSetUploadList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultipartReadSetUploadListItem', ], ], 'MultipartReadSetUploadListItem' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'uploadId', 'sourceFileType', 'subjectId', 'sampleId', 'generatedFrom', 'referenceArn', 'creationTime', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'sourceFileType' => [ 'shape' => 'FileType', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'generatedFrom' => [ 'shape' => 'GeneratedFrom', ], 'referenceArn' => [ 'shape' => 'ReferenceArn', ], 'name' => [ 'shape' => 'ReadSetName', ], 'description' => [ 'shape' => 'ReadSetDescription', ], 'tags' => [ 'shape' => 'TagMap', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 6144, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'NotSupportedOperationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 405, 'senderFault' => true, ], 'exception' => true, ], 'PrimitiveBoolean' => [ 'type' => 'boolean', ], 'Quote' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'QuoteAll' => [ 'type' => 'boolean', ], 'Range' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{N}||\\p{P}]+', ], 'RangeNotSatisfiableException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 416, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ReadOptions' => [ 'type' => 'structure', 'members' => [ 'sep' => [ 'shape' => 'Separator', ], 'encoding' => [ 'shape' => 'Encoding', ], 'quote' => [ 'shape' => 'Quote', ], 'quoteAll' => [ 'shape' => 'QuoteAll', ], 'escape' => [ 'shape' => 'EscapeChar', ], 'escapeQuotes' => [ 'shape' => 'EscapeQuotes', ], 'comment' => [ 'shape' => 'CommentChar', ], 'header' => [ 'shape' => 'Header', ], 'lineSep' => [ 'shape' => 'LineSep', ], ], ], 'ReadSetActivationJobItemStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'FINISHED', 'FAILED', ], ], 'ReadSetActivationJobStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'IN_PROGRESS', 'CANCELLING', 'CANCELLED', 'FAILED', 'COMPLETED', 'COMPLETED_WITH_FAILURES', ], ], 'ReadSetArn' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => 'arn:.+', ], 'ReadSetBatchError' => [ 'type' => 'structure', 'required' => [ 'id', 'code', 'message', ], 'members' => [ 'id' => [ 'shape' => 'ReadSetId', ], 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ReadSetBatchErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReadSetBatchError', ], ], 'ReadSetDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'ReadSetExportJobItemStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'FINISHED', 'FAILED', ], ], 'ReadSetExportJobStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'IN_PROGRESS', 'CANCELLING', 'CANCELLED', 'FAILED', 'COMPLETED', 'COMPLETED_WITH_FAILURES', ], ], 'ReadSetFile' => [ 'type' => 'string', 'enum' => [ 'SOURCE1', 'SOURCE2', 'INDEX', ], ], 'ReadSetFiles' => [ 'type' => 'structure', 'members' => [ 'source1' => [ 'shape' => 'FileInformation', ], 'source2' => [ 'shape' => 'FileInformation', ], 'index' => [ 'shape' => 'FileInformation', ], ], ], 'ReadSetFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ReadSetName', ], 'status' => [ 'shape' => 'ReadSetStatus', ], 'referenceArn' => [ 'shape' => 'ReferenceArnFilter', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'generatedFrom' => [ 'shape' => 'GeneratedFrom', ], 'creationType' => [ 'shape' => 'CreationType', ], ], ], 'ReadSetId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'ReadSetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReadSetId', ], 'max' => 100, 'min' => 1, ], 'ReadSetImportJobItemStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'FINISHED', 'FAILED', ], ], 'ReadSetImportJobStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'IN_PROGRESS', 'CANCELLING', 'CANCELLED', 'FAILED', 'COMPLETED', 'COMPLETED_WITH_FAILURES', ], ], 'ReadSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReadSetListItem', ], ], 'ReadSetListItem' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'sequenceStoreId', 'status', 'fileType', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ReadSetId', ], 'arn' => [ 'shape' => 'ReadSetArn', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'status' => [ 'shape' => 'ReadSetStatus', ], 'name' => [ 'shape' => 'ReadSetName', ], 'description' => [ 'shape' => 'ReadSetDescription', ], 'referenceArn' => [ 'shape' => 'ReferenceArn', ], 'fileType' => [ 'shape' => 'FileType', ], 'sequenceInformation' => [ 'shape' => 'SequenceInformation', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'statusMessage' => [ 'shape' => 'ReadSetStatusMessage', ], 'creationType' => [ 'shape' => 'CreationType', ], 'etag' => [ 'shape' => 'ETag', ], ], ], 'ReadSetName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'ReadSetPartSource' => [ 'type' => 'string', 'enum' => [ 'SOURCE1', 'SOURCE2', ], ], 'ReadSetPartStreamingBlob' => [ 'type' => 'blob', 'requiresLength' => true, 'streaming' => true, ], 'ReadSetS3Access' => [ 'type' => 'structure', 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'ReadSetStatus' => [ 'type' => 'string', 'enum' => [ 'ARCHIVED', 'ACTIVATING', 'ACTIVE', 'DELETING', 'DELETED', 'PROCESSING_UPLOAD', 'UPLOAD_FAILED', ], ], 'ReadSetStatusMessage' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'ReadSetStreamingBlob' => [ 'type' => 'blob', 'streaming' => true, ], 'ReadSetUploadPartList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReadSetUploadPartListItem', ], ], 'ReadSetUploadPartListFilter' => [ 'type' => 'structure', 'members' => [ 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ReadSetUploadPartListItem' => [ 'type' => 'structure', 'required' => [ 'partNumber', 'partSize', 'partSource', 'checksum', ], 'members' => [ 'partNumber' => [ 'shape' => 'ReadSetUploadPartListItemPartNumberInteger', ], 'partSize' => [ 'shape' => 'ReadSetUploadPartListItemPartSizeLong', ], 'partSource' => [ 'shape' => 'ReadSetPartSource', ], 'checksum' => [ 'shape' => 'String', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ReadSetUploadPartListItemPartNumberInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'ReadSetUploadPartListItemPartSizeLong' => [ 'type' => 'long', 'box' => true, 'max' => 5368709120, 'min' => 1, ], 'ReferenceArn' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => 'arn:.+', ], 'ReferenceArnFilter' => [ 'type' => 'string', 'max' => 127, 'min' => 0, 'pattern' => '$|^arn:.+', ], 'ReferenceDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'ReferenceFile' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'INDEX', ], ], 'ReferenceFiles' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'FileInformation', ], 'index' => [ 'shape' => 'FileInformation', ], ], ], 'ReferenceFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ReferenceName', ], 'md5' => [ 'shape' => 'Md5', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ReferenceId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'ReferenceImportJobItemStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'FINISHED', 'FAILED', ], ], 'ReferenceImportJobStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'IN_PROGRESS', 'CANCELLING', 'CANCELLED', 'FAILED', 'COMPLETED', 'COMPLETED_WITH_FAILURES', ], ], 'ReferenceItem' => [ 'type' => 'structure', 'members' => [ 'referenceArn' => [ 'shape' => 'ReferenceArn', ], ], 'union' => true, ], 'ReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReferenceListItem', ], ], 'ReferenceListItem' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'referenceStoreId', 'md5', 'creationTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'ReferenceId', ], 'arn' => [ 'shape' => 'ReferenceArn', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', ], 'md5' => [ 'shape' => 'Md5', ], 'status' => [ 'shape' => 'ReferenceStatus', ], 'name' => [ 'shape' => 'ReferenceName', ], 'description' => [ 'shape' => 'ReferenceDescription', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ReferenceName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'ReferenceStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETING', 'DELETED', ], ], 'ReferenceStoreArn' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => 'arn:.+', ], 'ReferenceStoreDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'ReferenceStoreDetail' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'creationTime', ], 'members' => [ 'arn' => [ 'shape' => 'ReferenceStoreArn', ], 'id' => [ 'shape' => 'ReferenceStoreId', ], 'name' => [ 'shape' => 'ReferenceStoreName', ], 'description' => [ 'shape' => 'ReferenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ReferenceStoreDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReferenceStoreDetail', ], ], 'ReferenceStoreFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ReferenceStoreName', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ReferenceStoreId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'ReferenceStoreName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'ReferenceStreamingBlob' => [ 'type' => 'blob', 'streaming' => true, ], 'RequestTimeoutException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 408, 'senderFault' => true, ], 'exception' => true, ], 'ResourceId' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceOwner' => [ 'type' => 'string', 'enum' => [ 'SELF', 'OTHER', ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*', ], 'RunArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:.+', ], 'RunExport' => [ 'type' => 'string', 'enum' => [ 'DEFINITION', ], 'max' => 64, 'min' => 1, ], 'RunExportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RunExport', ], 'max' => 32, 'min' => 0, ], 'RunFailureReason' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunGroupArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:.+', ], 'RunGroupId' => [ 'type' => 'string', 'max' => 18, 'min' => 1, 'pattern' => '[0-9]+', ], 'RunGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RunGroupListItem', ], ], 'RunGroupListItem' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RunGroupArn', ], 'id' => [ 'shape' => 'RunGroupId', ], 'name' => [ 'shape' => 'RunGroupName', ], 'maxCpus' => [ 'shape' => 'RunGroupListItemMaxCpusInteger', ], 'maxRuns' => [ 'shape' => 'RunGroupListItemMaxRunsInteger', ], 'maxDuration' => [ 'shape' => 'RunGroupListItemMaxDurationInteger', ], 'creationTime' => [ 'shape' => 'RunGroupTimestamp', ], 'maxGpus' => [ 'shape' => 'RunGroupListItemMaxGpusInteger', ], ], ], 'RunGroupListItemMaxCpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'RunGroupListItemMaxDurationInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'RunGroupListItemMaxGpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'RunGroupListItemMaxRunsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'RunGroupListToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunGroupRequestId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunGroupTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'RunId' => [ 'type' => 'string', 'max' => 18, 'min' => 1, 'pattern' => '[0-9]+', ], 'RunLeftNormalization' => [ 'type' => 'boolean', ], 'RunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RunListItem', ], ], 'RunListItem' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RunArn', ], 'id' => [ 'shape' => 'RunId', ], 'status' => [ 'shape' => 'RunStatus', ], 'workflowId' => [ 'shape' => 'WorkflowId', ], 'name' => [ 'shape' => 'RunName', ], 'priority' => [ 'shape' => 'RunListItemPriorityInteger', ], 'storageCapacity' => [ 'shape' => 'RunListItemStorageCapacityInteger', ], 'creationTime' => [ 'shape' => 'RunTimestamp', ], 'startTime' => [ 'shape' => 'RunTimestamp', ], 'stopTime' => [ 'shape' => 'RunTimestamp', ], 'storageType' => [ 'shape' => 'StorageType', ], ], ], 'RunListItemPriorityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'RunListItemStorageCapacityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'RunListToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunLogLevel' => [ 'type' => 'string', 'enum' => [ 'OFF', 'FATAL', 'ERROR', 'ALL', ], 'max' => 64, 'min' => 1, ], 'RunLogLocation' => [ 'type' => 'structure', 'members' => [ 'engineLogStream' => [ 'shape' => 'EngineLogStream', ], 'runLogStream' => [ 'shape' => 'RunLogStream', ], ], ], 'RunLogStream' => [ 'type' => 'string', 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunOutputUri' => [ 'type' => 'string', 'max' => 750, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunParameters' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'RunRequestId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunResourceDigest' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunResourceDigestKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunResourceDigests' => [ 'type' => 'map', 'key' => [ 'shape' => 'RunResourceDigestKey', ], 'value' => [ 'shape' => 'RunResourceDigest', ], ], 'RunRetentionMode' => [ 'type' => 'string', 'enum' => [ 'RETAIN', 'REMOVE', ], 'max' => 64, 'min' => 1, ], 'RunRoleArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:.+', ], 'RunStartedBy' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RunStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'STARTING', 'RUNNING', 'STOPPING', 'COMPLETED', 'DELETED', 'CANCELLED', 'FAILED', ], 'max' => 64, 'min' => 1, ], 'RunStatusMessage' => [ 'type' => 'string', 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'RunTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'RunUuid' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'S3AccessPointArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:[^:]*:s3:[^:]*:[^:]*:accesspoint/.*', ], 'S3Destination' => [ 'type' => 'string', 'pattern' => 's3://([a-z0-9][a-z0-9-.]{1,61}[a-z0-9])/?((.{1,1024})/)?', ], 'S3Uri' => [ 'type' => 'string', 'pattern' => 's3://([a-z0-9][a-z0-9-.]{1,61}[a-z0-9])/(.{1,1024})', ], 'SampleId' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'SchemaItem' => [ 'type' => 'map', 'key' => [ 'shape' => 'SchemaItemKeyString', ], 'value' => [ 'shape' => 'SchemaValueType', ], 'max' => 1, 'min' => 1, ], 'SchemaItemKeyString' => [ 'type' => 'string', 'pattern' => '[a-z0-9_]{1,255}', ], 'SchemaValueType' => [ 'type' => 'string', 'enum' => [ 'LONG', 'INT', 'STRING', 'FLOAT', 'DOUBLE', 'BOOLEAN', ], ], 'Separator' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'SequenceInformation' => [ 'type' => 'structure', 'members' => [ 'totalReadCount' => [ 'shape' => 'Long', ], 'totalBaseCount' => [ 'shape' => 'Long', ], 'generatedFrom' => [ 'shape' => 'GeneratedFrom', ], 'alignment' => [ 'shape' => 'String', ], ], ], 'SequenceStoreArn' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => 'arn:.+', ], 'SequenceStoreDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'SequenceStoreDetail' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'creationTime', ], 'members' => [ 'arn' => [ 'shape' => 'SequenceStoreArn', ], 'id' => [ 'shape' => 'SequenceStoreId', ], 'name' => [ 'shape' => 'SequenceStoreName', ], 'description' => [ 'shape' => 'SequenceStoreDescription', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'fallbackLocation' => [ 'shape' => 'S3Destination', ], 'eTagAlgorithmFamily' => [ 'shape' => 'ETagAlgorithmFamily', ], ], ], 'SequenceStoreDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SequenceStoreDetail', ], ], 'SequenceStoreFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'SequenceStoreName', ], 'createdAfter' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBefore' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'SequenceStoreId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'SequenceStoreName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'SequenceStoreS3Access' => [ 'type' => 'structure', 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], 's3AccessPointArn' => [ 'shape' => 'S3AccessPointArn', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ShareDetails' => [ 'type' => 'structure', 'members' => [ 'shareId' => [ 'shape' => 'String', ], 'resourceArn' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'principalSubscriber' => [ 'shape' => 'String', ], 'ownerId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'ShareStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'shareName' => [ 'shape' => 'ShareName', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], ], ], 'ShareDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShareDetails', ], ], 'ShareName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ShareResourceType' => [ 'type' => 'string', 'enum' => [ 'VARIANT_STORE', 'ANNOTATION_STORE', 'WORKFLOW', ], ], 'ShareStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVATING', 'ACTIVE', 'DELETING', 'DELETED', 'FAILED', ], ], 'SourceFiles' => [ 'type' => 'structure', 'required' => [ 'source1', ], 'members' => [ 'source1' => [ 'shape' => 'S3Uri', ], 'source2' => [ 'shape' => 'S3Uri', ], ], ], 'SseConfig' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'EncryptionType', ], 'keyArn' => [ 'shape' => 'SseConfigKeyArnString', ], ], ], 'SseConfigKeyArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '.*arn:([^: ]*):([^: ]*):([^: ]*):([0-9]{12}):([^: ]*).*', ], 'StartAnnotationImportRequest' => [ 'type' => 'structure', 'required' => [ 'destinationName', 'roleArn', 'items', ], 'members' => [ 'destinationName' => [ 'shape' => 'StoreName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'items' => [ 'shape' => 'AnnotationImportItemSources', ], 'versionName' => [ 'shape' => 'VersionName', ], 'formatOptions' => [ 'shape' => 'FormatOptions', ], 'runLeftNormalization' => [ 'shape' => 'RunLeftNormalization', ], 'annotationFields' => [ 'shape' => 'AnnotationFieldMap', ], ], ], 'StartAnnotationImportResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'ResourceId', ], ], ], 'StartReadSetActivationJobRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'sources', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'sources' => [ 'shape' => 'StartReadSetActivationJobRequestSourcesList', ], ], ], 'StartReadSetActivationJobRequestSourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartReadSetActivationJobSourceItem', ], 'max' => 20, 'min' => 1, ], 'StartReadSetActivationJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ActivationJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'status' => [ 'shape' => 'ReadSetActivationJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'StartReadSetActivationJobSourceItem' => [ 'type' => 'structure', 'required' => [ 'readSetId', ], 'members' => [ 'readSetId' => [ 'shape' => 'ReadSetId', ], ], ], 'StartReadSetExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'destination', 'roleArn', 'sources', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'destination' => [ 'shape' => 'S3Destination', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'sources' => [ 'shape' => 'StartReadSetExportJobRequestSourcesList', ], ], ], 'StartReadSetExportJobRequestSourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportReadSet', ], 'max' => 100, 'min' => 1, ], 'StartReadSetExportJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'destination', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ExportJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'destination' => [ 'shape' => 'S3Destination', ], 'status' => [ 'shape' => 'ReadSetExportJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'StartReadSetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'roleArn', 'sources', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'sources' => [ 'shape' => 'StartReadSetImportJobRequestSourcesList', ], ], ], 'StartReadSetImportJobRequestSourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartReadSetImportJobSourceItem', ], 'max' => 100, 'min' => 1, ], 'StartReadSetImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'sequenceStoreId', 'roleArn', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', ], 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ReadSetImportJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'StartReadSetImportJobSourceItem' => [ 'type' => 'structure', 'required' => [ 'sourceFiles', 'sourceFileType', 'subjectId', 'sampleId', ], 'members' => [ 'sourceFiles' => [ 'shape' => 'SourceFiles', ], 'sourceFileType' => [ 'shape' => 'FileType', ], 'subjectId' => [ 'shape' => 'SubjectId', ], 'sampleId' => [ 'shape' => 'SampleId', ], 'generatedFrom' => [ 'shape' => 'GeneratedFrom', ], 'referenceArn' => [ 'shape' => 'ReferenceArn', ], 'name' => [ 'shape' => 'ReadSetName', ], 'description' => [ 'shape' => 'ReadSetDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartReferenceImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'referenceStoreId', 'roleArn', 'sources', ], 'members' => [ 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', 'location' => 'uri', 'locationName' => 'referenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'sources' => [ 'shape' => 'StartReferenceImportJobRequestSourcesList', ], ], ], 'StartReferenceImportJobRequestSourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartReferenceImportJobSourceItem', ], 'max' => 100, 'min' => 1, ], 'StartReferenceImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'referenceStoreId', 'roleArn', 'status', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'ImportJobId', ], 'referenceStoreId' => [ 'shape' => 'ReferenceStoreId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ReferenceImportJobStatus', ], 'creationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'StartReferenceImportJobSourceItem' => [ 'type' => 'structure', 'required' => [ 'sourceFile', 'name', ], 'members' => [ 'sourceFile' => [ 'shape' => 'S3Uri', ], 'name' => [ 'shape' => 'ReferenceName', ], 'description' => [ 'shape' => 'ReferenceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartRunRequest' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'requestId', ], 'members' => [ 'workflowId' => [ 'shape' => 'WorkflowId', ], 'workflowType' => [ 'shape' => 'WorkflowType', ], 'runId' => [ 'shape' => 'RunId', ], 'roleArn' => [ 'shape' => 'RunRoleArn', ], 'name' => [ 'shape' => 'RunName', ], 'runGroupId' => [ 'shape' => 'RunGroupId', ], 'priority' => [ 'shape' => 'StartRunRequestPriorityInteger', ], 'parameters' => [ 'shape' => 'RunParameters', ], 'storageCapacity' => [ 'shape' => 'StartRunRequestStorageCapacityInteger', ], 'outputUri' => [ 'shape' => 'RunOutputUri', ], 'logLevel' => [ 'shape' => 'RunLogLevel', ], 'tags' => [ 'shape' => 'TagMap', ], 'requestId' => [ 'shape' => 'RunRequestId', 'idempotencyToken' => true, ], 'retentionMode' => [ 'shape' => 'RunRetentionMode', ], 'storageType' => [ 'shape' => 'StorageType', ], 'workflowOwnerId' => [ 'shape' => 'WorkflowOwnerId', ], ], ], 'StartRunRequestPriorityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'StartRunRequestStorageCapacityInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 0, ], 'StartRunResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RunArn', ], 'id' => [ 'shape' => 'RunId', ], 'status' => [ 'shape' => 'RunStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'uuid' => [ 'shape' => 'RunUuid', ], 'runOutputUri' => [ 'shape' => 'RunOutputUri', ], ], ], 'StartVariantImportRequest' => [ 'type' => 'structure', 'required' => [ 'destinationName', 'roleArn', 'items', ], 'members' => [ 'destinationName' => [ 'shape' => 'StoreName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'items' => [ 'shape' => 'VariantImportItemSources', ], 'runLeftNormalization' => [ 'shape' => 'RunLeftNormalization', ], 'annotationFields' => [ 'shape' => 'AnnotationFieldMap', ], ], ], 'StartVariantImportResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'ResourceId', ], ], ], 'StatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShareStatus', ], 'max' => 10, 'min' => 1, ], 'StatusMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'StorageType' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'DYNAMIC', ], 'max' => 64, 'min' => 1, ], 'StoreFormat' => [ 'type' => 'string', 'enum' => [ 'GFF', 'TSV', 'VCF', ], ], 'StoreName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '([a-z]){1}([a-z0-9_]){2,254}', ], 'StoreOptions' => [ 'type' => 'structure', 'members' => [ 'tsvStoreOptions' => [ 'shape' => 'TsvStoreOptions', ], ], 'union' => true, ], 'StoreStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'ACTIVE', 'FAILED', ], ], 'String' => [ 'type' => 'string', ], 'SubjectId' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:.+', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagResourceRequestTagsMap', ], ], ], 'TagResourceRequestTagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaskFailureReason' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'TaskId' => [ 'type' => 'string', 'max' => 18, 'min' => 1, 'pattern' => '[0-9]+', ], 'TaskInstanceType' => [ 'type' => 'string', 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'TaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskListItem', ], ], 'TaskListItem' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'status' => [ 'shape' => 'TaskStatus', ], 'name' => [ 'shape' => 'TaskName', ], 'cpus' => [ 'shape' => 'TaskListItemCpusInteger', ], 'memory' => [ 'shape' => 'TaskListItemMemoryInteger', ], 'creationTime' => [ 'shape' => 'TaskTimestamp', ], 'startTime' => [ 'shape' => 'TaskTimestamp', ], 'stopTime' => [ 'shape' => 'TaskTimestamp', ], 'gpus' => [ 'shape' => 'TaskListItemGpusInteger', ], 'instanceType' => [ 'shape' => 'TaskInstanceType', ], ], ], 'TaskListItemCpusInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'TaskListItemGpusInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'TaskListItemMemoryInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'TaskListToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'TaskLogStream' => [ 'type' => 'string', 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'TaskName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TaskStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'STARTING', 'RUNNING', 'STOPPING', 'COMPLETED', 'CANCELLED', 'FAILED', ], 'max' => 64, 'min' => 1, ], 'TaskStatusMessage' => [ 'type' => 'string', 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'TaskTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'TsvOptions' => [ 'type' => 'structure', 'members' => [ 'readOptions' => [ 'shape' => 'ReadOptions', ], ], ], 'TsvStoreOptions' => [ 'type' => 'structure', 'members' => [ 'annotationType' => [ 'shape' => 'AnnotationType', ], 'formatToHeader' => [ 'shape' => 'FormatToHeader', ], 'schema' => [ 'shape' => 'TsvStoreOptionsSchemaList', ], ], ], 'TsvStoreOptionsSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaItem', ], 'max' => 5000, 'min' => 1, ], 'TsvVersionOptions' => [ 'type' => 'structure', 'members' => [ 'annotationType' => [ 'shape' => 'AnnotationType', ], 'formatToHeader' => [ 'shape' => 'FormatToHeader', ], 'schema' => [ 'shape' => 'TsvVersionOptionsSchemaList', ], ], ], 'TsvVersionOptionsSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaItem', ], 'max' => 5000, 'min' => 1, ], 'TypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShareResourceType', ], 'max' => 10, 'min' => 1, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAnnotationStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateAnnotationStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'reference', 'status', 'name', 'description', 'creationTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'status' => [ 'shape' => 'StoreStatus', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'storeOptions' => [ 'shape' => 'StoreOptions', ], 'storeFormat' => [ 'shape' => 'StoreFormat', ], ], ], 'UpdateAnnotationStoreVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'versionName', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'versionName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'versionName', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateAnnotationStoreVersionResponse' => [ 'type' => 'structure', 'required' => [ 'storeId', 'id', 'status', 'name', 'versionName', 'description', 'creationTime', 'updateTime', ], 'members' => [ 'storeId' => [ 'shape' => 'ResourceId', ], 'id' => [ 'shape' => 'ResourceId', ], 'status' => [ 'shape' => 'VersionStatus', ], 'name' => [ 'shape' => 'StoreName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'description' => [ 'shape' => 'Description', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], ], ], 'UpdateRunGroupRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RunGroupId', 'location' => 'uri', 'locationName' => 'id', ], 'name' => [ 'shape' => 'RunGroupName', ], 'maxCpus' => [ 'shape' => 'UpdateRunGroupRequestMaxCpusInteger', ], 'maxRuns' => [ 'shape' => 'UpdateRunGroupRequestMaxRunsInteger', ], 'maxDuration' => [ 'shape' => 'UpdateRunGroupRequestMaxDurationInteger', ], 'maxGpus' => [ 'shape' => 'UpdateRunGroupRequestMaxGpusInteger', ], ], ], 'UpdateRunGroupRequestMaxCpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'UpdateRunGroupRequestMaxDurationInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'UpdateRunGroupRequestMaxGpusInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'UpdateRunGroupRequestMaxRunsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 1, ], 'UpdateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UpdateVariantStoreRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateVariantStoreResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'reference', 'status', 'name', 'description', 'creationTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'status' => [ 'shape' => 'StoreStatus', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], ], ], 'UpdateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkflowId', 'location' => 'uri', 'locationName' => 'id', ], 'name' => [ 'shape' => 'WorkflowName', ], 'description' => [ 'shape' => 'WorkflowDescription', ], ], ], 'UploadId' => [ 'type' => 'string', 'max' => 36, 'min' => 10, 'pattern' => '[0-9]+', ], 'UploadReadSetPartRequest' => [ 'type' => 'structure', 'required' => [ 'sequenceStoreId', 'uploadId', 'partSource', 'partNumber', 'payload', ], 'members' => [ 'sequenceStoreId' => [ 'shape' => 'SequenceStoreId', 'location' => 'uri', 'locationName' => 'sequenceStoreId', ], 'uploadId' => [ 'shape' => 'UploadId', 'location' => 'uri', 'locationName' => 'uploadId', ], 'partSource' => [ 'shape' => 'ReadSetPartSource', 'location' => 'querystring', 'locationName' => 'partSource', ], 'partNumber' => [ 'shape' => 'UploadReadSetPartRequestPartNumberInteger', 'location' => 'querystring', 'locationName' => 'partNumber', ], 'payload' => [ 'shape' => 'ReadSetPartStreamingBlob', ], ], 'payload' => 'payload', ], 'UploadReadSetPartRequestPartNumberInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'UploadReadSetPartResponse' => [ 'type' => 'structure', 'required' => [ 'checksum', ], 'members' => [ 'checksum' => [ 'shape' => 'String', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VariantImportItemDetail' => [ 'type' => 'structure', 'required' => [ 'source', 'jobStatus', ], 'members' => [ 'source' => [ 'shape' => 'S3Uri', ], 'jobStatus' => [ 'shape' => 'JobStatus', ], 'statusMessage' => [ 'shape' => 'JobStatusMsg', ], ], ], 'VariantImportItemDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariantImportItemDetail', ], 'min' => 1, ], 'VariantImportItemSource' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'source' => [ 'shape' => 'S3Uri', ], ], ], 'VariantImportItemSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariantImportItemSource', ], 'min' => 1, ], 'VariantImportJobItem' => [ 'type' => 'structure', 'required' => [ 'id', 'destinationName', 'roleArn', 'status', 'creationTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'destinationName' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'JobStatus', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'completionTime' => [ 'shape' => 'CompletionTime', ], 'runLeftNormalization' => [ 'shape' => 'RunLeftNormalization', ], 'annotationFields' => [ 'shape' => 'AnnotationFieldMap', ], ], ], 'VariantImportJobItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariantImportJobItem', ], ], 'VariantStoreItem' => [ 'type' => 'structure', 'required' => [ 'id', 'reference', 'status', 'storeArn', 'name', 'description', 'sseConfig', 'creationTime', 'updateTime', 'statusMessage', 'storeSizeBytes', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'reference' => [ 'shape' => 'ReferenceItem', ], 'status' => [ 'shape' => 'StoreStatus', ], 'storeArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'sseConfig' => [ 'shape' => 'SseConfig', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'storeSizeBytes' => [ 'shape' => 'Long', ], ], ], 'VariantStoreItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariantStoreItem', ], ], 'VcfOptions' => [ 'type' => 'structure', 'members' => [ 'ignoreQualField' => [ 'shape' => 'Boolean', ], 'ignoreFilterField' => [ 'shape' => 'Boolean', ], ], ], 'VersionDeleteError' => [ 'type' => 'structure', 'required' => [ 'versionName', 'message', ], 'members' => [ 'versionName' => [ 'shape' => 'VersionName', ], 'message' => [ 'shape' => 'String', ], ], ], 'VersionDeleteErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionDeleteError', ], ], 'VersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionName', ], 'max' => 10, 'min' => 1, ], 'VersionName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '([a-z]){1}([a-z0-9_]){2,254}', ], 'VersionOptions' => [ 'type' => 'structure', 'members' => [ 'tsvVersionOptions' => [ 'shape' => 'TsvVersionOptions', ], ], 'union' => true, ], 'VersionStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'ACTIVE', 'FAILED', ], ], 'WorkflowArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:.+', ], 'WorkflowDefinition' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowDigest' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'WorkflowEngine' => [ 'type' => 'string', 'enum' => [ 'WDL', 'NEXTFLOW', 'CWL', ], 'max' => 64, 'min' => 1, ], 'WorkflowExport' => [ 'type' => 'string', 'enum' => [ 'DEFINITION', ], 'max' => 64, 'min' => 1, ], 'WorkflowExportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowExport', ], 'max' => 32, 'min' => 0, ], 'WorkflowId' => [ 'type' => 'string', 'max' => 18, 'min' => 1, 'pattern' => '[0-9]+', ], 'WorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowListItem', ], ], 'WorkflowListItem' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'WorkflowArn', ], 'id' => [ 'shape' => 'WorkflowId', ], 'name' => [ 'shape' => 'WorkflowName', ], 'status' => [ 'shape' => 'WorkflowStatus', ], 'type' => [ 'shape' => 'WorkflowType', ], 'digest' => [ 'shape' => 'WorkflowDigest', ], 'creationTime' => [ 'shape' => 'WorkflowTimestamp', ], 'metadata' => [ 'shape' => 'WorkflowMetadata', ], ], ], 'WorkflowListToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowMain' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'WorkflowMetadataKey', ], 'value' => [ 'shape' => 'WorkflowMetadataValue', ], ], 'WorkflowMetadataKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'WorkflowMetadataValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'WorkflowName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowOwnerId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'WorkflowParameter' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'WorkflowParameterDescription', ], 'optional' => [ 'shape' => 'Boolean', ], ], ], 'WorkflowParameterDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowParameterName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowParameterTemplate' => [ 'type' => 'map', 'key' => [ 'shape' => 'WorkflowParameterName', ], 'value' => [ 'shape' => 'WorkflowParameter', ], 'max' => 1000, 'min' => 1, ], 'WorkflowRequestId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETED', 'FAILED', 'INACTIVE', ], 'max' => 64, 'min' => 1, ], 'WorkflowStatusMessage' => [ 'type' => 'string', 'pattern' => '[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+', ], 'WorkflowTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'WorkflowType' => [ 'type' => 'string', 'enum' => [ 'PRIVATE', 'READY2RUN', ], 'max' => 64, 'min' => 1, ], ],];
