<phpunit strict="true"
         beStrictAboutTestsThatDoNotTestAnything="true"
         beStrictAboutOutputDuringTests="true"
         checkForUnintentionallyCoveredCode="true"
         forceCoversAnnotation="true"
         bootstrap="tests/autoloader.php"
         verbose="true">
	<filter>
		<whitelist processUncoveredFilesFromWhitelist="false">
			<directory>Source/</directory>
			<directory>soap/</directory>
			<directory>src/</directory>
			<exclude>
				<directory>src/actionchains/views/</directory>
				<directory>src/actions/views/</directory>
				<directory>src/admin/views/</directory>
				<directory>src/assessments/views/</directory>
				<directory>src/ast/views/</directory>
				<directory>src/causalfactors/views/</directory>
				<directory>src/claims/views/</directory>
				<directory>src/contacts/views/</directory>
				<directory>src/documents/views/</directory>
				<directory>src/email/views/</directory>
				<directory>src/excelimport/views/</directory>
				<directory>src/formdesign/views/</directory>
				<directory>src/generic/views/</directory>
				<directory>src/incidents/views/</directory>
				<directory>src/json/views/</directory>
				<directory>src/library/views/</directory>
				<directory>src/login/views/</directory>
				<directory>src/med/views/</directory>
				<directory>src/organisations/views/</directory>
				<directory>src/policies/views/</directory>
				<directory>src/progressnotes/views/</directory>
				<directory>src/reasons/views/</directory>
				<directory>src/reports/views/</directory>
				<directory>src/respondents/views/</directory>
				<directory>src/riskregister/views/</directory>
				<directory>src/rootcauses/views/</directory>
				<directory>src/sabs/views/</directory>
				<directory>src/savedqueries/views/</directory>
				<directory>src/security/views/</directory>
				<directory>src/standards/views/</directory>
				<directory>src/tasks/views/</directory>
				<directory>src/users/views/</directory>
				<directory>src/weblinks/views/</directory>
				<directory>src/wordmergetemplate/views/</directory>
				<directory>app/views/</directory>
				<directory>app/controllers/</directory>
                <file>src/messagequeue/queues/notificationemails/worker.php</file>
			</exclude>
		</whitelist>
		<blacklist>
			<directory suffix=".php">.</directory>
		</blacklist>
	</filter>
    <listeners>
        <listener class="JohnKary\PHPUnit\Listener\SpeedTrapListener">
	        <arguments>
	            <array>
	                <element key="slowThreshold"> 
	                    <integer>100</integer>
	                </element>
	                <element key="reportLength"> 
	                    <integer>30</integer>
	                </element>
	            </array>
	        </arguments>
        </listener>
    </listeners>
</phpunit>