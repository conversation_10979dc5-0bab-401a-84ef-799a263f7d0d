{"name": "auth0/auth0-php", "description": "PHP SDK for Auth0 Authentication and Management APIs.", "license": "MIT", "type": "library", "keywords": ["auth0", "authentication", "authorization", "login", "auth", "jwt", "json web token", "jwk", "json web key", "o<PERSON>h", "openid", "secure", "protect", "api"], "authors": [{"name": "Auth0", "email": "<EMAIL>", "homepage": "https://auth0.com/"}], "homepage": "https://github.com/auth0/auth0-PHP", "require": {"php": "^8.1", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "php-http/multipart-stream-builder": "^1", "psr-discovery/all": "^1", "psr/http-client-implementation": "^1", "psr/http-factory-implementation": "^1", "psr/http-message-implementation": "^1"}, "require-dev": {"ergebnis/composer-normalize": "^2", "friendsofphp/php-cs-fixer": "^3", "mockery/mockery": "^1", "pestphp/pest": "^2", "phpstan/phpstan": "^1", "phpstan/phpstan-strict-rules": "^1", "psr-mock/http": "^1", "rector/rector": "0.17.6", "spatie/ray": "^1", "symfony/cache": "^4 || ^5 || ^6", "symfony/event-dispatcher": "^4 || ^5 || ^6", "vimeo/psalm": "^5", "wikimedia/composer-merge-plugin": "^2"}, "suggest": {"psr/cache-implementation": "(PSR-6 Cache) Improve performance by avoiding making redundant network requests.", "psr/event-dispatcher-implementation": "(PSR-14 Event Dispatcher) Observe and react to events when they occur."}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Auth0\\SDK\\": "src/"}}, "autoload-dev": {"psr-4": {"Auth0\\Tests\\": "tests/"}}, "config": {"allow-plugins": {"ergebnis/composer-normalize": true, "infection/extension-installer": false, "pestphp/pest-plugin": true, "php-http/discovery": false, "wikimedia/composer-merge-plugin": true}, "optimize-autoloader": true, "preferred-install": "dist", "process-timeout": 0, "sort-packages": true}, "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "scripts": {"pest": ["@putenv XDEBUG_MODE=coverage", "@php vendor/bin/pest --colors=always --strict-global-state --fail-on-risky --fail-on-warning --coverage --strict-coverage --compact"], "pest:ci": ["@pest:fast --order-by=random --no-progress"], "pest:fast": ["@pest --parallel"], "phpcs": ["@putenv PHP_CS_FIXER_IGNORE_ENV=1", "@php vendor/bin/php-cs-fixer fix --dry-run --diff"], "phpcs:fix": "@php vendor/bin/php-cs-fixer fix", "phpstan": "@php vendor/bin/phpstan analyze", "psalm": "@php vendor/bin/psalm", "psalm:fix": "@php vendor/bin/psalter --issues=all", "rector": "@php vendor/bin/rector process src --dry-run", "rector:fix": "@php vendor/bin/rector process src", "test": ["@pest", "@phpstan", "@psalm", "@rector", "@phpcs"]}}