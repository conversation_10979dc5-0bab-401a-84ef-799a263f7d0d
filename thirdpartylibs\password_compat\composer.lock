{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at http://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file"], "hash": "15ab0fbb84a186f801e4eedb4d86cb0a", "packages": [], "packages-dev": [{"name": "phpunit/php-code-coverage", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "a05618d03c60eb07b37432583aabf80ae5f5f447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/a05618d03c60eb07b37432583aabf80ae5f5f447", "reference": "a05618d03c60eb07b37432583aabf80ae5f5f447", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3.1", "phpunit/php-text-template": "~1.2", "phpunit/php-token-stream": "~1.2.2", "sebastian/environment": "~1.0", "sebastian/version": "~1.0.3"}, "require-dev": {"ext-xdebug": ">=2.1.4", "phpunit/phpunit": ">=4.0.0,<4.1.0"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2014-03-20 16:12:25"}, {"name": "phpunit/php-file-iterator", "version": "1.3.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "acd690379117b042d1c8af1fafd61bde001bf6bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/acd690379117b042d1c8af1fafd61bde001bf6bb", "reference": "acd690379117b042d1c8af1fafd61bde001bf6bb", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["File/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2013-10-10 15:34:57"}, {"name": "phpunit/php-text-template", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "206dfefc0ffe9cebf65c413e3d0e809c82fbf00a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/206dfefc0ffe9cebf65c413e3d0e809c82fbf00a", "reference": "206dfefc0ffe9cebf65c413e3d0e809c82fbf00a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["Text/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2014-01-30 17:20:04"}, {"name": "phpunit/php-timer", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "19689d4354b295ee3d8c54b4f42c3efb69cbc17c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/19689d4354b295ee3d8c54b4f42c3efb69cbc17c", "reference": "19689d4354b295ee3d8c54b4f42c3efb69cbc17c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["PHP/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2013-08-02 07:42:54"}, {"name": "phpunit/php-token-stream", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "ad4e1e23ae01b483c16f600ff1bebec184588e32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/ad4e1e23ae01b483c16f600ff1bebec184588e32", "reference": "ad4e1e23ae01b483c16f600ff1bebec184588e32", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["PHP/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2014-03-03 05:10:30"}, {"name": "phpunit/phpunit", "version": "4.0.12", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "e7631ea91f9e41149c55527047b5dee94538ae1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/e7631ea91f9e41149c55527047b5dee94538ae1d", "reference": "e7631ea91f9e41149c55527047b5dee94538ae1d", "shasum": ""}, "require": {"ext-dom": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpunit/php-code-coverage": ">=2.0.0,<2.1.0", "phpunit/php-file-iterator": "~1.3.1", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "~1.0.2", "phpunit/phpunit-mock-objects": ">=2.0.0,<2.1.0", "sebastian/diff": "~1.1", "sebastian/environment": "~1.0", "sebastian/exporter": "~1.0.1", "sebastian/version": "~1.0.3", "symfony/yaml": "~2.0"}, "suggest": {"ext-json": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["", "../../symfony/yaml/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "http://www.phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2014-03-20 16:22:50"}, {"name": "phpunit/phpunit-mock-objects", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "c5e6274b8f2bf983cf883bb375cf44f99aff200e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/c5e6274b8f2bf983cf883bb375cf44f99aff200e", "reference": "c5e6274b8f2bf983cf883bb375cf44f99aff200e", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-text-template": "~1.2"}, "require-dev": {"phpunit/phpunit": ">=4.0.0,<4.1.0"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2014-03-18 08:56:48"}, {"name": "sebastian/diff", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "1e091702a5a38e6b4c1ba9ca816e3dd343df2e2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/1e091702a5a38e6b4c1ba9ca816e3dd343df2e2d", "reference": "1e091702a5a38e6b4c1ba9ca816e3dd343df2e2d", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "http://www.github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2013-08-03 16:46:33"}, {"name": "sebastian/environment", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "79517609ec01139cd7e9fded0dd7ce08c952ef6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/79517609ec01139cd7e9fded0dd7ce08c952ef6a", "reference": "79517609ec01139cd7e9fded0dd7ce08c952ef6a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "4.0.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2014-02-18 16:17:19"}, {"name": "sebastian/exporter", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "1f9a98e6f5dfe0524cb8c6166f7c82f3e9ae1529"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/1f9a98e6f5dfe0524cb8c6166f7c82f3e9ae1529", "reference": "1f9a98e6f5dfe0524cb8c6166f7c82f3e9ae1529", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "4.0.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2014-02-16 08:26:31"}, {"name": "sebastian/version", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "b6e1f0cf6b9e1ec409a0d3e2f2a5fb0998e36b43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/b6e1f0cf6b9e1ec409a0d3e2f2a5fb0998e36b43", "reference": "b6e1f0cf6b9e1ec409a0d3e2f2a5fb0998e36b43", "shasum": ""}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2014-03-07 15:35:33"}, {"name": "symfony/yaml", "version": "v2.4.2", "target-dir": "Symfony/Component/Yaml", "source": {"type": "git", "url": "https://github.com/symfony/Yaml.git", "reference": "bb6ddaf8956139d1b8c360b4b713ed0138e876b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Yaml/zipball/bb6ddaf8956139d1b8c360b4b713ed0138e876b3", "reference": "bb6ddaf8956139d1b8c360b4b713ed0138e876b3", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-0": {"Symfony\\Component\\Yaml\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "http://symfony.com", "time": "2014-01-07 13:28:54"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "platform": [], "platform-dev": []}