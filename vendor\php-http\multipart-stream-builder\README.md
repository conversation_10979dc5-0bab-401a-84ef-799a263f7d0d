# PSR-7 Multipart Stream Builder

[![Latest Version](https://img.shields.io/github/release/php-http/multipart-stream-builder.svg?style=flat-square)](https://github.com/php-http/multipart-stream-builder/releases)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square)](LICENSE)
[![Build Status](https://github.com/php-http/multipart-stream-builder/actions/workflows/tests.yml/badge.svg)](https://github.com/php-http/multipart-stream-builder/actions/workflows/tests.yml)
[![Code Coverage](https://img.shields.io/scrutinizer/coverage/g/php-http/multipart-stream-builder.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/multipart-stream-builder)
[![Quality Score](https://img.shields.io/scrutinizer/g/php-http/multipart-stream-builder.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/multipart-stream-builder)
[![Total Downloads](https://img.shields.io/packagist/dt/php-http/multipart-stream-builder.svg?style=flat-square)](https://packagist.org/packages/php-http/multipart-stream-builder)

**A builder for Multipart PSR-7 Streams. The builder create streams independenly form any PSR-7 implementation.**


## Install

Via Composer

``` bash
$ composer require php-http/multipart-stream-builder
```

## Documentation

Please see the [official documentation](http://php-http.readthedocs.org/en/latest/components/multipart-stream-builder.html).


## Contributing

Please see [CONTRIBUTING](CONTRIBUTING.md) and [CONDUCT](CONDUCT.md) for details.


## Security

If you discover any security related issues, please contact us at [<EMAIL>](mailto:<EMAIL>).


## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.
