<?php
    session_start();
    if ($_SESSION['logged_in'] && $_SESSION['AdminUser']) {
        $config = [
            'PHP Version' => phpversion(),
            'Loaded php.ini file' => php_ini_loaded_file(),
            'Scanned .ini files' => php_ini_scanned_files(),
            'Extension Directory' => ini_get("extension_dir"),
            'Include Path' => get_include_path(),
            'Upload Temp Directory' => ini_get("upload_tmp_dir"),
            'Session Save Path' => ini_get("session.save_path"),
            'Loaded Extensions' => implode(", ", get_loaded_extensions()),
            'Disabled Functions' => ini_get("disable_functions"),
            'Memory Limit' => ini_get("memory_limit"),
            'max_execution_time' => ini_get("max_execution_time"),
            'max_input_time' => ini_get("max_input_time"),
            'post_max_size' => ini_get("post_max_size"),
            'upload_max_filesize' => ini_get("upload_max_filesize"),
            'max_file_uploads' => ini_get("max_file_uploads"),
            'error_reporting' => ini_get("error_reporting"),
            'display_errors' => ini_get("display_errors"),
            'log_errors' => ini_get("log_errors"),
            'error_log' => ini_get("error_log"),
            'session.gc_maxlifetime' => ini_get("session.gc_maxlifetime"),
            'session.cookie_lifetime' => ini_get("session.cookie_lifetime"),
            'session.use_cookies' => ini_get("session.use_cookies"),
            'session.use_only_cookies' => ini_get("session.use_only_cookies"),
            'session.cookie_secure' => ini_get("session.cookie_secure"),
            'session.cookie_httponly' => ini_get("session.cookie_httponly"),
            'session.cookie_samesite' => ini_get("session.cookie_samesite"),
            'session.serialize_handler' => ini_get("session.serialize_handler"),
            'session.save_handler' => ini_get("session.save_handler"),
            'cURL Installed' => extension_loaded('curl') ? 'Yes' : 'No',
            'mbstring Installed' => extension_loaded('mbstring') ? 'Yes' : 'No',
            'GD Installed' => extension_loaded('gd') ? 'Yes' : 'No',
            'PDO Drivers' => implode(", ", PDO::getAvailableDrivers()),
            'Environment PATH' => getenv('PATH'),
            'User Environment' => getenv('USER') ?: getenv('USERNAME'),
            'Error Handler' => is_callable(set_error_handler(function(){})) ? 'Set' : 'Not set',
            'Exception Handler' => is_callable(set_exception_handler(function(){})) ? 'Set' : 'Not set',
            'OPcache Enabled' => (function_exists('opcache_get_status') && opcache_get_status(false)['opcache_enabled']) ? 'Yes' : 'No',
            'PHP SAPI' => php_sapi_name(),
            'OS' => PHP_OS,
            'System' => php_uname(),
            'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'N/A',
            'HTTPS Enabled' => (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'Yes' : 'No',
            'Server Protocol' => $_SERVER['SERVER_PROTOCOL'] ?? 'N/A',
            'Server Name' => $_SERVER['SERVER_NAME'] ?? 'N/A',
            'Server Port' => $_SERVER['SERVER_PORT'] ?? 'N/A',
            'Client User Agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'N/A',
            'Client Accept Encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? 'N/A',
        ];
        ?>
        
        <!DOCTYPE html>
        <html>
        <head>
            <title>PHP Config Info</title>
            <style>
                body { font-family: Arial, sans-serif; background: #f9f9f9; }
                table { border-collapse: collapse; width: 100%; background: #fff; box-shadow: 0 0 8px rgba(0,0,0,0.1); }
                th, td { padding: 12px 16px; border: 1px solid #ddd; }
                th { background-color: #f2f2f2; text-align: left; }
                tr:hover { background-color: #f5f5f5; }
                caption { font-size: 1.5em; margin-bottom: 1em; font-weight: bold; }
            </style>
        </head>
        <body>
        
        <table>
            <caption>PHP Configuration Info</caption>
            <thead>
                <tr>
                    <th>Configuration</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($config as $key => $value): ?>
                <tr>
                    <td><?= htmlspecialchars($key) ?></td>
                    <td><?= htmlspecialchars($value ?: 'Not set') ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        </body>
        </html>
        <?php
    }
    else {
        echo "You do not have permission to view this page.";
    }
?>
