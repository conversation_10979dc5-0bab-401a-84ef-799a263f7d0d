<?php
require_once __DIR__.'/src/autoload/autoload.php';

use src\database\DatabaseMigratorFactory;

// As the scope in this file is local, any variable required in DatabaseMigratorFactory will need to be declared using global here
// see http://docs.phinx.org/en/latest/configuration.html for more information
global $ServerName, $Database, $UserName, $Password, $Username_Migration, $Password_Migration;

require __DIR__.'/client/DATIXConfig.php';

//If using a remote client folder, the file above will be empty except for a reference to the other folder.
if (isset($ClientFolder) && is_file($ClientFolder.'/DATIXConfig.php'))
{
    require $ClientFolder.'/DATIXConfig.php';
}


//In case the standard db user doesn't have permissions to run migrates, we can override it here
list($UserName, $Password) = (new DatabaseMigratorFactory)->create()->getAccountToUseToAlterSchema();

return [
    'paths' => [
        'migrations' => '%%PHINX_CONFIG_DIR%%/db/migrations',
        'seeds'      => '%%PHINX_CONFIG_DIR%%/db/seeds'
    ],
    'environments' => [
        'default_database' => 'datixweb',
        'default_migration_table' => 'phinxlog',
        'datixweb' => [
            'adapter' => 'sqlsrv',
            'host' => $ServerName,
            'name' => $Database,
            'user' => $UserName,
            'pass' => $Password
        ]
    ]
];