<?php
namespace Aws;

/**
 * Builds AWS clients based on configuration settings.
 *
 * @method \Aws\ACMPCA\ACMPCAClient createACMPCA(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionACMPCA(array $args = [])
 * @method \Aws\ARCZonalShift\ARCZonalShiftClient createARCZonalShift(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionARCZonalShift(array $args = [])
 * @method \Aws\AccessAnalyzer\AccessAnalyzerClient createAccessAnalyzer(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAccessAnalyzer(array $args = [])
 * @method \Aws\Account\AccountClient createAccount(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAccount(array $args = [])
 * @method \Aws\Acm\AcmClient createAcm(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAcm(array $args = [])
 * @method \Aws\Amplify\AmplifyClient createAmplify(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAmplify(array $args = [])
 * @method \Aws\AmplifyBackend\AmplifyBackendClient createAmplifyBackend(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAmplifyBackend(array $args = [])
 * @method \Aws\AmplifyUIBuilder\AmplifyUIBuilderClient createAmplifyUIBuilder(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAmplifyUIBuilder(array $args = [])
 * @method \Aws\ApiGateway\ApiGatewayClient createApiGateway(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApiGateway(array $args = [])
 * @method \Aws\ApiGatewayManagementApi\ApiGatewayManagementApiClient createApiGatewayManagementApi(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApiGatewayManagementApi(array $args = [])
 * @method \Aws\ApiGatewayV2\ApiGatewayV2Client createApiGatewayV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApiGatewayV2(array $args = [])
 * @method \Aws\AppConfig\AppConfigClient createAppConfig(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppConfig(array $args = [])
 * @method \Aws\AppConfigData\AppConfigDataClient createAppConfigData(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppConfigData(array $args = [])
 * @method \Aws\AppFabric\AppFabricClient createAppFabric(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppFabric(array $args = [])
 * @method \Aws\AppIntegrationsService\AppIntegrationsServiceClient createAppIntegrationsService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppIntegrationsService(array $args = [])
 * @method \Aws\AppMesh\AppMeshClient createAppMesh(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppMesh(array $args = [])
 * @method \Aws\AppRegistry\AppRegistryClient createAppRegistry(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppRegistry(array $args = [])
 * @method \Aws\AppRunner\AppRunnerClient createAppRunner(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppRunner(array $args = [])
 * @method \Aws\AppSync\AppSyncClient createAppSync(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppSync(array $args = [])
 * @method \Aws\AppTest\AppTestClient createAppTest(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppTest(array $args = [])
 * @method \Aws\Appflow\AppflowClient createAppflow(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppflow(array $args = [])
 * @method \Aws\ApplicationAutoScaling\ApplicationAutoScalingClient createApplicationAutoScaling(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApplicationAutoScaling(array $args = [])
 * @method \Aws\ApplicationCostProfiler\ApplicationCostProfilerClient createApplicationCostProfiler(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApplicationCostProfiler(array $args = [])
 * @method \Aws\ApplicationDiscoveryService\ApplicationDiscoveryServiceClient createApplicationDiscoveryService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApplicationDiscoveryService(array $args = [])
 * @method \Aws\ApplicationInsights\ApplicationInsightsClient createApplicationInsights(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApplicationInsights(array $args = [])
 * @method \Aws\ApplicationSignals\ApplicationSignalsClient createApplicationSignals(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionApplicationSignals(array $args = [])
 * @method \Aws\Appstream\AppstreamClient createAppstream(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAppstream(array $args = [])
 * @method \Aws\Artifact\ArtifactClient createArtifact(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionArtifact(array $args = [])
 * @method \Aws\Athena\AthenaClient createAthena(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAthena(array $args = [])
 * @method \Aws\AuditManager\AuditManagerClient createAuditManager(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAuditManager(array $args = [])
 * @method \Aws\AugmentedAIRuntime\AugmentedAIRuntimeClient createAugmentedAIRuntime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAugmentedAIRuntime(array $args = [])
 * @method \Aws\AutoScaling\AutoScalingClient createAutoScaling(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAutoScaling(array $args = [])
 * @method \Aws\AutoScalingPlans\AutoScalingPlansClient createAutoScalingPlans(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionAutoScalingPlans(array $args = [])
 * @method \Aws\B2bi\B2biClient createB2bi(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionB2bi(array $args = [])
 * @method \Aws\BCMDataExports\BCMDataExportsClient createBCMDataExports(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBCMDataExports(array $args = [])
 * @method \Aws\Backup\BackupClient createBackup(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBackup(array $args = [])
 * @method \Aws\BackupGateway\BackupGatewayClient createBackupGateway(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBackupGateway(array $args = [])
 * @method \Aws\Batch\BatchClient createBatch(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBatch(array $args = [])
 * @method \Aws\Bedrock\BedrockClient createBedrock(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBedrock(array $args = [])
 * @method \Aws\BedrockAgent\BedrockAgentClient createBedrockAgent(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBedrockAgent(array $args = [])
 * @method \Aws\BedrockAgentRuntime\BedrockAgentRuntimeClient createBedrockAgentRuntime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBedrockAgentRuntime(array $args = [])
 * @method \Aws\BedrockRuntime\BedrockRuntimeClient createBedrockRuntime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBedrockRuntime(array $args = [])
 * @method \Aws\BillingConductor\BillingConductorClient createBillingConductor(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBillingConductor(array $args = [])
 * @method \Aws\Braket\BraketClient createBraket(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBraket(array $args = [])
 * @method \Aws\Budgets\BudgetsClient createBudgets(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionBudgets(array $args = [])
 * @method \Aws\Chatbot\ChatbotClient createChatbot(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionChatbot(array $args = [])
 * @method \Aws\Chime\ChimeClient createChime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionChime(array $args = [])
 * @method \Aws\ChimeSDKIdentity\ChimeSDKIdentityClient createChimeSDKIdentity(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionChimeSDKIdentity(array $args = [])
 * @method \Aws\ChimeSDKMediaPipelines\ChimeSDKMediaPipelinesClient createChimeSDKMediaPipelines(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionChimeSDKMediaPipelines(array $args = [])
 * @method \Aws\ChimeSDKMeetings\ChimeSDKMeetingsClient createChimeSDKMeetings(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionChimeSDKMeetings(array $args = [])
 * @method \Aws\ChimeSDKMessaging\ChimeSDKMessagingClient createChimeSDKMessaging(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionChimeSDKMessaging(array $args = [])
 * @method \Aws\ChimeSDKVoice\ChimeSDKVoiceClient createChimeSDKVoice(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionChimeSDKVoice(array $args = [])
 * @method \Aws\CleanRooms\CleanRoomsClient createCleanRooms(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCleanRooms(array $args = [])
 * @method \Aws\CleanRoomsML\CleanRoomsMLClient createCleanRoomsML(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCleanRoomsML(array $args = [])
 * @method \Aws\Cloud9\Cloud9Client createCloud9(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloud9(array $args = [])
 * @method \Aws\CloudControlApi\CloudControlApiClient createCloudControlApi(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudControlApi(array $args = [])
 * @method \Aws\CloudDirectory\CloudDirectoryClient createCloudDirectory(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudDirectory(array $args = [])
 * @method \Aws\CloudFormation\CloudFormationClient createCloudFormation(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudFormation(array $args = [])
 * @method \Aws\CloudFront\CloudFrontClient createCloudFront(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudFront(array $args = [])
 * @method \Aws\CloudFrontKeyValueStore\CloudFrontKeyValueStoreClient createCloudFrontKeyValueStore(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudFrontKeyValueStore(array $args = [])
 * @method \Aws\CloudHSMV2\CloudHSMV2Client createCloudHSMV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudHSMV2(array $args = [])
 * @method \Aws\CloudHsm\CloudHsmClient createCloudHsm(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudHsm(array $args = [])
 * @method \Aws\CloudSearch\CloudSearchClient createCloudSearch(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudSearch(array $args = [])
 * @method \Aws\CloudSearchDomain\CloudSearchDomainClient createCloudSearchDomain(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudSearchDomain(array $args = [])
 * @method \Aws\CloudTrail\CloudTrailClient createCloudTrail(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudTrail(array $args = [])
 * @method \Aws\CloudTrailData\CloudTrailDataClient createCloudTrailData(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudTrailData(array $args = [])
 * @method \Aws\CloudWatch\CloudWatchClient createCloudWatch(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudWatch(array $args = [])
 * @method \Aws\CloudWatchEvents\CloudWatchEventsClient createCloudWatchEvents(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudWatchEvents(array $args = [])
 * @method \Aws\CloudWatchEvidently\CloudWatchEvidentlyClient createCloudWatchEvidently(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudWatchEvidently(array $args = [])
 * @method \Aws\CloudWatchLogs\CloudWatchLogsClient createCloudWatchLogs(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudWatchLogs(array $args = [])
 * @method \Aws\CloudWatchRUM\CloudWatchRUMClient createCloudWatchRUM(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCloudWatchRUM(array $args = [])
 * @method \Aws\CodeArtifact\CodeArtifactClient createCodeArtifact(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeArtifact(array $args = [])
 * @method \Aws\CodeBuild\CodeBuildClient createCodeBuild(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeBuild(array $args = [])
 * @method \Aws\CodeCatalyst\CodeCatalystClient createCodeCatalyst(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeCatalyst(array $args = [])
 * @method \Aws\CodeCommit\CodeCommitClient createCodeCommit(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeCommit(array $args = [])
 * @method \Aws\CodeConnections\CodeConnectionsClient createCodeConnections(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeConnections(array $args = [])
 * @method \Aws\CodeDeploy\CodeDeployClient createCodeDeploy(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeDeploy(array $args = [])
 * @method \Aws\CodeGuruProfiler\CodeGuruProfilerClient createCodeGuruProfiler(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeGuruProfiler(array $args = [])
 * @method \Aws\CodeGuruReviewer\CodeGuruReviewerClient createCodeGuruReviewer(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeGuruReviewer(array $args = [])
 * @method \Aws\CodeGuruSecurity\CodeGuruSecurityClient createCodeGuruSecurity(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeGuruSecurity(array $args = [])
 * @method \Aws\CodePipeline\CodePipelineClient createCodePipeline(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodePipeline(array $args = [])
 * @method \Aws\CodeStarNotifications\CodeStarNotificationsClient createCodeStarNotifications(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeStarNotifications(array $args = [])
 * @method \Aws\CodeStarconnections\CodeStarconnectionsClient createCodeStarconnections(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCodeStarconnections(array $args = [])
 * @method \Aws\CognitoIdentity\CognitoIdentityClient createCognitoIdentity(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCognitoIdentity(array $args = [])
 * @method \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient createCognitoIdentityProvider(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCognitoIdentityProvider(array $args = [])
 * @method \Aws\CognitoSync\CognitoSyncClient createCognitoSync(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCognitoSync(array $args = [])
 * @method \Aws\Comprehend\ComprehendClient createComprehend(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionComprehend(array $args = [])
 * @method \Aws\ComprehendMedical\ComprehendMedicalClient createComprehendMedical(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionComprehendMedical(array $args = [])
 * @method \Aws\ComputeOptimizer\ComputeOptimizerClient createComputeOptimizer(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionComputeOptimizer(array $args = [])
 * @method \Aws\ConfigService\ConfigServiceClient createConfigService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionConfigService(array $args = [])
 * @method \Aws\Connect\ConnectClient createConnect(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionConnect(array $args = [])
 * @method \Aws\ConnectCampaignService\ConnectCampaignServiceClient createConnectCampaignService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionConnectCampaignService(array $args = [])
 * @method \Aws\ConnectCases\ConnectCasesClient createConnectCases(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionConnectCases(array $args = [])
 * @method \Aws\ConnectContactLens\ConnectContactLensClient createConnectContactLens(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionConnectContactLens(array $args = [])
 * @method \Aws\ConnectParticipant\ConnectParticipantClient createConnectParticipant(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionConnectParticipant(array $args = [])
 * @method \Aws\ConnectWisdomService\ConnectWisdomServiceClient createConnectWisdomService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionConnectWisdomService(array $args = [])
 * @method \Aws\ControlCatalog\ControlCatalogClient createControlCatalog(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionControlCatalog(array $args = [])
 * @method \Aws\ControlTower\ControlTowerClient createControlTower(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionControlTower(array $args = [])
 * @method \Aws\CostExplorer\CostExplorerClient createCostExplorer(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCostExplorer(array $args = [])
 * @method \Aws\CostOptimizationHub\CostOptimizationHubClient createCostOptimizationHub(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCostOptimizationHub(array $args = [])
 * @method \Aws\CostandUsageReportService\CostandUsageReportServiceClient createCostandUsageReportService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCostandUsageReportService(array $args = [])
 * @method \Aws\CustomerProfiles\CustomerProfilesClient createCustomerProfiles(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionCustomerProfiles(array $args = [])
 * @method \Aws\DAX\DAXClient createDAX(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDAX(array $args = [])
 * @method \Aws\DLM\DLMClient createDLM(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDLM(array $args = [])
 * @method \Aws\DataExchange\DataExchangeClient createDataExchange(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDataExchange(array $args = [])
 * @method \Aws\DataPipeline\DataPipelineClient createDataPipeline(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDataPipeline(array $args = [])
 * @method \Aws\DataSync\DataSyncClient createDataSync(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDataSync(array $args = [])
 * @method \Aws\DataZone\DataZoneClient createDataZone(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDataZone(array $args = [])
 * @method \Aws\DatabaseMigrationService\DatabaseMigrationServiceClient createDatabaseMigrationService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDatabaseMigrationService(array $args = [])
 * @method \Aws\Deadline\DeadlineClient createDeadline(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDeadline(array $args = [])
 * @method \Aws\Detective\DetectiveClient createDetective(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDetective(array $args = [])
 * @method \Aws\DevOpsGuru\DevOpsGuruClient createDevOpsGuru(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDevOpsGuru(array $args = [])
 * @method \Aws\DeviceFarm\DeviceFarmClient createDeviceFarm(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDeviceFarm(array $args = [])
 * @method \Aws\DirectConnect\DirectConnectClient createDirectConnect(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDirectConnect(array $args = [])
 * @method \Aws\DirectoryService\DirectoryServiceClient createDirectoryService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDirectoryService(array $args = [])
 * @method \Aws\DocDB\DocDBClient createDocDB(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDocDB(array $args = [])
 * @method \Aws\DocDBElastic\DocDBElasticClient createDocDBElastic(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDocDBElastic(array $args = [])
 * @method \Aws\DynamoDb\DynamoDbClient createDynamoDb(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDynamoDb(array $args = [])
 * @method \Aws\DynamoDbStreams\DynamoDbStreamsClient createDynamoDbStreams(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionDynamoDbStreams(array $args = [])
 * @method \Aws\EBS\EBSClient createEBS(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEBS(array $args = [])
 * @method \Aws\EC2InstanceConnect\EC2InstanceConnectClient createEC2InstanceConnect(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEC2InstanceConnect(array $args = [])
 * @method \Aws\ECRPublic\ECRPublicClient createECRPublic(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionECRPublic(array $args = [])
 * @method \Aws\EKS\EKSClient createEKS(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEKS(array $args = [])
 * @method \Aws\EKSAuth\EKSAuthClient createEKSAuth(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEKSAuth(array $args = [])
 * @method \Aws\EMRContainers\EMRContainersClient createEMRContainers(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEMRContainers(array $args = [])
 * @method \Aws\EMRServerless\EMRServerlessClient createEMRServerless(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEMRServerless(array $args = [])
 * @method \Aws\Ec2\Ec2Client createEc2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEc2(array $args = [])
 * @method \Aws\Ecr\EcrClient createEcr(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEcr(array $args = [])
 * @method \Aws\Ecs\EcsClient createEcs(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEcs(array $args = [])
 * @method \Aws\Efs\EfsClient createEfs(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEfs(array $args = [])
 * @method \Aws\ElastiCache\ElastiCacheClient createElastiCache(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionElastiCache(array $args = [])
 * @method \Aws\ElasticBeanstalk\ElasticBeanstalkClient createElasticBeanstalk(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionElasticBeanstalk(array $args = [])
 * @method \Aws\ElasticInference\ElasticInferenceClient createElasticInference(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionElasticInference(array $args = [])
 * @method \Aws\ElasticLoadBalancing\ElasticLoadBalancingClient createElasticLoadBalancing(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionElasticLoadBalancing(array $args = [])
 * @method \Aws\ElasticLoadBalancingV2\ElasticLoadBalancingV2Client createElasticLoadBalancingV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionElasticLoadBalancingV2(array $args = [])
 * @method \Aws\ElasticTranscoder\ElasticTranscoderClient createElasticTranscoder(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionElasticTranscoder(array $args = [])
 * @method \Aws\ElasticsearchService\ElasticsearchServiceClient createElasticsearchService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionElasticsearchService(array $args = [])
 * @method \Aws\Emr\EmrClient createEmr(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEmr(array $args = [])
 * @method \Aws\EntityResolution\EntityResolutionClient createEntityResolution(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEntityResolution(array $args = [])
 * @method \Aws\EventBridge\EventBridgeClient createEventBridge(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionEventBridge(array $args = [])
 * @method \Aws\FIS\FISClient createFIS(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionFIS(array $args = [])
 * @method \Aws\FMS\FMSClient createFMS(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionFMS(array $args = [])
 * @method \Aws\FSx\FSxClient createFSx(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionFSx(array $args = [])
 * @method \Aws\FinSpaceData\FinSpaceDataClient createFinSpaceData(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionFinSpaceData(array $args = [])
 * @method \Aws\Firehose\FirehoseClient createFirehose(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionFirehose(array $args = [])
 * @method \Aws\ForecastQueryService\ForecastQueryServiceClient createForecastQueryService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionForecastQueryService(array $args = [])
 * @method \Aws\ForecastService\ForecastServiceClient createForecastService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionForecastService(array $args = [])
 * @method \Aws\FraudDetector\FraudDetectorClient createFraudDetector(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionFraudDetector(array $args = [])
 * @method \Aws\FreeTier\FreeTierClient createFreeTier(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionFreeTier(array $args = [])
 * @method \Aws\GameLift\GameLiftClient createGameLift(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGameLift(array $args = [])
 * @method \Aws\Glacier\GlacierClient createGlacier(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGlacier(array $args = [])
 * @method \Aws\GlobalAccelerator\GlobalAcceleratorClient createGlobalAccelerator(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGlobalAccelerator(array $args = [])
 * @method \Aws\Glue\GlueClient createGlue(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGlue(array $args = [])
 * @method \Aws\GlueDataBrew\GlueDataBrewClient createGlueDataBrew(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGlueDataBrew(array $args = [])
 * @method \Aws\Greengrass\GreengrassClient createGreengrass(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGreengrass(array $args = [])
 * @method \Aws\GreengrassV2\GreengrassV2Client createGreengrassV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGreengrassV2(array $args = [])
 * @method \Aws\GroundStation\GroundStationClient createGroundStation(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGroundStation(array $args = [])
 * @method \Aws\GuardDuty\GuardDutyClient createGuardDuty(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionGuardDuty(array $args = [])
 * @method \Aws\Health\HealthClient createHealth(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionHealth(array $args = [])
 * @method \Aws\HealthLake\HealthLakeClient createHealthLake(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionHealthLake(array $args = [])
 * @method \Aws\IVS\IVSClient createIVS(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIVS(array $args = [])
 * @method \Aws\IVSRealTime\IVSRealTimeClient createIVSRealTime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIVSRealTime(array $args = [])
 * @method \Aws\Iam\IamClient createIam(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIam(array $args = [])
 * @method \Aws\IdentityStore\IdentityStoreClient createIdentityStore(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIdentityStore(array $args = [])
 * @method \Aws\ImportExport\ImportExportClient createImportExport(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionImportExport(array $args = [])
 * @method \Aws\Inspector\InspectorClient createInspector(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionInspector(array $args = [])
 * @method \Aws\Inspector2\Inspector2Client createInspector2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionInspector2(array $args = [])
 * @method \Aws\InspectorScan\InspectorScanClient createInspectorScan(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionInspectorScan(array $args = [])
 * @method \Aws\InternetMonitor\InternetMonitorClient createInternetMonitor(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionInternetMonitor(array $args = [])
 * @method \Aws\IoT1ClickDevicesService\IoT1ClickDevicesServiceClient createIoT1ClickDevicesService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoT1ClickDevicesService(array $args = [])
 * @method \Aws\IoT1ClickProjects\IoT1ClickProjectsClient createIoT1ClickProjects(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoT1ClickProjects(array $args = [])
 * @method \Aws\IoTAnalytics\IoTAnalyticsClient createIoTAnalytics(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTAnalytics(array $args = [])
 * @method \Aws\IoTDeviceAdvisor\IoTDeviceAdvisorClient createIoTDeviceAdvisor(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTDeviceAdvisor(array $args = [])
 * @method \Aws\IoTEvents\IoTEventsClient createIoTEvents(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTEvents(array $args = [])
 * @method \Aws\IoTEventsData\IoTEventsDataClient createIoTEventsData(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTEventsData(array $args = [])
 * @method \Aws\IoTFleetHub\IoTFleetHubClient createIoTFleetHub(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTFleetHub(array $args = [])
 * @method \Aws\IoTFleetWise\IoTFleetWiseClient createIoTFleetWise(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTFleetWise(array $args = [])
 * @method \Aws\IoTJobsDataPlane\IoTJobsDataPlaneClient createIoTJobsDataPlane(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTJobsDataPlane(array $args = [])
 * @method \Aws\IoTSecureTunneling\IoTSecureTunnelingClient createIoTSecureTunneling(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTSecureTunneling(array $args = [])
 * @method \Aws\IoTSiteWise\IoTSiteWiseClient createIoTSiteWise(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTSiteWise(array $args = [])
 * @method \Aws\IoTThingsGraph\IoTThingsGraphClient createIoTThingsGraph(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTThingsGraph(array $args = [])
 * @method \Aws\IoTTwinMaker\IoTTwinMakerClient createIoTTwinMaker(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTTwinMaker(array $args = [])
 * @method \Aws\IoTWireless\IoTWirelessClient createIoTWireless(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIoTWireless(array $args = [])
 * @method \Aws\Iot\IotClient createIot(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIot(array $args = [])
 * @method \Aws\IotDataPlane\IotDataPlaneClient createIotDataPlane(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionIotDataPlane(array $args = [])
 * @method \Aws\Kafka\KafkaClient createKafka(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKafka(array $args = [])
 * @method \Aws\KafkaConnect\KafkaConnectClient createKafkaConnect(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKafkaConnect(array $args = [])
 * @method \Aws\KendraRanking\KendraRankingClient createKendraRanking(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKendraRanking(array $args = [])
 * @method \Aws\Keyspaces\KeyspacesClient createKeyspaces(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKeyspaces(array $args = [])
 * @method \Aws\Kinesis\KinesisClient createKinesis(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesis(array $args = [])
 * @method \Aws\KinesisAnalytics\KinesisAnalyticsClient createKinesisAnalytics(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesisAnalytics(array $args = [])
 * @method \Aws\KinesisAnalyticsV2\KinesisAnalyticsV2Client createKinesisAnalyticsV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesisAnalyticsV2(array $args = [])
 * @method \Aws\KinesisVideo\KinesisVideoClient createKinesisVideo(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesisVideo(array $args = [])
 * @method \Aws\KinesisVideoArchivedMedia\KinesisVideoArchivedMediaClient createKinesisVideoArchivedMedia(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesisVideoArchivedMedia(array $args = [])
 * @method \Aws\KinesisVideoMedia\KinesisVideoMediaClient createKinesisVideoMedia(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesisVideoMedia(array $args = [])
 * @method \Aws\KinesisVideoSignalingChannels\KinesisVideoSignalingChannelsClient createKinesisVideoSignalingChannels(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesisVideoSignalingChannels(array $args = [])
 * @method \Aws\KinesisVideoWebRTCStorage\KinesisVideoWebRTCStorageClient createKinesisVideoWebRTCStorage(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKinesisVideoWebRTCStorage(array $args = [])
 * @method \Aws\Kms\KmsClient createKms(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionKms(array $args = [])
 * @method \Aws\LakeFormation\LakeFormationClient createLakeFormation(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLakeFormation(array $args = [])
 * @method \Aws\Lambda\LambdaClient createLambda(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLambda(array $args = [])
 * @method \Aws\LaunchWizard\LaunchWizardClient createLaunchWizard(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLaunchWizard(array $args = [])
 * @method \Aws\LexModelBuildingService\LexModelBuildingServiceClient createLexModelBuildingService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLexModelBuildingService(array $args = [])
 * @method \Aws\LexModelsV2\LexModelsV2Client createLexModelsV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLexModelsV2(array $args = [])
 * @method \Aws\LexRuntimeService\LexRuntimeServiceClient createLexRuntimeService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLexRuntimeService(array $args = [])
 * @method \Aws\LexRuntimeV2\LexRuntimeV2Client createLexRuntimeV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLexRuntimeV2(array $args = [])
 * @method \Aws\LicenseManager\LicenseManagerClient createLicenseManager(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLicenseManager(array $args = [])
 * @method \Aws\LicenseManagerLinuxSubscriptions\LicenseManagerLinuxSubscriptionsClient createLicenseManagerLinuxSubscriptions(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLicenseManagerLinuxSubscriptions(array $args = [])
 * @method \Aws\LicenseManagerUserSubscriptions\LicenseManagerUserSubscriptionsClient createLicenseManagerUserSubscriptions(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLicenseManagerUserSubscriptions(array $args = [])
 * @method \Aws\Lightsail\LightsailClient createLightsail(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLightsail(array $args = [])
 * @method \Aws\LocationService\LocationServiceClient createLocationService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLocationService(array $args = [])
 * @method \Aws\LookoutEquipment\LookoutEquipmentClient createLookoutEquipment(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLookoutEquipment(array $args = [])
 * @method \Aws\LookoutMetrics\LookoutMetricsClient createLookoutMetrics(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLookoutMetrics(array $args = [])
 * @method \Aws\LookoutforVision\LookoutforVisionClient createLookoutforVision(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionLookoutforVision(array $args = [])
 * @method \Aws\MQ\MQClient createMQ(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMQ(array $args = [])
 * @method \Aws\MTurk\MTurkClient createMTurk(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMTurk(array $args = [])
 * @method \Aws\MWAA\MWAAClient createMWAA(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMWAA(array $args = [])
 * @method \Aws\MachineLearning\MachineLearningClient createMachineLearning(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMachineLearning(array $args = [])
 * @method \Aws\Macie2\Macie2Client createMacie2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMacie2(array $args = [])
 * @method \Aws\MailManager\MailManagerClient createMailManager(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMailManager(array $args = [])
 * @method \Aws\MainframeModernization\MainframeModernizationClient createMainframeModernization(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMainframeModernization(array $args = [])
 * @method \Aws\ManagedBlockchain\ManagedBlockchainClient createManagedBlockchain(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionManagedBlockchain(array $args = [])
 * @method \Aws\ManagedBlockchainQuery\ManagedBlockchainQueryClient createManagedBlockchainQuery(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionManagedBlockchainQuery(array $args = [])
 * @method \Aws\ManagedGrafana\ManagedGrafanaClient createManagedGrafana(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionManagedGrafana(array $args = [])
 * @method \Aws\MarketplaceAgreement\MarketplaceAgreementClient createMarketplaceAgreement(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMarketplaceAgreement(array $args = [])
 * @method \Aws\MarketplaceCatalog\MarketplaceCatalogClient createMarketplaceCatalog(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMarketplaceCatalog(array $args = [])
 * @method \Aws\MarketplaceCommerceAnalytics\MarketplaceCommerceAnalyticsClient createMarketplaceCommerceAnalytics(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMarketplaceCommerceAnalytics(array $args = [])
 * @method \Aws\MarketplaceDeployment\MarketplaceDeploymentClient createMarketplaceDeployment(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMarketplaceDeployment(array $args = [])
 * @method \Aws\MarketplaceEntitlementService\MarketplaceEntitlementServiceClient createMarketplaceEntitlementService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMarketplaceEntitlementService(array $args = [])
 * @method \Aws\MarketplaceMetering\MarketplaceMeteringClient createMarketplaceMetering(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMarketplaceMetering(array $args = [])
 * @method \Aws\MediaConnect\MediaConnectClient createMediaConnect(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaConnect(array $args = [])
 * @method \Aws\MediaConvert\MediaConvertClient createMediaConvert(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaConvert(array $args = [])
 * @method \Aws\MediaLive\MediaLiveClient createMediaLive(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaLive(array $args = [])
 * @method \Aws\MediaPackage\MediaPackageClient createMediaPackage(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaPackage(array $args = [])
 * @method \Aws\MediaPackageV2\MediaPackageV2Client createMediaPackageV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaPackageV2(array $args = [])
 * @method \Aws\MediaPackageVod\MediaPackageVodClient createMediaPackageVod(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaPackageVod(array $args = [])
 * @method \Aws\MediaStore\MediaStoreClient createMediaStore(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaStore(array $args = [])
 * @method \Aws\MediaStoreData\MediaStoreDataClient createMediaStoreData(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaStoreData(array $args = [])
 * @method \Aws\MediaTailor\MediaTailorClient createMediaTailor(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMediaTailor(array $args = [])
 * @method \Aws\MedicalImaging\MedicalImagingClient createMedicalImaging(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMedicalImaging(array $args = [])
 * @method \Aws\MemoryDB\MemoryDBClient createMemoryDB(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMemoryDB(array $args = [])
 * @method \Aws\MigrationHub\MigrationHubClient createMigrationHub(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMigrationHub(array $args = [])
 * @method \Aws\MigrationHubConfig\MigrationHubConfigClient createMigrationHubConfig(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMigrationHubConfig(array $args = [])
 * @method \Aws\MigrationHubOrchestrator\MigrationHubOrchestratorClient createMigrationHubOrchestrator(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMigrationHubOrchestrator(array $args = [])
 * @method \Aws\MigrationHubRefactorSpaces\MigrationHubRefactorSpacesClient createMigrationHubRefactorSpaces(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMigrationHubRefactorSpaces(array $args = [])
 * @method \Aws\MigrationHubStrategyRecommendations\MigrationHubStrategyRecommendationsClient createMigrationHubStrategyRecommendations(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionMigrationHubStrategyRecommendations(array $args = [])
 * @method \Aws\Neptune\NeptuneClient createNeptune(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionNeptune(array $args = [])
 * @method \Aws\NeptuneGraph\NeptuneGraphClient createNeptuneGraph(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionNeptuneGraph(array $args = [])
 * @method \Aws\Neptunedata\NeptunedataClient createNeptunedata(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionNeptunedata(array $args = [])
 * @method \Aws\NetworkFirewall\NetworkFirewallClient createNetworkFirewall(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionNetworkFirewall(array $args = [])
 * @method \Aws\NetworkManager\NetworkManagerClient createNetworkManager(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionNetworkManager(array $args = [])
 * @method \Aws\NetworkMonitor\NetworkMonitorClient createNetworkMonitor(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionNetworkMonitor(array $args = [])
 * @method \Aws\NimbleStudio\NimbleStudioClient createNimbleStudio(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionNimbleStudio(array $args = [])
 * @method \Aws\OAM\OAMClient createOAM(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOAM(array $args = [])
 * @method \Aws\OSIS\OSISClient createOSIS(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOSIS(array $args = [])
 * @method \Aws\Omics\OmicsClient createOmics(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOmics(array $args = [])
 * @method \Aws\OpenSearchServerless\OpenSearchServerlessClient createOpenSearchServerless(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOpenSearchServerless(array $args = [])
 * @method \Aws\OpenSearchService\OpenSearchServiceClient createOpenSearchService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOpenSearchService(array $args = [])
 * @method \Aws\OpsWorks\OpsWorksClient createOpsWorks(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOpsWorks(array $args = [])
 * @method \Aws\OpsWorksCM\OpsWorksCMClient createOpsWorksCM(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOpsWorksCM(array $args = [])
 * @method \Aws\Organizations\OrganizationsClient createOrganizations(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOrganizations(array $args = [])
 * @method \Aws\Outposts\OutpostsClient createOutposts(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionOutposts(array $args = [])
 * @method \Aws\PI\PIClient createPI(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPI(array $args = [])
 * @method \Aws\Panorama\PanoramaClient createPanorama(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPanorama(array $args = [])
 * @method \Aws\PaymentCryptography\PaymentCryptographyClient createPaymentCryptography(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPaymentCryptography(array $args = [])
 * @method \Aws\PaymentCryptographyData\PaymentCryptographyDataClient createPaymentCryptographyData(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPaymentCryptographyData(array $args = [])
 * @method \Aws\PcaConnectorAd\PcaConnectorAdClient createPcaConnectorAd(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPcaConnectorAd(array $args = [])
 * @method \Aws\PcaConnectorScep\PcaConnectorScepClient createPcaConnectorScep(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPcaConnectorScep(array $args = [])
 * @method \Aws\Personalize\PersonalizeClient createPersonalize(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPersonalize(array $args = [])
 * @method \Aws\PersonalizeEvents\PersonalizeEventsClient createPersonalizeEvents(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPersonalizeEvents(array $args = [])
 * @method \Aws\PersonalizeRuntime\PersonalizeRuntimeClient createPersonalizeRuntime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPersonalizeRuntime(array $args = [])
 * @method \Aws\Pinpoint\PinpointClient createPinpoint(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPinpoint(array $args = [])
 * @method \Aws\PinpointEmail\PinpointEmailClient createPinpointEmail(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPinpointEmail(array $args = [])
 * @method \Aws\PinpointSMSVoice\PinpointSMSVoiceClient createPinpointSMSVoice(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPinpointSMSVoice(array $args = [])
 * @method \Aws\PinpointSMSVoiceV2\PinpointSMSVoiceV2Client createPinpointSMSVoiceV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPinpointSMSVoiceV2(array $args = [])
 * @method \Aws\Pipes\PipesClient createPipes(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPipes(array $args = [])
 * @method \Aws\Polly\PollyClient createPolly(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPolly(array $args = [])
 * @method \Aws\Pricing\PricingClient createPricing(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPricing(array $args = [])
 * @method \Aws\PrivateNetworks\PrivateNetworksClient createPrivateNetworks(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPrivateNetworks(array $args = [])
 * @method \Aws\PrometheusService\PrometheusServiceClient createPrometheusService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionPrometheusService(array $args = [])
 * @method \Aws\Proton\ProtonClient createProton(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionProton(array $args = [])
 * @method \Aws\QApps\QAppsClient createQApps(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionQApps(array $args = [])
 * @method \Aws\QBusiness\QBusinessClient createQBusiness(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionQBusiness(array $args = [])
 * @method \Aws\QConnect\QConnectClient createQConnect(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionQConnect(array $args = [])
 * @method \Aws\QLDB\QLDBClient createQLDB(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionQLDB(array $args = [])
 * @method \Aws\QLDBSession\QLDBSessionClient createQLDBSession(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionQLDBSession(array $args = [])
 * @method \Aws\QuickSight\QuickSightClient createQuickSight(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionQuickSight(array $args = [])
 * @method \Aws\RAM\RAMClient createRAM(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRAM(array $args = [])
 * @method \Aws\RDSDataService\RDSDataServiceClient createRDSDataService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRDSDataService(array $args = [])
 * @method \Aws\Rds\RdsClient createRds(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRds(array $args = [])
 * @method \Aws\RecycleBin\RecycleBinClient createRecycleBin(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRecycleBin(array $args = [])
 * @method \Aws\Redshift\RedshiftClient createRedshift(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRedshift(array $args = [])
 * @method \Aws\RedshiftDataAPIService\RedshiftDataAPIServiceClient createRedshiftDataAPIService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRedshiftDataAPIService(array $args = [])
 * @method \Aws\RedshiftServerless\RedshiftServerlessClient createRedshiftServerless(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRedshiftServerless(array $args = [])
 * @method \Aws\Rekognition\RekognitionClient createRekognition(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRekognition(array $args = [])
 * @method \Aws\Repostspace\RepostspaceClient createRepostspace(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRepostspace(array $args = [])
 * @method \Aws\ResilienceHub\ResilienceHubClient createResilienceHub(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionResilienceHub(array $args = [])
 * @method \Aws\ResourceExplorer2\ResourceExplorer2Client createResourceExplorer2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionResourceExplorer2(array $args = [])
 * @method \Aws\ResourceGroups\ResourceGroupsClient createResourceGroups(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionResourceGroups(array $args = [])
 * @method \Aws\ResourceGroupsTaggingAPI\ResourceGroupsTaggingAPIClient createResourceGroupsTaggingAPI(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionResourceGroupsTaggingAPI(array $args = [])
 * @method \Aws\RoboMaker\RoboMakerClient createRoboMaker(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoboMaker(array $args = [])
 * @method \Aws\RolesAnywhere\RolesAnywhereClient createRolesAnywhere(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRolesAnywhere(array $args = [])
 * @method \Aws\Route53\Route53Client createRoute53(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoute53(array $args = [])
 * @method \Aws\Route53Domains\Route53DomainsClient createRoute53Domains(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoute53Domains(array $args = [])
 * @method \Aws\Route53Profiles\Route53ProfilesClient createRoute53Profiles(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoute53Profiles(array $args = [])
 * @method \Aws\Route53RecoveryCluster\Route53RecoveryClusterClient createRoute53RecoveryCluster(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoute53RecoveryCluster(array $args = [])
 * @method \Aws\Route53RecoveryControlConfig\Route53RecoveryControlConfigClient createRoute53RecoveryControlConfig(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoute53RecoveryControlConfig(array $args = [])
 * @method \Aws\Route53RecoveryReadiness\Route53RecoveryReadinessClient createRoute53RecoveryReadiness(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoute53RecoveryReadiness(array $args = [])
 * @method \Aws\Route53Resolver\Route53ResolverClient createRoute53Resolver(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionRoute53Resolver(array $args = [])
 * @method \Aws\S3\S3Client createS3(array $args = [])
 * @method \Aws\S3\S3MultiRegionClient createMultiRegionS3(array $args = [])
 * @method \Aws\S3Control\S3ControlClient createS3Control(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionS3Control(array $args = [])
 * @method \Aws\S3Outposts\S3OutpostsClient createS3Outposts(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionS3Outposts(array $args = [])
 * @method \Aws\SSMContacts\SSMContactsClient createSSMContacts(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSSMContacts(array $args = [])
 * @method \Aws\SSMIncidents\SSMIncidentsClient createSSMIncidents(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSSMIncidents(array $args = [])
 * @method \Aws\SSMQuickSetup\SSMQuickSetupClient createSSMQuickSetup(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSSMQuickSetup(array $args = [])
 * @method \Aws\SSO\SSOClient createSSO(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSSO(array $args = [])
 * @method \Aws\SSOAdmin\SSOAdminClient createSSOAdmin(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSSOAdmin(array $args = [])
 * @method \Aws\SSOOIDC\SSOOIDCClient createSSOOIDC(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSSOOIDC(array $args = [])
 * @method \Aws\SageMaker\SageMakerClient createSageMaker(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSageMaker(array $args = [])
 * @method \Aws\SageMakerFeatureStoreRuntime\SageMakerFeatureStoreRuntimeClient createSageMakerFeatureStoreRuntime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSageMakerFeatureStoreRuntime(array $args = [])
 * @method \Aws\SageMakerGeospatial\SageMakerGeospatialClient createSageMakerGeospatial(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSageMakerGeospatial(array $args = [])
 * @method \Aws\SageMakerMetrics\SageMakerMetricsClient createSageMakerMetrics(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSageMakerMetrics(array $args = [])
 * @method \Aws\SageMakerRuntime\SageMakerRuntimeClient createSageMakerRuntime(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSageMakerRuntime(array $args = [])
 * @method \Aws\SagemakerEdgeManager\SagemakerEdgeManagerClient createSagemakerEdgeManager(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSagemakerEdgeManager(array $args = [])
 * @method \Aws\SavingsPlans\SavingsPlansClient createSavingsPlans(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSavingsPlans(array $args = [])
 * @method \Aws\Scheduler\SchedulerClient createScheduler(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionScheduler(array $args = [])
 * @method \Aws\Schemas\SchemasClient createSchemas(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSchemas(array $args = [])
 * @method \Aws\SecretsManager\SecretsManagerClient createSecretsManager(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSecretsManager(array $args = [])
 * @method \Aws\SecurityHub\SecurityHubClient createSecurityHub(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSecurityHub(array $args = [])
 * @method \Aws\SecurityLake\SecurityLakeClient createSecurityLake(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSecurityLake(array $args = [])
 * @method \Aws\ServerlessApplicationRepository\ServerlessApplicationRepositoryClient createServerlessApplicationRepository(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionServerlessApplicationRepository(array $args = [])
 * @method \Aws\ServiceCatalog\ServiceCatalogClient createServiceCatalog(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionServiceCatalog(array $args = [])
 * @method \Aws\ServiceDiscovery\ServiceDiscoveryClient createServiceDiscovery(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionServiceDiscovery(array $args = [])
 * @method \Aws\ServiceQuotas\ServiceQuotasClient createServiceQuotas(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionServiceQuotas(array $args = [])
 * @method \Aws\Ses\SesClient createSes(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSes(array $args = [])
 * @method \Aws\SesV2\SesV2Client createSesV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSesV2(array $args = [])
 * @method \Aws\Sfn\SfnClient createSfn(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSfn(array $args = [])
 * @method \Aws\Shield\ShieldClient createShield(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionShield(array $args = [])
 * @method \Aws\SimSpaceWeaver\SimSpaceWeaverClient createSimSpaceWeaver(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSimSpaceWeaver(array $args = [])
 * @method \Aws\Sms\SmsClient createSms(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSms(array $args = [])
 * @method \Aws\SnowBall\SnowBallClient createSnowBall(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSnowBall(array $args = [])
 * @method \Aws\SnowDeviceManagement\SnowDeviceManagementClient createSnowDeviceManagement(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSnowDeviceManagement(array $args = [])
 * @method \Aws\Sns\SnsClient createSns(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSns(array $args = [])
 * @method \Aws\Sqs\SqsClient createSqs(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSqs(array $args = [])
 * @method \Aws\Ssm\SsmClient createSsm(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSsm(array $args = [])
 * @method \Aws\SsmSap\SsmSapClient createSsmSap(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSsmSap(array $args = [])
 * @method \Aws\StorageGateway\StorageGatewayClient createStorageGateway(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionStorageGateway(array $args = [])
 * @method \Aws\Sts\StsClient createSts(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSts(array $args = [])
 * @method \Aws\SupplyChain\SupplyChainClient createSupplyChain(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSupplyChain(array $args = [])
 * @method \Aws\Support\SupportClient createSupport(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSupport(array $args = [])
 * @method \Aws\SupportApp\SupportAppClient createSupportApp(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSupportApp(array $args = [])
 * @method \Aws\Swf\SwfClient createSwf(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSwf(array $args = [])
 * @method \Aws\Synthetics\SyntheticsClient createSynthetics(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionSynthetics(array $args = [])
 * @method \Aws\TaxSettings\TaxSettingsClient createTaxSettings(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTaxSettings(array $args = [])
 * @method \Aws\Textract\TextractClient createTextract(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTextract(array $args = [])
 * @method \Aws\TimestreamInfluxDB\TimestreamInfluxDBClient createTimestreamInfluxDB(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTimestreamInfluxDB(array $args = [])
 * @method \Aws\TimestreamQuery\TimestreamQueryClient createTimestreamQuery(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTimestreamQuery(array $args = [])
 * @method \Aws\TimestreamWrite\TimestreamWriteClient createTimestreamWrite(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTimestreamWrite(array $args = [])
 * @method \Aws\Tnb\TnbClient createTnb(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTnb(array $args = [])
 * @method \Aws\TranscribeService\TranscribeServiceClient createTranscribeService(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTranscribeService(array $args = [])
 * @method \Aws\Transfer\TransferClient createTransfer(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTransfer(array $args = [])
 * @method \Aws\Translate\TranslateClient createTranslate(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTranslate(array $args = [])
 * @method \Aws\TrustedAdvisor\TrustedAdvisorClient createTrustedAdvisor(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionTrustedAdvisor(array $args = [])
 * @method \Aws\VPCLattice\VPCLatticeClient createVPCLattice(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionVPCLattice(array $args = [])
 * @method \Aws\VerifiedPermissions\VerifiedPermissionsClient createVerifiedPermissions(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionVerifiedPermissions(array $args = [])
 * @method \Aws\VoiceID\VoiceIDClient createVoiceID(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionVoiceID(array $args = [])
 * @method \Aws\WAFV2\WAFV2Client createWAFV2(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWAFV2(array $args = [])
 * @method \Aws\Waf\WafClient createWaf(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWaf(array $args = [])
 * @method \Aws\WafRegional\WafRegionalClient createWafRegional(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWafRegional(array $args = [])
 * @method \Aws\WellArchitected\WellArchitectedClient createWellArchitected(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWellArchitected(array $args = [])
 * @method \Aws\WorkDocs\WorkDocsClient createWorkDocs(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWorkDocs(array $args = [])
 * @method \Aws\WorkLink\WorkLinkClient createWorkLink(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWorkLink(array $args = [])
 * @method \Aws\WorkMail\WorkMailClient createWorkMail(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWorkMail(array $args = [])
 * @method \Aws\WorkMailMessageFlow\WorkMailMessageFlowClient createWorkMailMessageFlow(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWorkMailMessageFlow(array $args = [])
 * @method \Aws\WorkSpaces\WorkSpacesClient createWorkSpaces(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWorkSpaces(array $args = [])
 * @method \Aws\WorkSpacesThinClient\WorkSpacesThinClientClient createWorkSpacesThinClient(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWorkSpacesThinClient(array $args = [])
 * @method \Aws\WorkSpacesWeb\WorkSpacesWebClient createWorkSpacesWeb(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionWorkSpacesWeb(array $args = [])
 * @method \Aws\XRay\XRayClient createXRay(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionXRay(array $args = [])
 * @method \Aws\drs\drsClient createdrs(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegiondrs(array $args = [])
 * @method \Aws\finspace\finspaceClient createfinspace(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionfinspace(array $args = [])
 * @method \Aws\imagebuilder\imagebuilderClient createimagebuilder(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionimagebuilder(array $args = [])
 * @method \Aws\ivschat\ivschatClient createivschat(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionivschat(array $args = [])
 * @method \Aws\kendra\kendraClient createkendra(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionkendra(array $args = [])
 * @method \Aws\mgn\mgnClient createmgn(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionmgn(array $args = [])
 * @method \Aws\signer\signerClient createsigner(array $args = [])
 * @method \Aws\MultiRegionClient createMultiRegionsigner(array $args = [])
 */
class Sdk
{
    const VERSION = '3.320.5';

    /** @var array Arguments for creating clients */
    private $args;

    /**
     * Constructs a new SDK object with an associative array of default
     * client settings.
     *
     * @param array $args
     *
     * @throws \InvalidArgumentException
     * @see Aws\AwsClient::__construct for a list of available options.
     */
    public function __construct(array $args = [])
    {
        $this->args = $args;

        if (!isset($args['handler']) && !isset($args['http_handler'])) {
            $this->args['http_handler'] = default_http_handler();
        }
    }

    public function __call($name, array $args)
    {
        $args = isset($args[0]) ? $args[0] : [];
        if (strpos($name, 'createMultiRegion') === 0) {
            return $this->createMultiRegionClient(substr($name, 17), $args);
        }

        if (strpos($name, 'create') === 0) {
            return $this->createClient(substr($name, 6), $args);
        }

        throw new \BadMethodCallException("Unknown method: {$name}.");
    }

    /**
     * Get a client by name using an array of constructor options.
     *
     * @param string $name Service name or namespace (e.g., DynamoDb, s3).
     * @param array  $args Arguments to configure the client.
     *
     * @return AwsClientInterface
     * @throws \InvalidArgumentException if any required options are missing or
     *                                   the service is not supported.
     * @see Aws\AwsClient::__construct for a list of available options for args.
     */
    public function createClient($name, array $args = [])
    {
        // Get information about the service from the manifest file.
        $service = manifest($name);
        $namespace = $service['namespace'];

        // Instantiate the client class.
        $client = "Aws\\{$namespace}\\{$namespace}Client";
        return new $client($this->mergeArgs($namespace, $service, $args));
    }

    public function createMultiRegionClient($name, array $args = [])
    {
        // Get information about the service from the manifest file.
        $service = manifest($name);
        $namespace = $service['namespace'];

        $klass = "Aws\\{$namespace}\\{$namespace}MultiRegionClient";
        $klass = class_exists($klass) ? $klass : MultiRegionClient::class;

        return new $klass($this->mergeArgs($namespace, $service, $args));
    }

    /**
     * Clone existing SDK instance with ability to pass an associative array
     * of extra client settings.
     *
     * @param array $args
     *
     * @return self
     */
    public function copy(array $args = [])
    {
        return new self($args + $this->args);
    }

    private function mergeArgs($namespace, array $manifest, array $args = [])
    {
        // Merge provided args with stored, service-specific args.
        if (isset($this->args[$namespace])) {
            $args += $this->args[$namespace];
        }

        // Provide the endpoint prefix in the args.
        if (!isset($args['service'])) {
            $args['service'] = $manifest['endpoint'];
        }

        return $args + $this->args;
    }

    /**
     * Determine the endpoint prefix from a client namespace.
     *
     * @param string $name Namespace name
     *
     * @return string
     * @internal
     * @deprecated Use the `\Aws\manifest()` function instead.
     */
    public static function getEndpointPrefix($name)
    {
        return manifest($name)['endpoint'];
    }
}
