{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "2b2ca7f188fb3be096b5f26d02459076", "packages": [{"name": "auth0/auth0-php", "version": "8.11.1", "source": {"type": "git", "url": "https://github.com/auth0/auth0-PHP.git", "reference": "5d132ad4b3b95c5d5d342d09088d469568bfa627"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/auth0/auth0-PHP/zipball/5d132ad4b3b95c5d5d342d09088d469568bfa627", "reference": "5d132ad4b3b95c5d5d342d09088d469568bfa627", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "php": "^8.1", "php-http/multipart-stream-builder": "^1", "psr-discovery/all": "^1", "psr/http-client-implementation": "^1", "psr/http-factory-implementation": "^1", "psr/http-message-implementation": "^1"}, "require-dev": {"ergebnis/composer-normalize": "^2", "friendsofphp/php-cs-fixer": "^3", "mockery/mockery": "^1", "pestphp/pest": "^2", "phpstan/phpstan": "^1", "phpstan/phpstan-strict-rules": "^1", "psr-mock/http": "^1", "rector/rector": "0.17.6", "spatie/ray": "^1", "symfony/cache": "^4 || ^5 || ^6", "symfony/event-dispatcher": "^4 || ^5 || ^6", "vimeo/psalm": "^5", "wikimedia/composer-merge-plugin": "^2"}, "suggest": {"psr/cache-implementation": "(PSR-6 Cache) Improve performance by avoiding making redundant network requests.", "psr/event-dispatcher-implementation": "(PSR-14 Event Dispatcher) Observe and react to events when they occur."}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"Auth0\\SDK\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Auth0", "email": "<EMAIL>", "homepage": "https://auth0.com/"}], "description": "PHP SDK for Auth0 Authentication and Management APIs.", "homepage": "https://github.com/auth0/auth0-PHP", "keywords": ["Authentication", "JSON Web Token", "JWK", "OpenId", "api", "auth", "auth0", "authorization", "json web key", "jwt", "login", "o<PERSON>h", "protect", "secure"], "support": {"issues": "https://github.com/auth0/auth0-PHP/issues", "source": "https://github.com/auth0/auth0-PHP/tree/8.11.1"}, "time": "2024-01-11T15:28:10+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.2.6", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "a63485b65b6b3367039306496d49737cf1995408"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/a63485b65b6b3367039306496d49737cf1995408", "reference": "a63485b65b6b3367039306496d49737cf1995408", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.6"}, "time": "2024-06-13T17:21:28+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.320.5", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "afda5aefd59da90208d2f59427ce81e91535b1f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/afda5aefd59da90208d2f59427ce81e91535b1f2", "reference": "afda5aefd59da90208d2f59427ce81e91535b1f2", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}, "exclude-from-classmap": ["src/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.320.5"}, "time": "2024-08-21T18:14:31+00:00"}, {"name": "composer/semver", "version": "3.4.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "c51258e759afdb17f1fd1fe83bc12baaef6309d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/c51258e759afdb17f1fd1fe83bc12baaef6309d6", "reference": "c51258e759afdb17f1fd1fe83bc12baaef6309d6", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-07-12T11:35:52+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-24T11:22:20+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-07-18T10:29:17+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2024-07-18T11:15:46+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.3", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "f5753e4b90daffe50c902e99df5ce3c58fca3fee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/f5753e4b90daffe50c902e99df5ce3c58fca3fee", "reference": "f5753e4b90daffe50c902e99df5ce3c58fca3fee", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://ghita.org", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/devel"}, "time": "2014-07-10T20:00:25+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/bbb69a935c2cbb0c03d7f481a238027430f6440b", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.7.0"}, "time": "2023-08-25T10:54:48+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/php-amqplib/php-amqplib.git", "reference": "adc2a425ab214517a5e76557a9370bdc6f212576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-amqplib/php-amqplib/zipball/adc2a425ab214517a5e76557a9370bdc6f212576", "reference": "adc2a425ab214517a5e76557a9370bdc6f212576", "shasum": ""}, "require": {"ext-bcmath": "*", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"PhpAmqpLib": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/videlalvaro/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "support": {"issues": "https://github.com/php-amqplib/php-amqplib/issues", "source": "https://github.com/php-amqplib/php-amqplib/tree/v2.1.0"}, "time": "2013-06-17T19:25:27+00:00"}, {"name": "php-http/discovery", "version": "1.19.4", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "0700efda8d7526335132360167315fdab3aeb599"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/0700efda8d7526335132360167315fdab3aeb599", "reference": "0700efda8d7526335132360167315fdab3aeb599", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.4"}, "time": "2024-03-29T13:00:05+00:00"}, {"name": "php-http/multipart-stream-builder", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/multipart-stream-builder.git", "reference": "ed56da23b95949ae4747378bed8a5b61a2fdae24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/multipart-stream-builder/zipball/ed56da23b95949ae4747378bed8a5b61a2fdae24", "reference": "ed56da23b95949ae4747378bed8a5b61a2fdae24", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/discovery": "^1.15", "psr/http-factory-implementation": "^1.0"}, "require-dev": {"nyholm/psr7": "^1.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.0.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Message\\MultipartStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A builder class that help you create a multipart stream", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "multipart stream", "stream"], "support": {"issues": "https://github.com/php-http/multipart-stream-builder/issues", "source": "https://github.com/php-http/multipart-stream-builder/tree/1.3.1"}, "time": "2024-06-10T14:51:55+00:00"}, {"name": "psr-discovery/all", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/psr-discovery/all.git", "reference": "e353ca0cac46b2e954f4a3ee3a13f0de8be7b87b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/all/zipball/e353ca0cac46b2e954f4a3ee3a13f0de8be7b87b", "reference": "e353ca0cac46b2e954f4a3ee3a13f0de8be7b87b", "shasum": ""}, "require": {"php": "^8.1", "psr-discovery/cache-implementations": "^1.0", "psr-discovery/container-implementations": "^1.0", "psr-discovery/event-dispatcher-implementations": "^1.0", "psr-discovery/http-client-implementations": "^1.0", "psr-discovery/http-factory-implementations": "^1.0", "psr-discovery/log-implementations": "^1.0"}, "type": "metapackage", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery", "keywords": ["PSR-11", "discovery", "psr", "psr-14", "psr-17", "psr-18", "psr-3", "psr-6"], "support": {"source": "https://github.com/psr-discovery/all/tree/1.0.1"}, "time": "2024-03-04T21:20:17+00:00"}, {"name": "psr-discovery/cache-implementations", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/psr-discovery/cache-implementations.git", "reference": "ebede0af34a7fd3c5564809e659ee69c0ab85ff6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/cache-implementations/zipball/ebede0af34a7fd3c5564809e659ee69c0ab85ff6", "reference": "ebede0af34a7fd3c5564809e659ee69c0ab85ff6", "shasum": ""}, "require": {"php": "^8.1", "psr-discovery/discovery": "^1.0", "psr/cache": "^1.0 | ^2.0 | ^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR-6 Cache implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery", "keywords": ["cache", "cache-implementation", "discovery", "psr", "psr-6"], "support": {"issues": "https://github.com/psr-discovery/cache-implementations/issues", "source": "https://github.com/psr-discovery/cache-implementations/tree/1.1.1"}, "time": "2024-03-04T21:22:36+00:00"}, {"name": "psr-discovery/container-implementations", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/psr-discovery/container-implementations.git", "reference": "728a452b32b0bb60c4bac43b18db2e3105bb8d7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/container-implementations/zipball/728a452b32b0bb60c4bac43b18db2e3105bb8d7e", "reference": "728a452b32b0bb60c4bac43b18db2e3105bb8d7e", "shasum": ""}, "require": {"php": "^8.1", "psr-discovery/discovery": "^1.0", "psr/container": "^1.0 | ^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR-11 Container implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery/http-client-implementations", "keywords": ["PSR-11", "discovery", "psr"], "support": {"issues": "https://github.com/psr-discovery/container-implementations/issues", "source": "https://github.com/psr-discovery/container-implementations/tree/1.1.1"}, "time": "2024-03-04T21:24:05+00:00"}, {"name": "psr-discovery/discovery", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/psr-discovery/discovery.git", "reference": "f94a41c150efaffd6f4c23ef95e31cae7a83704f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/discovery/zipball/f94a41c150efaffd6f4c23ef95e31cae7a83704f", "reference": "f94a41c150efaffd6f4c23ef95e31cae7a83704f", "shasum": ""}, "require": {"composer/semver": "^3.0", "php": "^8.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR implementations by searching for a list of well-known classes that implement the relevant interfaces, and returning an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery/discovery", "keywords": ["PSR-11", "discovery", "psr", "psr-14", "psr-17", "psr-18", "psr-3", "psr-6"], "support": {"issues": "https://github.com/psr-discovery/discovery/issues", "source": "https://github.com/psr-discovery/discovery/tree/1.1.2"}, "time": "2024-08-09T07:04:30+00:00"}, {"name": "psr-discovery/event-dispatcher-implementations", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/psr-discovery/event-dispatcher-implementations.git", "reference": "9033bb984613703e4c4f795ef0657184dc1c70eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/event-dispatcher-implementations/zipball/9033bb984613703e4c4f795ef0657184dc1c70eb", "reference": "9033bb984613703e4c4f795ef0657184dc1c70eb", "shasum": ""}, "require": {"php": "^8.1", "psr-discovery/discovery": "^1.0", "psr/event-dispatcher": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR-14 Event Dispatcher implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery/http-client-implementations", "keywords": ["discovery", "psr", "psr-18"], "support": {"issues": "https://github.com/psr-discovery/event-dispatcher-implementations/issues", "source": "https://github.com/psr-discovery/event-dispatcher-implementations/tree/1.1.1"}, "time": "2024-03-04T21:27:10+00:00"}, {"name": "psr-discovery/http-client-implementations", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/psr-discovery/http-client-implementations.git", "reference": "a05c54087d13504d8e48c27395fbab638fb0a114"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/http-client-implementations/zipball/a05c54087d13504d8e48c27395fbab638fb0a114", "reference": "a05c54087d13504d8e48c27395fbab638fb0a114", "shasum": ""}, "require": {"php": "^8.1", "psr-discovery/discovery": "^1.0", "psr/http-client": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR-18 HTTP Client implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery/http-client-implementations", "keywords": ["discovery", "psr", "psr-18"], "support": {"issues": "https://github.com/psr-discovery/http-client-implementations/issues", "source": "https://github.com/psr-discovery/http-client-implementations/tree/1.2.0"}, "time": "2024-03-16T05:29:47+00:00"}, {"name": "psr-discovery/http-factory-implementations", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/psr-discovery/http-factory-implementations.git", "reference": "4ee07ae795b794e61578db32b5422a780b01b833"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/http-factory-implementations/zipball/4ee07ae795b794e61578db32b5422a780b01b833", "reference": "4ee07ae795b794e61578db32b5422a780b01b833", "shasum": ""}, "require": {"php": "^8.1", "psr-discovery/discovery": "^1.1", "psr/http-factory": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR-17 HTTP Factory implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery/http-factory-implementations", "keywords": ["discovery", "psr", "psr-18"], "support": {"issues": "https://github.com/psr-discovery/http-factory-implementations/issues", "source": "https://github.com/psr-discovery/http-factory-implementations/tree/1.1.1"}, "time": "2024-03-04T21:31:16+00:00"}, {"name": "psr-discovery/log-implementations", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/psr-discovery/log-implementations.git", "reference": "384894384663fa5e1b2186112fb8ffe3f81a0b22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psr-discovery/log-implementations/zipball/384894384663fa5e1b2186112fb8ffe3f81a0b22", "reference": "384894384663fa5e1b2186112fb8ffe3f81a0b22", "shasum": ""}, "require": {"php": "^8.1", "psr-discovery/discovery": "^1.0", "psr/log": "^1.0 | ^2.0 | ^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "pestphp/pest": "^2.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "rector/rector": "^0.15", "vimeo/psalm": "^5.8", "wikimedia/composer-merge-plugin": "^2.0"}, "type": "library", "extra": {"merge-plugin": {"ignore-duplicates": false, "include": ["composer.local.json"], "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false, "recurse": true, "replace": true}}, "autoload": {"psr-4": {"PsrDiscovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://evansims.com/"}], "description": "Lightweight library that discovers available PSR-3 Log implementations by searching for a list of well-known classes that implement the relevant interface, and returns an instance of the first one that is found.", "homepage": "https://github.com/psr-discovery", "keywords": ["discovery", "log", "log-implementation", "psr", "psr-3"], "support": {"issues": "https://github.com/psr-discovery/log-implementations/issues", "source": "https://github.com/psr-discovery/log-implementations/tree/1.0.1"}, "time": "2024-03-04T21:32:27+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "79dff0b268932c640297f5208d6298f71855c03e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/79dff0b268932c640297f5208d6298f71855c03e", "reference": "79dff0b268932c640297f5208d6298f71855c03e", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.1"}, "time": "2024-08-21T13:31:24+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "robmorgan/phinx", "version": "v0.6.6", "source": {"type": "git", "url": "https://github.com/cakephp/phinx.git", "reference": "cc97b79f62c2180caba0be1d3744a335a296a678"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/phinx/zipball/cc97b79f62c2180caba0be1d3744a335a296a678", "reference": "cc97b79f62c2180caba0be1d3744a335a296a678", "shasum": ""}, "require": {"php": ">=5.4", "symfony/config": "~2.8|~3.0", "symfony/console": "~2.8|~3.0", "symfony/yaml": "~2.8|~3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.26|^5.0"}, "bin": ["bin/phinx"], "type": "library", "autoload": {"psr-4": {"Phinx\\": "src/Phinx"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://shadowhand.me", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://robmorgan.id.au", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Phinx makes it ridiculously easy to manage the database migrations for your PHP app.", "homepage": "https://phinx.org", "keywords": ["database", "database migrations", "db", "migrations", "phinx"], "support": {"issues": "https://github.com/cakephp/phinx/issues", "source": "https://github.com/cakephp/phinx/tree/v0.6.6"}, "time": "2017-01-23T08:53:20+00:00"}, {"name": "steampixel/simple-php-router", "version": "0.7.1", "source": {"type": "git", "url": "https://github.com/steampixel/simplePHPRouter.git", "reference": "c996d75b34c2c6da45d4dc737f61c0ee8fed5d2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/steampixel/simplePHPRouter/zipball/c996d75b34c2c6da45d4dc737f61c0ee8fed5d2b", "reference": "c996d75b34c2c6da45d4dc737f61c0ee8fed5d2b", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Steampixel": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Steampixel", "email": "<EMAIL>", "homepage": "https://steampixel.de/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxbits.net/"}], "description": "This is a simple and small PHP router that can handle the whole url routing for your project.", "support": {"issues": "https://github.com/steampixel/simplePHPRouter/issues", "source": "https://github.com/steampixel/simplePHPRouter/tree/0.7.1"}, "time": "2022-10-12T18:04:20+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v5.4.12", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "181b89f18a90f8925ef805f950d47a7190e9b950"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/181b89f18a90f8925ef805f950d47a7190e9b950", "reference": "181b89f18a90f8925ef805f950d47a7190e9b950", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v5.4.12"}, "abandoned": "symfony/mailer", "time": "2018-07-31T09:26:32+00:00"}, {"name": "symfony/config", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "bc6b3fd3930d4b53a60b42fe2ed6fc466b75f03f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/bc6b3fd3930d4b53a60b42fe2ed6fc466b75f03f", "reference": "bc6b3fd3930d4b53a60b42fe2ed6fc466b75f03f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/dependency-injection": "<3.3", "symfony/finder": "<3.3"}, "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/event-dispatcher": "~3.3|~4.0", "symfony/finder": "~3.3|~4.0", "symfony/yaml": "~3.0|~4.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/console", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a10b1da6fc93080c180bba7219b5ff5b7518fe81", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/debug", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "1a692492190773c5310bc7877cb590c04c2f05be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/1a692492190773c5310bc7877cb590c04c2f05be", "reference": "1a692492190773c5310bc7877cb590c04c2f05be", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "symfony/error-handler", "time": "2022-07-28T16:29:46+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.42", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "815412ee8971209bd4c1eecd5f4f481eacd44bf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/815412ee8971209bd4c1eecd5f4f481eacd44bf5", "reference": "815412ee8971209bd4c1eecd5f4f481eacd44bf5", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v4.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-20T08:49:14+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/yaml", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "88289caa3c166321883f67fe5130188ebbb47094"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/88289caa3c166321883f67fe5130188ebbb47094", "reference": "88289caa3c166321883f67fe5130188ebbb47094", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.1.0"}